import{v as b,K as L,d as C,u as M,c as r,o as i,g as s,i as k,b as n,I as G,t as N,_ as S,r as $,k as f,l as I,f as B,L as E,w as A,j as T}from"./index-BE8XLQ59.js";import{E as z}from"./index-B27ddgxZ.js";import{G as y}from"./GridCard-D-EgbodX.js";function ae(e){return b(L.HOME_CONTENT,e)}const H={class:"section-header animate__animated animate__fadeInUp"},K={class:"section-title"},V={class:"title-left"},x=C({__name:"SectionHeader",props:{title:{type:String,required:!0},icon:{type:String,default:"apps-o"},moreLink:{type:String,default:""},showMore:{type:Boolean,default:!0}},emits:["more-click"],setup(e,{emit:l}){const d=e,t=l,u=M(),g=()=>{d.moreLink?u.push(d.moreLink):t("more-click")};return(p,_)=>{const m=G;return i(),r("div",H,[s("div",K,[s("div",V,[n(m,{name:e.icon},null,8,["name"]),s("span",null,N(e.title),1)]),e.showMore?(i(),r("div",{key:0,class:"title-right",onClick:g},[_[0]||(_[0]=s("span",null,"更多",-1)),n(m,{name:"arrow"})])):k("",!0)])])}}}),O=S(x,[["__scopeId","data-v-8cd00828"]]);function R(e,l){return{id:e.id,title:e.name,image:e.thumbnail,badge:l,originalData:e}}function D(e,l){return e.map(d=>R(d,l))}function U(e){return{culture:"CultureDetail",knowledge:"TcmKnowledgeDetail",case:"CasesDetail"}[e]||"KnowledgeDetail"}function w(e){return{culture:"文化",knowledge:"知识",case:"案例"}[e]||"精选"}const j={class:"section-container featured-articles-section"},q={key:0,class:"loading-container"},W={key:2,class:"articles-container"},F={key:0,class:"category-section"},J={class:"category-title"},P={key:1,class:"category-section"},Q={class:"category-title"},X={key:2,class:"category-section"},Y={class:"category-title"},Z=C({__name:"ArticleGridSection",props:{cultureData:{},knowledgeData:{},caseData:{},title:{default:"精品文章"},icon:{default:"description-o"},showMore:{type:Boolean,default:!1}},setup(e){const l=M(),d=$(!1),t=e,u=f(()=>D(t.cultureData,w("culture"))),g=f(()=>D(t.knowledgeData,w("knowledge"))),p=f(()=>D(t.caseData,w("case"))),_=f(()=>u.value.length>0||g.value.length>0||p.value.length>0),m=a=>{console.log("点击了文章:",a),a.originalData;let o="";t.cultureData.some(c=>c.id===a.id)?o="culture":t.knowledgeData.some(c=>c.id===a.id)?o="knowledge":t.caseData.some(c=>c.id===a.id)&&(o="case");const v=U(o);l.push({name:v,params:{id:a.id}})};return I(()=>{console.log(`${t.title} - cultureData:`,t.cultureData),console.log(`${t.title} - knowledgeData:`,t.knowledgeData),console.log(`${t.title} - caseData:`,t.caseData),console.log(`${t.title} - 转换后的GridCard数据:`),console.log("- culture:",u.value),console.log("- knowledge:",g.value),console.log("- case:",p.value)}),(a,o)=>{const v=E,c=z,h=G;return i(),r("div",j,[n(O,{title:a.title,icon:a.icon,showMore:a.showMore},null,8,["title","icon","showMore"]),d.value?(i(),r("div",q,[n(v,{size:"24px"},{default:A(()=>o[0]||(o[0]=[T("加载中...")])),_:1,__:[0]})])):_.value?(i(),r("div",W,[u.value.length>0?(i(),r("div",F,[s("div",J,[n(h,{name:"gem-o",color:"#7c3aed",size:"16"}),o[1]||(o[1]=s("span",null,"中医文化",-1))]),n(y,{items:u.value,onCardClick:m},null,8,["items"])])):k("",!0),g.value.length>0?(i(),r("div",P,[s("div",Q,[n(h,{name:"book-o",color:"#4b8bf4",size:"16"}),o[2]||(o[2]=s("span",null,"中医知识",-1))]),n(y,{items:g.value,onCardClick:m},null,8,["items"])])):k("",!0),p.value.length>0?(i(),r("div",X,[s("div",Y,[n(h,{name:"records",color:"#059669",size:"16"}),o[3]||(o[3]=s("span",null,"中医案例",-1))]),n(y,{items:p.value,onCardClick:m},null,8,["items"])])):k("",!0)])):(i(),B(c,{key:1,description:`暂无${a.title}`},null,8,["description"]))])}}}),ne=S(Z,[["__scopeId","data-v-9ecd1f0b"]]);export{ne as A,O as S,ae as g};
