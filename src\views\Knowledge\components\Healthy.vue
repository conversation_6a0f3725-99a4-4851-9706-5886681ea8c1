<template>
  <div class="section-container">
    <div class="section-header">
      <div class="section-title">
        <van-icon name="heart-o" color="#ff6b35" size="18" />
        <span>养生专区</span>
      </div>
    </div>

    <div class="healthy-articles">
      <!-- 中医文化养生 -->
      <div class="knowledge-card" v-for="item in cultureHealthyData" :key="'culture-' + item.id"
        @click="onCultureClick(item)">
        <div class="card-image-wrapper">
          <img :src="item.thumbnail" :alt="item.name" class="card-image" />
        </div>
        <div class="card-content">
          <h3 class="card-title">{{ item.name }}</h3>
          <p class="card-summary">{{ item.desc }}</p>
          <div class="card-meta">
            <div class="meta-left">
              <span class="author">{{ item.creater }}</span>
            </div>
            <div class="meta-right">
              <span class="date">
                <van-icon name="clock-o" size="12" />
                {{ item.create_time }}
              </span>
            </div>
          </div>
        </div>
      </div>

      <!-- 中医知识养生 -->
      <div class="knowledge-card" v-for="item in knowledgeHealthyData" :key="'knowledge-' + item.id"
        @click="onKnowledgeClick(item)">
        <div class="card-image-wrapper">
          <img :src="item.thumbnail" :alt="item.name" class="card-image" />
        </div>
        <div class="card-content">
          <h3 class="card-title">{{ item.name }}</h3>
          <p class="card-summary">{{ item.desc }}</p>
          <div class="card-meta">
            <div class="meta-left">
              <span class="author">{{ item.creater }}</span>
            </div>
            <div class="meta-right">
              <span class="date">
                <van-icon name="clock-o" size="12" />
                {{ item.create_time }}
              </span>
            </div>
          </div>
        </div>
      </div>

      <!-- 中医案例养生 -->
      <div class="knowledge-card" v-for="item in caseHealthyData" :key="'case-' + item.id"
        @click="onCaseClick(item)">
        <div class="card-image-wrapper">
          <img :src="item.thumbnail" :alt="item.name" class="card-image" />
        </div>
        <div class="card-content">
          <h3 class="card-title">{{ item.name }}</h3>
          <p class="card-summary">{{ item.desc }}</p>
          <div class="card-meta">
            <div class="meta-left">
              <span class="author">{{ item.creater }}</span>
            </div>
            <div class="meta-right">
              <span class="date">
                <van-icon name="clock-o" size="12" />
                {{ item.create_time }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';

const router = useRouter();

const props = defineProps<{
  cultureHealthyData: Array<{
    id: number
    name: string
    thumbnail: string
    desc: string
    create_time: string
    creater:string
  }>
  knowledgeHealthyData: Array<{
    id: number
    name: string
    thumbnail: string
    desc: string
    create_time: string
    creater:string
  }>
  caseHealthyData: Array<{
    id: number
    name: string
    thumbnail: string
    desc: string
    create_time: string
    creater:string
  }>
}>()
// 文章点击事件
const onCultureClick = (item: any) => {
  // 跳转到文章详情页
  router.push({ name: 'CultureDetail', params: { id: item.id } });
};

const onKnowledgeClick = (item: any) => {
  // 跳转到文章详情页
  router.push({ name: 'TcmKnowledgeDetail', params: { id: item.id } });
};
const onCaseClick = (item: any) => {
  // 跳转到文章详情页
  router.push({ name: 'CasesDetail', params: { id: item.id } });
};
// 组件挂载时打印数据
onMounted(() => {
})
</script>

<style scoped>
.healthy-articles {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 16px;
  margin-top: 16px;
}

.knowledge-card {
  background: #fff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  cursor: pointer;
  border: 1px solid rgba(255, 107, 53, 0.1);
}

.knowledge-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(255, 107, 53, 0.15);
  border-color: rgba(255, 107, 53, 0.2);
}

.card-image-wrapper {
  position: relative;
  height: 160px;
  overflow: hidden;
}

.card-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.knowledge-card:hover .card-image {
  transform: scale(1.05);
}

.card-content {
  padding: 16px;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0 0 8px 0;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-break: break-word;
}

.card-summary {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
  margin: 0 0 12px 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-break: break-word;
}

.card-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: #999;
}

.meta-left {
  display: flex;
  align-items: center;
}

.meta-right {
  display: flex;
  align-items: center;
  gap: 4px;
}

.author {
  color: #ff6b35;
  font-weight: 500;
}

.date {
  display: flex;
  align-items: center;
  gap: 4px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .healthy-articles {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 12px;
  }
}

@media (max-width: 480px) {
  .healthy-articles {
    grid-template-columns: 1fr;
    gap: 12px;
  }
  
  .card-image-wrapper {
    height: 140px;
  }
  
  .card-content {
    padding: 12px;
  }
  
  .card-title {
    font-size: 15px;
  }
  
  .card-summary {
    font-size: 13px;
  }
}
</style>
