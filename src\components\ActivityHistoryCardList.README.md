# ActivityHistoryCardList 活动历史卡片列表组件

## 组件概述

`ActivityHistoryCardList` 是一个专门用于展示活动历史记录的列表组件。该组件采用卡片式布局，每个卡片包含活动的序号、时间、图片和详细信息，符合现代移动端设计规范。

## 功能特性

- ✅ **序号显示**: 每个活动卡片都有编号，方便用户识别和排序
- ✅ **时间展示**: 突出显示活动时间信息
- ✅ **图片预览**: 支持活动图片展示，带有悬停效果
- ✅ **状态标签**: 显示活动状态（已结束、进行中、未开始等）
- ✅ **详细信息**: 包含活动标题、描述和元信息
- ✅ **无限滚动**: 支持分页加载和无限滚动
- ✅ **响应式设计**: 适配不同屏幕尺寸
- ✅ **加载状态**: 支持加载和空状态展示
- ✅ **点击交互**: 支持卡片点击事件

## 安装使用

### 基础用法

```vue
<template>
  <ActivityHistoryCardList
    :items="activityItems"
    :loading="loading"
    :finished="finished"
    @card-click="handleCardClick"
    @load-more="loadMore"
  />
</template>

<script setup>
import ActivityHistoryCardList from "@/components/ActivityHistoryCardList.vue";

const activityItems = ref([
  {
    id: 1,
    name: "中医养生健康讲座",
    desc: "本次讲座将介绍中医养生的基本理念和实用方法",
    thumbnail: "https://example.com/image.jpg",
    startdate: "2024-01-15",
    enddate: "2024-01-15",
    location: "中医院大礼堂",
    participants: 120,
    cat_display: "健康讲座",
    status: "已结束",
  },
]);

const loading = ref(false);
const finished = ref(false);

const handleCardClick = (item) => {
  console.log("点击了活动:", item);
  // 跳转到详情页
};

const loadMore = () => {
  // 加载更多数据
};
</script>
```

## Props 属性

| 属性名              | 类型      | 默认值           | 说明             |
| ------------------- | --------- | ---------------- | ---------------- |
| `items`             | `Array`   | `[]`             | 活动数据列表     |
| `loading`           | `Boolean` | `false`          | 是否正在加载     |
| `finished`          | `Boolean` | `false`          | 是否已加载完成   |
| `emptyText`         | `String`  | `'暂无活动历史'` | 空状态提示文本   |
| `useInfiniteScroll` | `Boolean` | `true`           | 是否启用无限滚动 |

## Events 事件

| 事件名       | 参数           | 说明                   |
| ------------ | -------------- | ---------------------- |
| `card-click` | `item: Object` | 点击卡片时触发         |
| `load-more`  | -              | 需要加载更多数据时触发 |

## 数据格式

### 活动项目数据结构

```typescript
interface ActivityItem {
  id: string | number; // 活动ID
  name?: string; // 活动名称
  title?: string; // 活动标题（备选）
  desc?: string; // 活动描述
  description?: string; // 活动描述（备选）
  summary?: string; // 活动摘要（备选）
  thumbnail?: string; // 活动图片
  image?: string; // 活动图片（备选）
  startdate?: string; // 开始日期
  enddate?: string; // 结束日期
  start_time?: string; // 开始时间（备选）
  end_time?: string; // 结束时间（备选）
  activity_date?: string; // 活动日期（备选）
  create_time?: string; // 创建时间（备选）
  location?: string; // 活动地点
  address?: string; // 活动地址（备选）
  participants?: number; // 参与人数
  current_participants?: number; // 当前参与人数（备选）
  cat_display?: string; // 分类显示名称
  category?: string; // 分类（备选）
  status?: string; // 活动状态
}
```

## 样式定制

### CSS 变量

组件使用了以下 CSS 变量，可以通过覆盖这些变量来定制样式：

```css
:root {
  --activity-primary-color: #10ac84;
  --activity-card-radius: 12px;
  --activity-card-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  --activity-card-hover-shadow: 0 8px 24px rgba(16, 172, 132, 0.12);
}
```

### 自定义样式

```css
/* 自定义卡片样式 */
.activity-history-card-list .history-card {
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 自定义序号样式 */
.activity-history-card-list .number-circle {
  background: linear-gradient(135deg, #667eea, #764ba2);
}

/* 自定义时间区域样式 */
.activity-history-card-list .activity-time {
  background: rgba(102, 126, 234, 0.05);
  border-left-color: #667eea;
}
```

## 使用场景

### 1. 活动历史页面

```vue
<template>
  <div class="activity-history-page">
    <van-nav-bar title="活动历史" left-arrow @click-left="goBack" />
    <ActivityHistoryCardList
      :items="historyItems"
      :loading="loading"
      :finished="finished"
      @card-click="viewActivityDetail"
      @load-more="loadMoreHistory"
    />
  </div>
</template>
```

### 2. 用户参与活动记录

```vue
<template>
  <div class="user-activities">
    <ActivityHistoryCardList
      :items="userActivities"
      :loading="loading"
      :finished="finished"
      :useInfiniteScroll="false"
      emptyText="您还没有参与过任何活动"
      @card-click="viewActivity"
    />
  </div>
</template>
```

### 3. 机构活动档案

```vue
<template>
  <div class="organization-activities">
    <ActivityHistoryCardList
      :items="orgActivities"
      :loading="loading"
      :finished="finished"
      @card-click="editActivity"
      @load-more="loadOrgActivities"
    />
  </div>
</template>
```

## 最佳实践

### 1. 数据处理

```javascript
// 推荐的数据预处理
const processActivityData = (rawData) => {
  return rawData.map((item) => ({
    ...item,
    // 确保必要字段存在
    name: item.name || item.title || "未命名活动",
    desc: item.desc || item.description || item.summary || "暂无描述",
    thumbnail: item.thumbnail || item.image || "/default-activity.jpg",
  }));
};
```

### 2. 错误处理

```javascript
// 推荐的错误处理方式
const loadActivities = async () => {
  try {
    loading.value = true;
    const response = await api.getActivityHistory();
    items.value = processActivityData(response.data);
  } catch (error) {
    console.error("加载活动历史失败:", error);
    // 显示错误提示
  } finally {
    loading.value = false;
  }
};
```

### 3. 性能优化

```javascript
// 使用防抖优化搜索
import { debounce } from "lodash-es";

const searchActivities = debounce(async (keyword) => {
  // 搜索逻辑
}, 300);

// 虚拟滚动（大量数据时）
const virtualScrollConfig = {
  itemHeight: 200,
  buffer: 5,
};
```

## 兼容性

- Vue 3.0+
- Vant 4.0+
- 现代浏览器（Chrome 60+, Firefox 60+, Safari 12+）
- 移动端浏览器

## 更新日志

### v1.0.0

- 初始版本发布
- 支持基础的活动历史展示功能
- 支持无限滚动和点击交互
- 响应式设计和动画效果

## 贡献指南

如果您发现问题或有改进建议，请：

1. 在项目中创建 Issue
2. 提交 Pull Request
3. 遵循项目的编码规范

## 许可证

MIT License
