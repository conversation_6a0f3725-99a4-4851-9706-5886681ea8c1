import { get, requestWithRetry } from '../../api/request';
import { MEDICINE_URLS, buildUrl } from '../../api/urls';

/**
 * 获取领导分类
 * @returns 领导分类数据
 */
export function getMedicineCategories() {
  return get<MedicineCategoriesResponse>(MEDICINE_URLS.CATEGORIES);
}

/**
 * 根据分类获取领导列表
 * @param cat 分类ID
 * @param page 页码
 * @param pageSize 每页数量
 * @returns 领导列表数据
 */
export function getMedicineByCategory(cat: string, page: number = 1, pageSize: number = 10) {
  return get<MedicineListResponse>(MEDICINE_URLS.MEDICINE, {
    cat,
    page,
    page_size: pageSize
  });
}

/**
 * 带重试功能的获取领导分类
 * 在网络不稳定情况下使用
 * @returns 领导分类数据
 */

export function getMedicineCategoriesWithRetry(cat: string, page: number = 1, pageSize: number = 10) {
  return requestWithRetry<MedicineListResponse>(() => getMedicineByCategory(
    cat,
    page,
    pageSize
  ));
}

// 领导分类接口
export interface MedicineCategory {
  value: string;
  label: string;
}

// 领导分类响应接口
export interface MedicineCategoriesResponse {
  categories: MedicineCategory[];
}

// 领导信息接口
export interface MedicineItem {
  id: number;
  name: string;
  title: string;
  job: string;
  cat_display: string;
  desc: string;
  thumbnail: string;
  category: string;
  tags?: string[];
}

// 领导列表响应接口
export interface MedicineListResponse {
  results: MedicineItem[];
  page: number;
  page_size: number;
  total: number;
  is_last_page: boolean;
}