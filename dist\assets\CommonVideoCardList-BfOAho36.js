import{d as S,r as h,l as $,B as z,y as E,c as o,o as a,f as r,b as v,L as U,w as _,j as g,F as m,e as p,g as e,I as F,t as n,i as u,_ as M}from"./index-BE8XLQ59.js";import{T as R}from"./index-CAfqjps3.js";import{L as j}from"./index-ouF_E0m0.js";import{E as D}from"./index-B27ddgxZ.js";const q={key:0,class:"loading-container"},x=["onClick"],A={class:"video-thumbnail-wrapper"},G=["src","alt"],H={class:"play-button"},J={class:"video-duration"},K={class:"video-content"},O={class:"video-title"},P={class:"video-summary"},Q={class:"video-meta"},W={class:"author"},X={class:"date"},Y={class:"card-meta"},Z={key:0,class:"meta-left"},ss={class:"category"},es={key:1,class:"meta-right"},as={class:"card-tags"},ts={key:3},os=["onClick"],ns={class:"card-image-container"},is=["src","alt"],ls={class:"card-content"},ds={class:"news-title"},cs={class:"news-description"},rs=S({__name:"CommonVideoCardList",props:{items:{},loading:{type:Boolean},finished:{type:Boolean,default:!1},emptyText:{default:"暂无内容"},useInfiniteScroll:{type:Boolean,default:!0}},emits:["load-more","card-click"],setup(f,{emit:y}){const l=h(null),k=()=>l.value;$(()=>{z(()=>{console.log("滚动容器:",l.value)})});const C=f,w=y,d=h(!1);E(()=>C.loading,t=>{d.value=t},{immediate:!0});const L=()=>{console.log("触发加载更多"),w("load-more")};return(t,i)=>{const B=U,b=D,I=F,T=j,V=R;return a(),o("div",{class:"common-card-list",ref_key:"scrollContainerRef",ref:l},[t.loading&&!t.items.length?(a(),o("div",q,[v(B,{size:"24px"},{default:_(()=>i[1]||(i[1]=[g("加载中...")])),_:1,__:[1]})])):t.items.length?t.useInfiniteScroll?(a(),r(T,{key:2,loading:d.value,"onUpdate:loading":i[0]||(i[0]=s=>d.value=s),finished:t.finished,"finished-text":"没有更多了",onLoad:L,"immediate-check":!1,"scroll-container":k},{default:_(()=>[(a(!0),o(m,null,p(t.items,s=>(a(),o("div",{key:s.id,class:"news-card animate__animated animate__fadeInUp",onClick:c=>t.$emit("card-click",s)},[e("div",A,[e("img",{src:s.thumbnail,alt:s.name,class:"video-thumbnail"},null,8,G),e("div",H,[v(I,{name:"play",size:"24",color:"#fff"})]),e("div",J,n(s.duration),1)]),e("div",K,[e("h3",O,n(s.name),1),e("p",P,n(s.desc),1),e("div",Q,[e("span",W,n(s.creater),1),e("span",X,n(s.create_time),1)])]),e("div",Y,[s.cat_display?(a(),o("div",Z,[e("span",ss,n(s.cat_display),1)])):u("",!0),s.tags&&s.tags.length?(a(),o("div",es,[e("div",as,[(a(!0),o(m,null,p(s.tags,(c,N)=>(a(),o("span",{key:N,class:"tag"},n(c),1))),128))])])):u("",!0)])],8,x))),128))]),_:1},8,["loading","finished"])):(a(),o("div",ts,[(a(!0),o(m,null,p(t.items,s=>(a(),o("div",{key:s.id,class:"news-card animate__animated animate__fadeInUp",onClick:c=>t.$emit("card-click",s)},[e("div",ns,[s.tag?(a(),r(V,{key:0,type:"danger",size:"medium",class:"news-tag",round:""},{default:_(()=>[g(n(s.tag),1)]),_:2},1024)):u("",!0),e("img",{src:s.thumbnail,alt:s.name,class:"news-image"},null,8,is),i[2]||(i[2]=e("div",{class:"image-overlay"},null,-1))]),e("div",ls,[e("h3",ds,n(s.name),1),e("p",cs,n(s.desc),1)])],8,os))),128))])):(a(),r(b,{key:1,description:t.emptyText},null,8,["description"]))],512)}}}),hs=M(rs,[["__scopeId","data-v-eb4f5268"]]);export{hs as C};
