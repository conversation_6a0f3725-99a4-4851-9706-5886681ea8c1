import{d as l,a as i,u as m,r as p,l as _,c as u,o as f,b as a,g as h,_ as v}from"./index-BE8XLQ59.js";import{G as x,a as b}from"./GlobalHeader-lL88u8sR.js";import{L as g}from"./LeaderDetailCard-VVs8AxZp.js";import{s as t}from"./function-call-BUl5915X.js";import"./index-CAfqjps3.js";import"./index-B27ddgxZ.js";const L={class:"medicine-detail-page"},R={class:"detail-content"},k=l({__name:"index",setup(y){const s=i(),r=m(),o=p("");_(()=>{o.value=s.params.id,o.value||(t("药剂ID无效"),r.back())});const n=e=>{console.log("特色药剂详情加载成功:",e)},d=e=>{console.error("特色药剂详情加载失败:",e),t(e)},c=()=>{console.log("重试加载特色药剂详情")};return(e,B)=>(f(),u("div",L,[a(x,{title:"特色药剂详情"}),h("div",R,[a(g,{"leader-id":o.value,"api-type":"medicine",onLoaded:n,onError:d,onRetry:c},null,8,["leader-id"])]),a(b)]))}}),V=v(k,[["__scopeId","data-v-7cd54430"]]);export{V as default};
