import{d as C,u as V,a as T,r as d,c as p,o as r,b as a,w as y,F as z,e as P,f as $,n as H,g as o,h as I,I as M,t as f,_ as k,i as b,j as S,k as U,l as E,L as B,P as F,m as R,p as L,q}from"./index-BE8XLQ59.js";import{B as j}from"./index-jIY073my.js";import{G as Y,a as J}from"./GlobalHeader-lL88u8sR.js";import{G as K,a as O,g as Q,b as W,C as X}from"./index-BJjcaqGM.js";import{S as x,A as Z,g as ee}from"./ArticleGridSection-Lg8Nwmv4.js";import{s as w}from"./function-call-BUl5915X.js";import{E as te}from"./index-B27ddgxZ.js";import{T as oe}from"./index-CAfqjps3.js";import{C as se}from"./CommonVideoCardList-BfOAho36.js";import"./index-sbxbYdRt.js";import"./GridCard-D-EgbodX.js";import"./index-ouF_E0m0.js";const ae={class:"section-container"},ne=["onClick"],ce=["onClick"],ie=C({__name:"FunctionGrid",setup(m){const n=V(),i=T(),v=d([{icon:"manager-o",text:"领导信息",bgColor:"#e8f4ff",iconColor:"#1989fa",routeName:"Leader"},{icon:"friends-o",text:"名中医信息",bgColor:"#e8f7ff",iconColor:"#07c160",routeName:"Doctor"}]),l=d([{icon:"gift-o",text:"特色制剂",bgColor:"#fef2e8",iconColor:"#ff976a",routeName:"Medicine"},{icon:"cluster-o",text:"特色科室",bgColor:"#f5f5f5",iconColor:"#ee0a24",routeName:"Dept"},{icon:"star-o",text:"重点科室",bgColor:"#f0f9eb",iconColor:"#8bc34a",routeName:"MainDept"}]),s=t=>i.name===t.routeName,c=t=>s(t)?"#fff":t.iconColor,e=t=>s(t)?{background:`linear-gradient(135deg, ${t.iconColor}, ${t.iconColor}dd)`}:{backgroundColor:t.bgColor},u=t=>{switch(console.log(`点击了${t.text}`),t.text){case"领导信息":n.push({name:"Leader",params:{type:"leader"}});break;case"名中医信息":n.push({name:"Doctor",params:{type:"doctor"}});break;case"特色制剂":n.push({name:"Medicine",params:{type:"medicine"}});break;case"特色科室":n.push({name:"Dept",params:{type:"department"}});break;case"重点科室":n.push({name:"MainDept",params:{type:"main-department"}});break;default:w(`点击了${t.text}`)}};return(t,h)=>{const g=M,A=O,D=K;return r(),p("div",ae,[a(x,{title:"常用功能",showMore:!1}),a(D,{"column-num":2,border:!1,gutter:10,class:"function-grid first-row"},{default:y(()=>[(r(!0),p(z,null,P(v.value,(_,N)=>(r(),$(A,{key:N,class:"animate__animated animate__fadeInUp",style:H({animationDelay:N*.05+"s"})},{default:y(()=>[o("div",{class:I(["grid-item-content",{active:s(_)}]),onClick:G=>u(_)},[o("div",{class:I(["icon-wrapper",{active:s(_)}]),style:H(e(_))},[a(g,{name:_.icon,color:c(_),size:"24"},null,8,["name","color"])],6),o("span",{class:I(["grid-text",{active:s(_)}])},f(_.text),3)],10,ne)]),_:2},1032,["style"]))),128))]),_:1}),a(D,{"column-num":3,border:!1,gutter:10,class:"function-grid second-row"},{default:y(()=>[(r(!0),p(z,null,P(l.value,(_,N)=>(r(),$(A,{key:N,class:"animate__animated animate__fadeInUp",style:H({animationDelay:(N+2)*.05+"s"})},{default:y(()=>[o("div",{class:I(["grid-item-content",{active:s(_)}]),onClick:G=>u(_)},[o("div",{class:I(["icon-wrapper",{active:s(_)}]),style:H(e(_))},[a(g,{name:_.icon,color:c(_),size:"24"},null,8,["name","color"])],6),o("span",{class:I(["grid-text",{active:s(_)}])},f(_.text),3)],10,ce)]),_:2},1032,["style"]))),128))]),_:1})])}}}),le=k(ie,[["__scopeId","data-v-7cfa33f2"]]),re={class:"news-image-container"},de=["src","alt"],ue={class:"news-content"},me={class:"news-header"},_e={key:1,class:"news-date"},pe={class:"news-title"},ve={key:0,class:"news-description"},fe={key:1,class:"news-meta"},he={key:0,class:"meta-item"},ge={key:1,class:"meta-item"},we=C({__name:"NewsCard",props:{newsItem:{},showMeta:{type:Boolean,default:!1},clickable:{type:Boolean,default:!0}},emits:["click","imageError"],setup(m,{emit:n}){const i=m,v=n,l=()=>{i.clickable&&v("click",i.newsItem)},s=()=>{v("imageError",i.newsItem)},c=e=>{try{const u=new Date(e),t=u.getFullYear(),h=String(u.getMonth()+1).padStart(2,"0"),g=String(u.getDate()).padStart(2,"0");return`${t}.${h}.${g}`}catch{return e}};return(e,u)=>{const t=oe,h=M;return r(),p("div",{class:"news-card",onClick:l},[o("div",re,[o("img",{src:e.newsItem.thumbnail,alt:e.newsItem.name,class:"news-image",onError:s},null,40,de),u[0]||(u[0]=o("div",{class:"image-overlay"},null,-1))]),o("div",ue,[o("div",me,[e.newsItem.category?(r(),$(t,{key:0,type:"primary",size:"medium",class:"news-category"},{default:y(()=>[S(f(e.newsItem.category),1)]),_:1})):b("",!0),e.newsItem.publishDate?(r(),p("span",_e,f(c(e.newsItem.publishDate)),1)):b("",!0)]),o("h3",pe,f(e.newsItem.name),1),e.newsItem.desc?(r(),p("p",ve,f(e.newsItem.desc),1)):b("",!0),e.showMeta?(r(),p("div",fe,[e.newsItem.author?(r(),p("div",he,[a(h,{name:"user-o",size:"12"}),o("span",null,f(e.newsItem.author),1)])):b("",!0),e.newsItem.views?(r(),p("div",ge,[a(h,{name:"eye-o",size:"12"}),o("span",null,f(e.newsItem.views),1)])):b("",!0)])):b("",!0)])])}}}),ye=k(we,[["__scopeId","data-v-76eff11e"]]);function Ce(m){return{id:m.id,name:m.name,desc:m.desc,thumbnail:m.thumbnail,category:"医院新闻",publishDate:m.create_time,views:m.viewtimes?parseInt(m.viewtimes):void 0}}function ke(m){return m.map(Ce)}const Ie={class:"section-container"},be={key:0,class:"loading-container"},$e={key:2,class:"news-list"},De=C({__name:"HospitalNews",setup(m){const n=V(),i=d(!1),v=d(""),l=d(!1);d(4),d(!1);const s=d([]),c=U(()=>ke(s.value)),e=async()=>{if(!i.value){i.value=!0;try{const t=await Q();if(console.log("获取到医院新闻列表数据:",t),!t||!t.results||!Array.isArray(t.results)){console.error("API响应格式不正确:",t),s.value=[];return}console.log("响应数据项数量:",t.results.length),s.value=t.results,console.log("设置的原始数据:",s.value),console.log("转换后的NewsCard数据:",c.value)}catch(t){console.error("获取医院新闻失败:",t),v.value="获取医院新闻失败，请稍后再试",w("获取新闻失败，请稍后重试")}finally{i.value=!1,l.value=!1}}},u=t=>{console.log("点击了新闻:",t),n.push({name:"HospitalNewsDetail",params:{id:t.id}})};return E(()=>{e()}),(t,h)=>{const g=B,A=te;return r(),p("div",Ie,[a(x,{title:"医院新闻",icon:"description-o",moreLink:"/hospital-news-list",showMore:!0}),i.value&&!c.value.length?(r(),p("div",be,[a(g,{size:"24px"},{default:y(()=>h[0]||(h[0]=[S("加载中...")])),_:1,__:[0]})])):c.value.length?(r(),p("div",$e,[(r(!0),p(z,null,P(c.value,D=>(r(),$(ye,{key:D.id,"news-item":D,onClick:u},null,8,["news-item"]))),128))])):(r(),$(A,{key:1,description:"暂无新闻"}))])}}}),Ne=k(De,[["__scopeId","data-v-f126ab3c"]]),Ae={class:"section-container"},He=C({__name:"HotVideos",setup(m){const n=V(),i=d(!1);d(""),d(!1),d(6);const v=d(!1),l=d([]),s=async()=>{if(i.value)return;i.value=!0;const e=await W();if(!e||!e.results||!Array.isArray(e.results)){console.error("API响应格式不正确:",e),l.value=[],i.value=!1;return}const u=e.results.map(t=>({id:t.id,name:t.name,desc:t.desc,thumbnail:t.thumbnail,duration:"15:30",author:"张医生",authorAvatar:"/images/20.jpg"}));console.log("格式化后的数据:",u),l.value=u,i.value=!1},c=e=>{console.log("点击的视频项:",e),n.push({name:"VideoDetail",params:{id:e.id}})};return E(()=>{s()}),(e,u)=>(r(),p("div",Ae,[a(x,{title:"热门视频",icon:"tv-o",showMore:!1}),a(se,{items:l.value,loading:i.value,finished:v.value,"use-infinite-scroll":!0,onLoadMore:s,onCardClick:c},null,8,["items","loading","finished"])]))}}),Le=k(He,[["__scopeId","data-v-9d77cf65"]]),Me={class:"video-player"},xe={class:"video-title"},ze={class:"video-placeholder"},Pe=C({__name:"VideoPopup",setup(m){const n=d(!1),i=d({});return(v,l)=>{const s=M,c=F;return r(),$(c,{show:n.value,"onUpdate:show":l[0]||(l[0]=e=>n.value=e),round:"",closeable:"","close-icon-position":"top-right",class:"video-popup"},{default:y(()=>[o("div",Me,[o("div",xe,f(i.value.title),1),o("div",ze,[a(s,{name:"play-circle-o",size:"48",color:"#fff"}),l[1]||(l[1]=o("div",{class:"video-loading"},"视频加载中...",-1))])])]),_:1},8,["show"])}}}),Ve=k(Pe,[["__scopeId","data-v-96c573d1"]]),Ee={class:"section-container"},Se={class:"contact-content"},Ge={class:"contact-list"},Te={class:"contact-icon phone-icon"},Ue={class:"contact-info"},Be={class:"contact-value"},Fe={class:"contact-icon email-icon"},Re={class:"contact-info"},qe={class:"contact-value"},je={class:"contact-icon location-icon"},Ye={class:"contact-info"},Je={class:"contact-value"},Ke={class:"contact-item"},Oe={class:"contact-icon time-icon"},Qe={class:"contact-info"},We={class:"contact-value"},Xe=C({__name:"ContactUs",setup(m){const{effectiveContactInfo:n}=R(),i=()=>{const s=n.value.phone;navigator.userAgent.match(/(iPhone|iPod|Android|ios)/i)?window.location.href=`tel:${s}`:navigator.clipboard.writeText(s).then(()=>{w("电话号码已复制到剪贴板")}).catch(()=>{w("复制失败，请手动拨打："+s)})},v=()=>{const s=n.value.email;navigator.userAgent.match(/(iPhone|iPod|Android|ios)/i)?window.location.href=`mailto:${s}`:navigator.clipboard.writeText(s).then(()=>{w("邮箱地址已复制到剪贴板")}).catch(()=>{w("复制失败，邮箱："+s)})},l=()=>{const s=n.value.address;navigator.userAgent.match(/(iPhone|iPod|ios)/i)?window.location.href=`maps://maps.apple.com/?q=${encodeURIComponent(s)}`:navigator.userAgent.match(/Android/i)?window.location.href=`geo:0,0?q=${encodeURIComponent(s)}`:navigator.clipboard.writeText(s).then(()=>{w("地址已复制到剪贴板")}).catch(()=>{w("地址："+s)})};return(s,c)=>{const e=M;return r(),p("div",Ee,[a(x,{title:"联系我们",icon:"phone-o",showMore:!1}),o("div",Se,[o("div",Ge,[o("div",{class:"contact-item",onClick:i},[o("div",Te,[a(e,{name:"phone-o",size:"20"})]),o("div",Ue,[c[0]||(c[0]=o("div",{class:"contact-label"},"联系电话",-1)),o("div",Be,f(L(n).phone),1)])]),o("div",{class:"contact-item",onClick:v},[o("div",Fe,[a(e,{name:"envelop-o",size:"20"})]),o("div",Re,[c[1]||(c[1]=o("div",{class:"contact-label"},"邮箱地址",-1)),o("div",qe,f(L(n).email),1)])]),o("div",{class:"contact-item",onClick:l},[o("div",je,[a(e,{name:"location-o",size:"20"})]),o("div",Ye,[c[2]||(c[2]=o("div",{class:"contact-label"},"医院地址",-1)),o("div",Je,f(L(n).address),1)])]),o("div",Ke,[o("div",Oe,[a(e,{name:"clock-o",size:"20"})]),o("div",Qe,[c[3]||(c[3]=o("div",{class:"contact-label"},"营业时间",-1)),o("div",We,f(L(n).business_hours),1)])])])])])}}}),Ze=k(Xe,[["__scopeId","data-v-0f64d2a9"]]),et={class:"home-container"},tt=C({__name:"index",setup(m){const n=d(!1),i=d(""),v=d(!1),l=d({culture_latest_data:[],knowledge_latest_data:[],case_latest_data:[]}),s=async()=>{if(!n.value)try{n.value=!0,i.value="";const e=await ee({source:"index"});l.value=e.items,console.log("获取到的文章列表数据:",l.value)}catch(e){console.error("获取文章列表失败:",e),i.value="获取文章列表失败，请稍后再试"}finally{n.value=!1,v.value=!1}},c=()=>{document.querySelectorAll(".animate__animated:not(.animate__fadeIn):not(.animate__fadeInUp)").forEach(u=>{const t=u.getBoundingClientRect(),h=window.innerHeight||document.documentElement.clientHeight;t.top<=h*.8&&u.classList.add("animate__fadeInUp")})};return E(()=>{window.addEventListener("scroll",c),c(),s()}),q(()=>{window.removeEventListener("scroll",c)}),(e,u)=>{const t=Y,h=J,g=j;return r(),p("div",et,[a(t),a(X,{position:"1"}),a(le),a(Ne),a(Z,{title:"最新文章",icon:"notes-o",showMore:!0,cultureData:l.value.culture_latest_data,knowledgeData:l.value.knowledge_latest_data,caseData:l.value.case_latest_data},null,8,["cultureData","knowledgeData","caseData"]),a(Le),a(Ze),a(h),a(g,{right:"16",bottom:"80"}),a(Ve)])}}}),pt=k(tt,[["__scopeId","data-v-15f5fa5f"]]);export{pt as default};
