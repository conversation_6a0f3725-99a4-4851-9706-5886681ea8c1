/**
 * SEO管理工具
 * 用于动态更新页面的title和meta标签
 */

import type { SeoConfig } from '../types/system';

/**
 * 更新页面标题
 */
export function updatePageTitle(title: string): void {
  document.title = title;
}

/**
 * 更新meta标签
 */
export function updateMetaTag(name: string, content: string): void {
  let meta = document.querySelector(`meta[name="${name}"]`);
  if (!meta) {
    // 如果不存在，创建新的meta标签
    meta = document.createElement('meta');
    meta.setAttribute('name', name);
    document.head.appendChild(meta);
  }
  meta.setAttribute('content', content);
}

/**
 * 更新property类型的meta标签（如og:title）
 */
export function updateMetaProperty(property: string, content: string): void {
  let meta = document.querySelector(`meta[property="${property}"]`);
  if (!meta) {
    // 如果不存在，创建新的meta标签
    meta = document.createElement('meta');
    meta.setAttribute('property', property);
    document.head.appendChild(meta);
  }
  meta.setAttribute('content', content);
}

/**
 * 根据SEO配置更新所有相关的meta标签
 */
export function updateSeoMeta(seoConfig: SeoConfig): void {
  // 更新基础SEO标签
  updatePageTitle(seoConfig.site_title);
  updateMetaTag('description', seoConfig.site_description);
  updateMetaTag('keywords', seoConfig.site_keywords);
  updateMetaTag('author', seoConfig.site_author);
  
  // 更新Open Graph标签
  updateMetaProperty('og:title', seoConfig.site_title);
  updateMetaProperty('og:description', seoConfig.site_description);
  updateMetaProperty('og:site_name', seoConfig.site_name);
  
  // 更新Twitter Card标签
  updateMetaTag('twitter:title', seoConfig.site_title);
  updateMetaTag('twitter:description', seoConfig.site_description);
  
  // 更新移动端标签
  updateMetaTag('apple-mobile-web-app-title', seoConfig.app_title);
}

/**
 * 更新特定页面的SEO信息
 */
export function updatePageSeo(pageTitle: string, pageDescription?: string): void {
  updatePageTitle(pageTitle);
  
  if (pageDescription) {
    updateMetaTag('description', pageDescription);
    updateMetaProperty('og:title', pageTitle);
    updateMetaProperty('og:description', pageDescription);
    updateMetaTag('twitter:title', pageTitle);
    updateMetaTag('twitter:description', pageDescription);
  }
} 