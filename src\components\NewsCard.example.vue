<template>
  <div class="news-card-example">
    <h2>NewsCard 组件使用示例</h2>
    
    <!-- 基础用法 -->
    <div class="example-section">
      <h3>基础用法</h3>
      <NewsCard 
        :news-item="basicNewsItem" 
        @click="handleNewsClick"
      />
    </div>
    
    <!-- 显示元信息 -->
    <div class="example-section">
      <h3>显示元信息（作者、浏览量）</h3>
      <NewsCard 
        :news-item="fullNewsItem" 
        :show-meta="true"
        @click="handleNewsClick"
      />
    </div>
    
    <!-- 不可点击状态 -->
    <div class="example-section">
      <h3>不可点击状态</h3>
      <NewsCard 
        :news-item="basicNewsItem" 
        :clickable="false"
      />
    </div>
    
    <!-- 新闻列表 -->
    <div class="example-section">
      <h3>新闻列表</h3>
      <div class="news-list">
        <NewsCard 
          v-for="item in newsList" 
          :key="item.id"
          :news-item="item"
          @click="handleNewsClick"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import NewsCard, { type NewsItem } from './NewsCard.vue';
import { showToast } from 'vant';

// 基础新闻项
const basicNewsItem = ref<NewsItem>({
  id: 1,
  name: '我院成功举办中医药学术大会',
  desc: '本次大会汇聚了全国知名中医专家，共同探讨中医药发展新方向...',
  thumbnail: 'https://via.placeholder.com/100x80',
  category: '医院新闻',
  publishDate: '2023-01-01'
});

// 完整信息新闻项
const fullNewsItem = ref<NewsItem>({
  id: 2,
  name: '中医养生：春季如何调理身体',
  desc: '春季是万物复苏的季节，也是调理身体的最佳时机。中医认为春季应该...',
  thumbnail: 'https://via.placeholder.com/100x80',
  category: '养生知识',
  publishDate: '2023-01-02',
  author: '张医生',
  views: 1234
});

// 新闻列表
const newsList = ref<NewsItem[]>([
  {
    id: 3,
    name: '针灸治疗颈椎病的临床研究',
    desc: '通过对100例颈椎病患者的针灸治疗观察，发现针灸在改善颈椎病症状方面具有显著效果...',
    thumbnail: 'https://via.placeholder.com/100x80',
    category: '学术研究',
    publishDate: '2023-01-03'
  },
  {
    id: 4,
    name: '中药材质量控制新标准发布',
    desc: '国家药监局发布了新的中药材质量控制标准，进一步规范中药材的生产和流通...',
    thumbnail: 'https://via.placeholder.com/100x80',
    category: '行业动态',
    publishDate: '2023-01-04'
  },
  {
    id: 5,
    name: '传统中医与现代医学的融合发展',
    desc: '探讨传统中医理论与现代医学技术相结合的发展模式，为患者提供更好的医疗服务...',
    thumbnail: 'https://via.placeholder.com/100x80',
    category: '医学前沿',
    publishDate: '2023-01-05'
  }
]);

// 处理新闻点击
const handleNewsClick = (item: NewsItem) => {
  showToast(`点击了新闻: ${item.name}`);
  console.log('新闻点击事件:', item);
};
</script>

<style scoped>
.news-card-example {
  padding: 20px;
  max-width: 600px;
  margin: 0 auto;
}

.example-section {
  margin-bottom: 30px;
}

.example-section h3 {
  margin-bottom: 15px;
  color: #333;
  font-size: 16px;
}

.news-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}
</style> 