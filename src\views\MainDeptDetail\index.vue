<template>
  <div class="main-dept-detail-page">
    <GlobalHeader title="重点科室详情" />
    <div class="detail-content">
      <LeaderDetailCard 
        :leader-id="mainDeptId" 
        api-type="mainDept"
        @loaded="handleLoaded"
        @error="handleError"
        @retry="handleRetry"
      />
    </div>
    <GlobalFooter />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { showToast } from 'vant';
import GlobalHeader from '../../components/GlobalHeader.vue';
import GlobalFooter from '../../components/GlobalFooter.vue';
import LeaderDetailCard from '../../components/LeaderDetailCard.vue';
import type { LeaderDetail } from '../LeaderDetail/api';

const route = useRoute();
const router = useRouter();
const mainDeptId = ref<string>('');

onMounted(() => {
  mainDeptId.value = route.params.id as string;
  if (!mainDeptId.value) {
    showToast('科室ID无效');
    router.back();
  }
});

const handleLoaded = (data: LeaderDetail) => {
  console.log('重点科室详情加载成功:', data);
};

const handleError = (error: string) => {
  console.error('重点科室详情加载失败:', error);
  showToast(error);
};

const handleRetry = () => {
  console.log('重试加载重点科室详情');
};
</script>

<style scoped>
.main-dept-detail-page {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f8f9fa;
}

.detail-content {
  flex: 1;
  overflow-y: auto;
}

</style>