<template>
    <!-- 全局页头组件 -->
    <GlobalHeader title="活动详情" @left-click="goBack" />

    <!-- 活动详情组件 -->
    <ActivityDetailCard 
      :activity-id="activityId"
      :show-actions="true"
      @loaded="handleActivityLoaded"
      @error="handleActivityError"
      @register="handleActivityRegister"
    />
  <GlobalFooter />
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { showToast } from 'vant';
import 'vant/es/toast/style';
import GlobalHeader from '../../components/GlobalHeader.vue';
import GlobalFooter from '@/components/GlobalFooter.vue';
import ActivityDetailCard from '../../components/ActivityDetailCard.vue';
import type { ActivityDetailItem } from '../Activity/api';

const router = useRouter();
const route = useRoute();

// 获取活动ID
const activityId = computed(() => {
  return Array.isArray(route.params.id) ? route.params.id[0] : route.params.id;
});

const goBack = () => {
  router.back();
};

// 处理活动数据加载完成
const handleActivityLoaded = (activity: ActivityDetailItem) => {
  console.log('活动详情加载完成:', activity);
  // 可以在这里处理加载完成后的逻辑，比如更新页面标题等
};

// 处理活动加载错误
const handleActivityError = (error: string) => {
  console.error('活动详情加载失败:', error);
  showToast(error);
};

// 处理报名事件
const handleActivityRegister = (activity: ActivityDetailItem) => {
  console.log('报名活动:', activity);
  // 可以在这里添加报名统计等逻辑
};
</script>

<style scoped>
.activity-detail-page {
  min-height: 100vh;
  background: #f5f7fa;
}
</style>
