<template>
  <div class="section-container featured-articles-section">
    <SectionHeader :title="title" :icon="icon" :showMore="showMore" />

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <van-loading size="24px">加载中...</van-loading>
    </div>

    <!-- 空状态 -->
    <van-empty v-else-if="!hasArticles" :description="`暂无${title}`" />

    <!-- 文章网格 -->
    <div v-else class="articles-container">
      <!-- 中医文化 -->
      <div v-if="cultureGridItems.length > 0" class="category-section">
        <div class="category-title">
          <van-icon name="gem-o" color="#7c3aed" size="16" />
          <span>中医文化</span>
        </div>
        <GridCard 
          :items="cultureGridItems" 
          @card-click="handleCardClick"
        />
      </div>

      <!-- 中医知识 -->
      <div v-if="knowledgeGridItems.length > 0" class="category-section">
        <div class="category-title">
          <van-icon name="book-o" color="#4b8bf4" size="16" />
          <span>中医知识</span>
        </div>
        <GridCard 
          :items="knowledgeGridItems" 
          @card-click="handleCardClick"
        />
      </div>

      <!-- 中医案例 -->
      <div v-if="caseGridItems.length > 0" class="category-section">
        <div class="category-title">
          <van-icon name="records" color="#059669" size="16" />
          <span>中医案例</span>
        </div>
        <GridCard 
          :items="caseGridItems" 
          @card-click="handleCardClick"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import SectionHeader from './SectionHeader.vue';
import GridCard from './GridCard.vue';
import type { GridCardItem } from './GridCard.vue';
import { 
  transformArticleListToGridCard, 
  getCategoryRouteName, 
  getCategoryBadge,
  type ApiArticleItem 
} from '../utils/articleTransform';

const router = useRouter();
const loading = ref(false);

// Props定义 - 保持与原组件完全一致，但增加可配置项
interface Props {
  cultureData: ApiArticleItem[];
  knowledgeData: ApiArticleItem[];
  caseData: ApiArticleItem[];
  title?: string;
  icon?: string;
  showMore?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  title: '精品文章',
  icon: 'description-o',
  showMore: false
});

// 转换数据为GridCard格式
const cultureGridItems = computed<GridCardItem[]>(() => {
  return transformArticleListToGridCard(props.cultureData, getCategoryBadge('culture'));
});

const knowledgeGridItems = computed<GridCardItem[]>(() => {
  return transformArticleListToGridCard(props.knowledgeData, getCategoryBadge('knowledge'));
});

const caseGridItems = computed<GridCardItem[]>(() => {
  return transformArticleListToGridCard(props.caseData, getCategoryBadge('case'));
});

// 检查是否有文章
const hasArticles = computed(() => {
  return cultureGridItems.value.length > 0 || 
         knowledgeGridItems.value.length > 0 || 
         caseGridItems.value.length > 0;
});

// 处理卡片点击 - 根据原始数据判断分类
const handleCardClick = (item: GridCardItem) => {
  console.log('点击了文章:', item);
  
  // 从原始数据中获取分类信息
  const originalData = item.originalData as ApiArticleItem;
  
  // 根据数据来源确定分类和路由
  let category = '';
  if (props.cultureData.some(culture => culture.id === item.id)) {
    category = 'culture';
  } else if (props.knowledgeData.some(knowledge => knowledge.id === item.id)) {
    category = 'knowledge';
  } else if (props.caseData.some(caseItem => caseItem.id === item.id)) {
    category = 'case';
  }
  
  // 获取对应的路由名称
  const routeName = getCategoryRouteName(category);
  
  // 跳转到详情页
  router.push({ name: routeName, params: { id: item.id } });
};

// 组件挂载时打印数据（保持与原组件一致）
onMounted(() => {
  console.log(`${props.title} - cultureData:`, props.cultureData);
  console.log(`${props.title} - knowledgeData:`, props.knowledgeData);
  console.log(`${props.title} - caseData:`, props.caseData);
  console.log(`${props.title} - 转换后的GridCard数据:`);
  console.log('- culture:', cultureGridItems.value);
  console.log('- knowledge:', knowledgeGridItems.value);
  console.log('- case:', caseGridItems.value);
});
</script>

<style scoped>
/* 区块通用样式 */
.section-container {
  padding: 5px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

/* 加载状态 */
.loading-container {
  padding: 30px 0;
  text-align: center;
}

/* 文章容器 */
.articles-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* 分类区域 */
.category-section {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.category-title {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  font-weight: 600;
  color: #333;
  padding-left: 4px;
}

/* 响应式调整 */
@media (max-width: 480px) {
  .articles-container {
    gap: 16px;
  }
  
  .category-section {
    gap: 10px;
  }
  
  .category-title {
    font-size: 13px;
  }
}

@media (max-width: 320px) {
  .articles-container {
    gap: 12px;
  }
  
  .category-section {
    gap: 8px;
  }
  
  .category-title {
    font-size: 12px;
  }
}
</style> 