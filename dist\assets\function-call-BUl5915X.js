import{d as I,Q as g,a8 as p,a9 as k,aZ as B,S as T,y as v,l as z,q as M,b as s,a2 as h,at as N,P as L,I as j,L as U,aa as q,aC as V,Y as b,af as _,a_ as D,r as H,a$ as Q,aW as W}from"./index-BE8XLQ59.js";let f=0;function Y(e){e?(f||document.body.classList.add("van-toast--unclickable"),f++):f&&(f--,f||document.body.classList.remove("van-toast--unclickable"))}const[Z,i]=T("toast"),$=["show","overlay","teleport","transition","overlayClass","overlayStyle","closeOnClickOverlay","zIndex"],A={icon:String,show:Boolean,type:k("text"),overlay:Boolean,message:g,iconSize:g,duration:B(2e3),position:k("middle"),teleport:[String,Object],wordBreak:String,className:p,iconPrefix:String,transition:k("van-fade"),loadingType:String,forbidClick:Boolean,overlayClass:p,overlayStyle:Object,closeOnClick:Boolean,closeOnClickOverlay:Boolean,zIndex:g};var E=I({name:Z,props:A,emits:["update:show"],setup(e,{emit:o,slots:a}){let l,c=!1;const r=()=>{const n=e.show&&e.forbidClick;c!==n&&(c=n,Y(c))},d=n=>o("update:show",n),y=()=>{e.closeOnClick&&d(!1)},u=()=>clearTimeout(l),O=()=>{const{icon:n,type:t,iconSize:C,iconPrefix:P,loadingType:x}=e;if(n||t==="success"||t==="fail")return s(j,{name:n||t,size:C,class:i("icon"),classPrefix:P},null);if(t==="loading")return s(U,{class:i("loading"),size:C,type:x},null)},S=()=>{const{type:n,message:t}=e;if(a.message)return s("div",{class:i("text")},[a.message()]);if(q(t)&&t!=="")return n==="html"?s("div",{key:0,class:i("text"),innerHTML:String(t)},null):s("div",{class:i("text")},[t])};return v(()=>[e.show,e.forbidClick],r),v(()=>[e.show,e.type,e.message,e.duration],()=>{u(),e.show&&e.duration>0&&(l=setTimeout(()=>{d(!1)},e.duration))}),z(r),M(r),()=>s(L,h({class:[i([e.position,e.wordBreak==="normal"?"break-normal":e.wordBreak,{[e.type]:!e.icon}]),e.className],lockScroll:!1,onClick:y,onClosed:u,"onUpdate:show":d},N(e,$)),{default:()=>[O(),S()]})}});const F={icon:"",type:"text",message:"",className:"",overlay:!1,onClose:void 0,onOpened:void 0,duration:2e3,teleport:"body",iconSize:void 0,iconPrefix:void 0,position:"middle",transition:"van-fade",forbidClick:!1,loadingType:void 0,overlayClass:"",overlayStyle:void 0,closeOnClick:!1,closeOnClickOverlay:!1};let m=[],G=!1,w=b({},F);const J=new Map;function K(e){return _(e)?e:{message:e}}function R(){const{instance:e}=D({setup(){const o=H(""),{open:a,state:l,close:c,toggle:r}=Q(),d=()=>{},y=()=>s(E,h(l,{onClosed:d,"onUpdate:show":r}),null);return v(o,u=>{l.message=u}),W().render=y,{open:a,close:c,message:o}}});return e}function X(){if(!m.length||G){const e=R();m.push(e)}return m[m.length-1]}function te(e={}){if(!V)return{};const o=X(),a=K(e);return o.open(b({},w,J.get(a.type||w.type),a)),o}export{te as s};
