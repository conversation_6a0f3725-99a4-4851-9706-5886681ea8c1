{"root": ["./src/main.ts", "./src/vite-env.d.ts", "./src/api/index.ts", "./src/api/loading.ts", "./src/api/request.ts", "./src/api/system.ts", "./src/api/urls.ts", "./src/composables/usesystemconfig.ts", "./src/router/index.ts", "./src/types/news.ts", "./src/types/system.ts", "./src/utils/articletransform.ts", "./src/utils/datetime.ts", "./src/utils/newstransform.ts", "./src/utils/saveimage.ts", "./src/utils/seomanager.ts", "./src/utils/share.ts", "./src/views/activity/api.ts", "./src/views/activitynews/api.ts", "./src/views/activitynewsdetail/api.ts", "./src/views/activityregistration/api.ts", "./src/views/cases/api.ts", "./src/views/casesdetail/api.ts", "./src/views/culture/api.ts", "./src/views/culturedetail/api.ts", "./src/views/dept/api.ts", "./src/views/deptdetail/api.ts", "./src/views/doctor/api.ts", "./src/views/doctordetail/api.ts", "./src/views/educationmedia/api.ts", "./src/views/educationmediadetail/api.ts", "./src/views/home/<USER>", "./src/views/knowledge/api.ts", "./src/views/leader/api.ts", "./src/views/leaderdetail/api.ts", "./src/views/maindept/api.ts", "./src/views/maindeptdetail/api.ts", "./src/views/medicine/api.ts", "./src/views/medicinedetail/api.ts", "./src/views/news/api.ts", "./src/views/newsdetail/api.ts", "./src/views/tcmknowledge/api.ts", "./src/views/tcmknowledgedetail/api.ts", "./src/views/video/api.ts", "./src/views/videodetail/api.ts", "./src/app.vue", "./src/components/activitycommoncardlist.vue", "./src/components/activitydetailcard.vue", "./src/components/activityhistorycardlist.example.vue", "./src/components/activityhistorycardlist.vue", "./src/components/articlegridsection.vue", "./src/components/commoncardlist.vue", "./src/components/commonvideocardlist.vue", "./src/components/globalfooter.vue", "./src/components/globalheader.vue", "./src/components/gridcard.example.vue", "./src/components/gridcard.vue", "./src/components/leaderdetailcard.vue", "./src/components/newscard.example.vue", "./src/components/newscard.vue", "./src/components/sectionheader.vue", "./src/views/activity/index.vue", "./src/views/activity/list.vue", "./src/views/activitydetail/index.vue", "./src/views/activitynews/list.vue", "./src/views/activitynewsdetail/index.vue", "./src/views/activityregistration/index.vue", "./src/views/cases/index.vue", "./src/views/casesdetail/index.vue", "./src/views/culture/index.vue", "./src/views/culturedetail/index.vue", "./src/views/dept/index.vue", "./src/views/deptdetail/index.vue", "./src/views/doctor/index.vue", "./src/views/doctordetail/index.vue", "./src/views/educationmedia/index.vue", "./src/views/educationmediadetail/index.vue", "./src/views/home/<USER>", "./src/views/home/<USER>/carousel.vue", "./src/views/home/<USER>/contactus.vue", "./src/views/home/<USER>/functiongrid.vue", "./src/views/home/<USER>/hospitalnews.vue", "./src/views/home/<USER>/hotvideos.vue", "./src/views/home/<USER>/navbar.vue", "./src/views/home/<USER>/subscribeform.vue", "./src/views/home/<USER>/videopopup.vue", "./src/views/knowledge/index.vue", "./src/views/knowledge/components/articlelist.vue", "./src/views/knowledge/components/featuredarticles.vue", "./src/views/knowledge/components/healthy.vue", "./src/views/leader/index.vue", "./src/views/leaderdetail/index.vue", "./src/views/maindept/index.vue", "./src/views/maindeptdetail/index.vue", "./src/views/medicine/index.vue", "./src/views/medicinedetail/index.vue", "./src/views/news/list.vue", "./src/views/newsdetail/index.vue", "./src/views/tcmknowledge/index.vue", "./src/views/tcmknowledgedetail/index.vue", "./src/views/video/index.vue", "./src/views/videodetail/index.vue"], "version": "5.8.3"}