import { ref, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { showToast } from 'vant';
import GlobalHeader from '../../components/GlobalHeader.vue';
import GlobalFooter from '../../components/GlobalFooter.vue';
import LeaderDetailCard from '../../components/LeaderDetailCard.vue';
const route = useRoute();
const router = useRouter();
const mainDeptId = ref('');
onMounted(() => {
    mainDeptId.value = route.params.id;
    if (!mainDeptId.value) {
        showToast('科室ID无效');
        router.back();
    }
});
const handleLoaded = (data) => {
    console.log('重点科室详情加载成功:', data);
};
const handleError = (error) => {
    console.error('重点科室详情加载失败:', error);
    showToast(error);
};
const handleRetry = () => {
    console.log('重试加载重点科室详情');
};
debugger; /* PartiallyEnd: #3632/scriptSetup.vue */
const __VLS_ctx = {};
let __VLS_components;
let __VLS_directives;
// CSS variable injection 
// CSS variable injection end 
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ class: "main-dept-detail-page" },
});
/** @type {[typeof GlobalHeader, ]} */ ;
// @ts-ignore
const __VLS_0 = __VLS_asFunctionalComponent(GlobalHeader, new GlobalHeader({
    title: "重点科室详情",
}));
const __VLS_1 = __VLS_0({
    title: "重点科室详情",
}, ...__VLS_functionalComponentArgsRest(__VLS_0));
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ class: "detail-content" },
});
/** @type {[typeof LeaderDetailCard, ]} */ ;
// @ts-ignore
const __VLS_3 = __VLS_asFunctionalComponent(LeaderDetailCard, new LeaderDetailCard({
    ...{ 'onLoaded': {} },
    ...{ 'onError': {} },
    ...{ 'onRetry': {} },
    leaderId: (__VLS_ctx.mainDeptId),
    apiType: "mainDept",
}));
const __VLS_4 = __VLS_3({
    ...{ 'onLoaded': {} },
    ...{ 'onError': {} },
    ...{ 'onRetry': {} },
    leaderId: (__VLS_ctx.mainDeptId),
    apiType: "mainDept",
}, ...__VLS_functionalComponentArgsRest(__VLS_3));
let __VLS_6;
let __VLS_7;
let __VLS_8;
const __VLS_9 = {
    onLoaded: (__VLS_ctx.handleLoaded)
};
const __VLS_10 = {
    onError: (__VLS_ctx.handleError)
};
const __VLS_11 = {
    onRetry: (__VLS_ctx.handleRetry)
};
var __VLS_5;
/** @type {[typeof GlobalFooter, ]} */ ;
// @ts-ignore
const __VLS_12 = __VLS_asFunctionalComponent(GlobalFooter, new GlobalFooter({}));
const __VLS_13 = __VLS_12({}, ...__VLS_functionalComponentArgsRest(__VLS_12));
/** @type {__VLS_StyleScopedClasses['main-dept-detail-page']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-content']} */ ;
var __VLS_dollars;
const __VLS_self = (await import('vue')).defineComponent({
    setup() {
        return {
            GlobalHeader: GlobalHeader,
            GlobalFooter: GlobalFooter,
            LeaderDetailCard: LeaderDetailCard,
            mainDeptId: mainDeptId,
            handleLoaded: handleLoaded,
            handleError: handleError,
            handleRetry: handleRetry,
        };
    },
});
export default (await import('vue')).defineComponent({
    setup() {
        return {};
    },
});
; /* PartiallyEnd: #4569/main.vue */
