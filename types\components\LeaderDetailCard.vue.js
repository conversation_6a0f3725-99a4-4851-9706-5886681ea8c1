import { ref, computed, watch } from 'vue';
import { useRouter } from 'vue-router';
import { getLeaderDetail } from '../views/LeaderDetail/api';
import { getDoctorDetail } from '../views/DoctorDetail/api';
import { getMedicineDetail } from '../views/MedicineDetail/api';
import { getDeptDetail } from '../views/DeptDetail/api';
import { getMainDeptDetail } from '../views/MainDeptDetail/api';
import { getEducationDetail } from '../views/EducationMediaDetail/api';
import { getKnowledgeDetail } from '../views/TcmKnowledgeDetail/api';
import { getCultureDetail } from '../views/CultureDetail/api';
import { getCaseDetail } from '../views/CasesDetail/api';
const props = withDefaults(defineProps(), {
    leaderId: '',
    leaderInfo: undefined,
    showRetryButton: true,
    apiType: 'leader'
});
const emit = defineEmits();
// 路由实例
const router = useRouter();
// 响应式数据
const loading = ref(false);
const error = ref('');
const defaultAvatar = '/images/default-avatar.svg';
// 领导数据
const leaderData = ref({
    id: 0,
    name: '',
    job: '',
    desc: '',
    thumbnail: '',
    cat_display: '',
    tags: [],
    content: '',
    create_time: '',
    specialties: [],
    experience: [],
    publications: [],
    achievements: [],
    research_directions: []
});
// 计算属性：确定数据源
const dataSource = computed(() => {
    return props.leaderInfo || leaderData.value;
});
// 获取详情数据
const fetchLeaderDetail = async () => {
    if (!props.leaderId) {
        error.value = 'ID无效';
        return;
    }
    try {
        loading.value = true;
        error.value = '';
        let response;
        let data;
        // 根据API类型调用不同的API
        switch (props.apiType) {
            case 'doctor':
                response = await getDoctorDetail(props.leaderId);
                data = response.famousdoctor;
                break;
            case 'medicine':
                response = await getMedicineDetail(props.leaderId);
                data = response.specialpreparations;
                break;
            case 'dept':
                response = await getDeptDetail(props.leaderId);
                data = response.characterdepart;
                break;
            case 'mainDept':
                response = await getMainDeptDetail(props.leaderId);
                data = response.maindept;
                break;
            case 'education':
                response = await getEducationDetail(props.leaderId);
                data = response.drumbeating;
                break;
            case 'knowledge':
                response = await getKnowledgeDetail(props.leaderId);
                data = response.knowledge;
                break;
            case 'culture':
                response = await getCultureDetail(props.leaderId);
                data = response.culture;
                break;
            case 'case':
                response = await getCaseDetail(props.leaderId);
                data = response.case;
                break;
            case 'leader':
            default:
                response = await getLeaderDetail(props.leaderId);
                data = response.leader;
                break;
        }
        if (!data) {
            throw new Error('API返回数据格式不正确');
        }
        const thumbnail = data.thumbnail ? data.thumbnail.trim() : '';
        leaderData.value = {
            id: data.id,
            name: data.name || '未知',
            job: data.job || '',
            desc: data.desc || '',
            thumbnail: thumbnail,
            cat_display: data.cat_display || data.category_name || '未分类',
            tags: data.tags || [],
            content: data.content || '',
            create_time: data.create_time || new Date().toISOString(), // 如果API没有返回时间，使用当前时间
            viewtimes_display: data.viewtimes_display || '0', // 浏览次数显示
            specialties: [],
            experience: [],
            publications: [],
            achievements: [],
            research_directions: []
        };
        // 调试信息：检查create_time字段
        console.log('API返回的原始数据:', data);
        console.log('create_time字段值:', data.create_time);
        console.log('处理后的leaderData:', leaderData.value);
        emit('loaded', leaderData.value);
    }
    catch (err) {
        console.error('获取详情失败:', err);
        error.value = '获取详情失败，请稍后再试';
        emit('error', error.value);
    }
    finally {
        loading.value = false;
    }
};
// 监听leaderId变化
watch(() => props.leaderId, (newId) => {
    if (newId && !props.leaderInfo) {
        fetchLeaderDetail();
    }
}, { immediate: true });
// 监听leaderInfo变化
watch(() => props.leaderInfo, (newInfo) => {
    if (newInfo) {
        leaderData.value = { ...newInfo };
    }
}, { immediate: true, deep: true });
// 处理图片加载错误
const handleImageError = (event) => {
    const img = event.target;
    img.src = defaultAvatar;
};
// 格式化描述
const formatDescription = (desc) => {
    return desc.replace(/\r\n/g, '<br>').replace(/\n/g, '<br>');
};
// 格式化时间
const formatTime = (timeStr) => {
    if (!timeStr)
        return '';
    try {
        const date = new Date(timeStr);
        if (isNaN(date.getTime())) {
            return timeStr; // 如果无法解析，返回原始字符串
        }
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        return `${year}-${month}-${day} 08:00`;
    }
    catch (error) {
        console.warn('时间格式化失败:', error);
        return timeStr;
    }
};
// 获取标签颜色类名
const getTagColorClass = (index) => {
    const colorClasses = [
        'tag-red',
        'tag-orange',
        'tag-yellow',
        'tag-green',
        'tag-cyan',
        'tag-blue',
        'tag-purple',
        'tag-pink',
        'tag-indigo',
        'tag-teal'
    ];
    return colorClasses[index % colorClasses.length];
};
// 处理重试
const handleRetry = () => {
    emit('retry');
    if (props.leaderId) {
        fetchLeaderDetail();
    }
};
// 处理返回上一级
const handleGoBack = () => {
    emit('goBack');
    // 使用路由返回上一页
    if (window.history.length > 1) {
        router.go(-1);
    }
    else {
        // 如果没有历史记录，根据API类型返回对应的列表页
        switch (props.apiType) {
            case 'doctor':
                router.push('/doctor');
                break;
            case 'medicine':
                router.push('/medicine');
                break;
            case 'dept':
                router.push('/dept');
                break;
            case 'mainDept':
                router.push('/main-dept');
                break;
            case 'leader':
            default:
                router.push('/leader');
                break;
        }
    }
};
// 暴露方法给父组件
const __VLS_exposed = {
    refresh: fetchLeaderDetail,
    leaderData: computed(() => dataSource.value),
    goBack: handleGoBack
};
defineExpose(__VLS_exposed);
debugger; /* PartiallyEnd: #3632/scriptSetup.vue */
const __VLS_withDefaultsArg = (function (t) { return t; })({
    leaderId: '',
    leaderInfo: undefined,
    showRetryButton: true,
    apiType: 'leader'
});
const __VLS_ctx = {};
let __VLS_components;
let __VLS_directives;
/** @type {__VLS_StyleScopedClasses['large-avatar']} */ ;
/** @type {__VLS_StyleScopedClasses['large-avatar']} */ ;
/** @type {__VLS_StyleScopedClasses['large-avatar']} */ ;
/** @type {__VLS_StyleScopedClasses['avatar-overlay']} */ ;
/** @type {__VLS_StyleScopedClasses['tag-item']} */ ;
/** @type {__VLS_StyleScopedClasses['section-header']} */ ;
/** @type {__VLS_StyleScopedClasses['rich-content']} */ ;
/** @type {__VLS_StyleScopedClasses['rich-content']} */ ;
/** @type {__VLS_StyleScopedClasses['rich-content']} */ ;
/** @type {__VLS_StyleScopedClasses['rich-content']} */ ;
/** @type {__VLS_StyleScopedClasses['rich-content']} */ ;
/** @type {__VLS_StyleScopedClasses['rich-content']} */ ;
/** @type {__VLS_StyleScopedClasses['rich-content']} */ ;
/** @type {__VLS_StyleScopedClasses['rich-content']} */ ;
/** @type {__VLS_StyleScopedClasses['large-avatar']} */ ;
/** @type {__VLS_StyleScopedClasses['section-title']} */ ;
/** @type {__VLS_StyleScopedClasses['thumbnail-section']} */ ;
/** @type {__VLS_StyleScopedClasses['description-section']} */ ;
/** @type {__VLS_StyleScopedClasses['richtext-section']} */ ;
/** @type {__VLS_StyleScopedClasses['large-avatar']} */ ;
/** @type {__VLS_StyleScopedClasses['section-title']} */ ;
// CSS variable injection 
// CSS variable injection end 
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ class: "leader-detail-card" },
});
if (__VLS_ctx.loading) {
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
        ...{ class: "loading-container" },
    });
    const __VLS_0 = {}.VanLoading;
    /** @type {[typeof __VLS_components.VanLoading, typeof __VLS_components.vanLoading, ]} */ ;
    // @ts-ignore
    const __VLS_1 = __VLS_asFunctionalComponent(__VLS_0, new __VLS_0({
        type: "spinner",
        color: "#1989fa",
        size: "24",
    }));
    const __VLS_2 = __VLS_1({
        type: "spinner",
        color: "#1989fa",
        size: "24",
    }, ...__VLS_functionalComponentArgsRest(__VLS_1));
    __VLS_asFunctionalElement(__VLS_intrinsicElements.p, __VLS_intrinsicElements.p)({
        ...{ class: "loading-text" },
    });
}
else if (__VLS_ctx.error) {
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
        ...{ class: "error-container" },
    });
    const __VLS_4 = {}.VanEmpty;
    /** @type {[typeof __VLS_components.VanEmpty, typeof __VLS_components.vanEmpty, typeof __VLS_components.VanEmpty, typeof __VLS_components.vanEmpty, ]} */ ;
    // @ts-ignore
    const __VLS_5 = __VLS_asFunctionalComponent(__VLS_4, new __VLS_4({
        description: "加载失败",
        image: "error",
    }));
    const __VLS_6 = __VLS_5({
        description: "加载失败",
        image: "error",
    }, ...__VLS_functionalComponentArgsRest(__VLS_5));
    __VLS_7.slots.default;
    {
        const { description: __VLS_thisSlot } = __VLS_7.slots;
        __VLS_asFunctionalElement(__VLS_intrinsicElements.p, __VLS_intrinsicElements.p)({
            ...{ class: "error-text" },
        });
        (__VLS_ctx.error);
    }
    const __VLS_8 = {}.VanButton;
    /** @type {[typeof __VLS_components.VanButton, typeof __VLS_components.vanButton, typeof __VLS_components.VanButton, typeof __VLS_components.vanButton, ]} */ ;
    // @ts-ignore
    const __VLS_9 = __VLS_asFunctionalComponent(__VLS_8, new __VLS_8({
        ...{ 'onClick': {} },
        round: true,
        type: "primary",
        ...{ class: "retry-btn" },
    }));
    const __VLS_10 = __VLS_9({
        ...{ 'onClick': {} },
        round: true,
        type: "primary",
        ...{ class: "retry-btn" },
    }, ...__VLS_functionalComponentArgsRest(__VLS_9));
    let __VLS_12;
    let __VLS_13;
    let __VLS_14;
    const __VLS_15 = {
        onClick: (__VLS_ctx.handleRetry)
    };
    __VLS_11.slots.default;
    var __VLS_11;
    var __VLS_7;
}
else {
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
        ...{ class: "content" },
    });
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
        ...{ class: "thumbnail-section" },
    });
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
        ...{ class: "section-header" },
    });
    const __VLS_16 = {}.VanIcon;
    /** @type {[typeof __VLS_components.VanIcon, typeof __VLS_components.vanIcon, ]} */ ;
    // @ts-ignore
    const __VLS_17 = __VLS_asFunctionalComponent(__VLS_16, new __VLS_16({
        name: "photo-o",
        size: "18",
        color: "#4b8bf4",
    }));
    const __VLS_18 = __VLS_17({
        name: "photo-o",
        size: "18",
        color: "#4b8bf4",
    }, ...__VLS_functionalComponentArgsRest(__VLS_17));
    __VLS_asFunctionalElement(__VLS_intrinsicElements.h2, __VLS_intrinsicElements.h2)({
        ...{ class: "section-title" },
    });
    (__VLS_ctx.leaderData.name);
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
        ...{ class: "thumbnail-content" },
    });
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
        ...{ class: "large-avatar" },
    });
    __VLS_asFunctionalElement(__VLS_intrinsicElements.img)({
        ...{ onError: (__VLS_ctx.handleImageError) },
        src: (__VLS_ctx.leaderData.thumbnail || __VLS_ctx.defaultAvatar),
        alt: (__VLS_ctx.leaderData.name),
    });
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
        ...{ class: "avatar-overlay" },
    });
    const __VLS_20 = {}.VanIcon;
    /** @type {[typeof __VLS_components.VanIcon, typeof __VLS_components.vanIcon, ]} */ ;
    // @ts-ignore
    const __VLS_21 = __VLS_asFunctionalComponent(__VLS_20, new __VLS_20({
        name: "user-o",
        size: "32",
        color: "#fff",
    }));
    const __VLS_22 = __VLS_21({
        name: "user-o",
        size: "32",
        color: "#fff",
    }, ...__VLS_functionalComponentArgsRest(__VLS_21));
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
        ...{ class: "info-section" },
    });
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
        ...{ class: "meta-container" },
    });
    if (__VLS_ctx.leaderData.create_time && __VLS_ctx.leaderData.create_time.trim()) {
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "meta-item" },
        });
        const __VLS_24 = {}.VanIcon;
        /** @type {[typeof __VLS_components.VanIcon, typeof __VLS_components.vanIcon, ]} */ ;
        // @ts-ignore
        const __VLS_25 = __VLS_asFunctionalComponent(__VLS_24, new __VLS_24({
            name: "clock-o",
            size: "14",
            color: "#999",
        }));
        const __VLS_26 = __VLS_25({
            name: "clock-o",
            size: "14",
            color: "#999",
        }, ...__VLS_functionalComponentArgsRest(__VLS_25));
        __VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({
            ...{ class: "meta-text" },
        });
        (__VLS_ctx.formatTime(__VLS_ctx.leaderData.create_time));
    }
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
        ...{ class: "meta-item" },
    });
    const __VLS_28 = {}.VanIcon;
    /** @type {[typeof __VLS_components.VanIcon, typeof __VLS_components.vanIcon, ]} */ ;
    // @ts-ignore
    const __VLS_29 = __VLS_asFunctionalComponent(__VLS_28, new __VLS_28({
        name: "eye-o",
        size: "14",
        color: "#999",
    }));
    const __VLS_30 = __VLS_29({
        name: "eye-o",
        size: "14",
        color: "#999",
    }, ...__VLS_functionalComponentArgsRest(__VLS_29));
    __VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({
        ...{ class: "meta-text" },
    });
    (__VLS_ctx.leaderData.viewtimes_display || '0');
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
        ...{ class: "tags-container" },
    });
    if (__VLS_ctx.leaderData.job && __VLS_ctx.leaderData.job.trim()) {
        const __VLS_32 = {}.VanTag;
        /** @type {[typeof __VLS_components.VanTag, typeof __VLS_components.vanTag, typeof __VLS_components.VanTag, typeof __VLS_components.vanTag, ]} */ ;
        // @ts-ignore
        const __VLS_33 = __VLS_asFunctionalComponent(__VLS_32, new __VLS_32({
            size: "large",
            round: true,
            ...{ class: "tag-item tag-blue" },
        }));
        const __VLS_34 = __VLS_33({
            size: "large",
            round: true,
            ...{ class: "tag-item tag-blue" },
        }, ...__VLS_functionalComponentArgsRest(__VLS_33));
        __VLS_35.slots.default;
        (__VLS_ctx.leaderData.job);
        var __VLS_35;
    }
    for (const [tag, index] of __VLS_getVForSourceType((__VLS_ctx.leaderData.tags))) {
        const __VLS_36 = {}.VanTag;
        /** @type {[typeof __VLS_components.VanTag, typeof __VLS_components.vanTag, typeof __VLS_components.VanTag, typeof __VLS_components.vanTag, ]} */ ;
        // @ts-ignore
        const __VLS_37 = __VLS_asFunctionalComponent(__VLS_36, new __VLS_36({
            key: (index),
            size: "large",
            round: true,
            ...{ class: (`tag-item ${__VLS_ctx.getTagColorClass(index)}`) },
        }));
        const __VLS_38 = __VLS_37({
            key: (index),
            size: "large",
            round: true,
            ...{ class: (`tag-item ${__VLS_ctx.getTagColorClass(index)}`) },
        }, ...__VLS_functionalComponentArgsRest(__VLS_37));
        __VLS_39.slots.default;
        (tag);
        var __VLS_39;
    }
    if (__VLS_ctx.leaderData.desc) {
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "description-section" },
        });
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "section-header" },
        });
        const __VLS_40 = {}.VanIcon;
        /** @type {[typeof __VLS_components.VanIcon, typeof __VLS_components.vanIcon, ]} */ ;
        // @ts-ignore
        const __VLS_41 = __VLS_asFunctionalComponent(__VLS_40, new __VLS_40({
            name: "info-o",
            size: "18",
            color: "#f5a623",
        }));
        const __VLS_42 = __VLS_41({
            name: "info-o",
            size: "18",
            color: "#f5a623",
        }, ...__VLS_functionalComponentArgsRest(__VLS_41));
        __VLS_asFunctionalElement(__VLS_intrinsicElements.h2, __VLS_intrinsicElements.h2)({
            ...{ class: "section-title" },
        });
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "section-content" },
        });
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "desc-content" },
        });
        __VLS_asFunctionalDirective(__VLS_directives.vHtml)(null, { ...__VLS_directiveBindingRestFields, value: (__VLS_ctx.formatDescription(__VLS_ctx.leaderData.desc)) }, null, null);
    }
    if (__VLS_ctx.leaderData.content) {
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "richtext-section" },
        });
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "section-header" },
        });
        const __VLS_44 = {}.VanIcon;
        /** @type {[typeof __VLS_components.VanIcon, typeof __VLS_components.vanIcon, ]} */ ;
        // @ts-ignore
        const __VLS_45 = __VLS_asFunctionalComponent(__VLS_44, new __VLS_44({
            name: "notes-o",
            size: "18",
            color: "#722ed1",
        }));
        const __VLS_46 = __VLS_45({
            name: "notes-o",
            size: "18",
            color: "#722ed1",
        }, ...__VLS_functionalComponentArgsRest(__VLS_45));
        __VLS_asFunctionalElement(__VLS_intrinsicElements.h2, __VLS_intrinsicElements.h2)({
            ...{ class: "section-title" },
        });
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "section-content" },
        });
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "rich-content" },
        });
        __VLS_asFunctionalDirective(__VLS_directives.vHtml)(null, { ...__VLS_directiveBindingRestFields, value: (__VLS_ctx.leaderData.content) }, null, null);
    }
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
        ...{ class: "back-button-section" },
    });
    const __VLS_48 = {}.VanButton;
    /** @type {[typeof __VLS_components.VanButton, typeof __VLS_components.vanButton, typeof __VLS_components.VanButton, typeof __VLS_components.vanButton, ]} */ ;
    // @ts-ignore
    const __VLS_49 = __VLS_asFunctionalComponent(__VLS_48, new __VLS_48({
        ...{ 'onClick': {} },
        type: "primary",
        size: "large",
        round: true,
        block: true,
        icon: "arrow-left",
        ...{ class: "back-button" },
    }));
    const __VLS_50 = __VLS_49({
        ...{ 'onClick': {} },
        type: "primary",
        size: "large",
        round: true,
        block: true,
        icon: "arrow-left",
        ...{ class: "back-button" },
    }, ...__VLS_functionalComponentArgsRest(__VLS_49));
    let __VLS_52;
    let __VLS_53;
    let __VLS_54;
    const __VLS_55 = {
        onClick: (__VLS_ctx.handleGoBack)
    };
    __VLS_51.slots.default;
    var __VLS_51;
}
/** @type {__VLS_StyleScopedClasses['leader-detail-card']} */ ;
/** @type {__VLS_StyleScopedClasses['loading-container']} */ ;
/** @type {__VLS_StyleScopedClasses['loading-text']} */ ;
/** @type {__VLS_StyleScopedClasses['error-container']} */ ;
/** @type {__VLS_StyleScopedClasses['error-text']} */ ;
/** @type {__VLS_StyleScopedClasses['retry-btn']} */ ;
/** @type {__VLS_StyleScopedClasses['content']} */ ;
/** @type {__VLS_StyleScopedClasses['thumbnail-section']} */ ;
/** @type {__VLS_StyleScopedClasses['section-header']} */ ;
/** @type {__VLS_StyleScopedClasses['section-title']} */ ;
/** @type {__VLS_StyleScopedClasses['thumbnail-content']} */ ;
/** @type {__VLS_StyleScopedClasses['large-avatar']} */ ;
/** @type {__VLS_StyleScopedClasses['avatar-overlay']} */ ;
/** @type {__VLS_StyleScopedClasses['info-section']} */ ;
/** @type {__VLS_StyleScopedClasses['meta-container']} */ ;
/** @type {__VLS_StyleScopedClasses['meta-item']} */ ;
/** @type {__VLS_StyleScopedClasses['meta-text']} */ ;
/** @type {__VLS_StyleScopedClasses['meta-item']} */ ;
/** @type {__VLS_StyleScopedClasses['meta-text']} */ ;
/** @type {__VLS_StyleScopedClasses['tags-container']} */ ;
/** @type {__VLS_StyleScopedClasses['tag-item']} */ ;
/** @type {__VLS_StyleScopedClasses['tag-blue']} */ ;
/** @type {__VLS_StyleScopedClasses['description-section']} */ ;
/** @type {__VLS_StyleScopedClasses['section-header']} */ ;
/** @type {__VLS_StyleScopedClasses['section-title']} */ ;
/** @type {__VLS_StyleScopedClasses['section-content']} */ ;
/** @type {__VLS_StyleScopedClasses['desc-content']} */ ;
/** @type {__VLS_StyleScopedClasses['richtext-section']} */ ;
/** @type {__VLS_StyleScopedClasses['section-header']} */ ;
/** @type {__VLS_StyleScopedClasses['section-title']} */ ;
/** @type {__VLS_StyleScopedClasses['section-content']} */ ;
/** @type {__VLS_StyleScopedClasses['rich-content']} */ ;
/** @type {__VLS_StyleScopedClasses['back-button-section']} */ ;
/** @type {__VLS_StyleScopedClasses['back-button']} */ ;
var __VLS_dollars;
const __VLS_self = (await import('vue')).defineComponent({
    setup() {
        return {
            loading: loading,
            error: error,
            defaultAvatar: defaultAvatar,
            leaderData: leaderData,
            handleImageError: handleImageError,
            formatDescription: formatDescription,
            formatTime: formatTime,
            getTagColorClass: getTagColorClass,
            handleRetry: handleRetry,
            handleGoBack: handleGoBack,
        };
    },
    __typeEmits: {},
    __typeProps: {},
    props: {},
});
export default (await import('vue')).defineComponent({
    setup() {
        return {
            ...__VLS_exposed,
        };
    },
    __typeEmits: {},
    __typeProps: {},
    props: {},
});
; /* PartiallyEnd: #4569/main.vue */
