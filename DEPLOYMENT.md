# 中医智慧前端项目部署指南

## 📋 项目概述

中医智慧(SXZYY)是一个基于 Vue 3 + Vite + TypeScript 的移动端响应式网站，专注于中医文化传播和健康服务。

## 🛠️ 技术栈

- **前端框架**: Vue 3.5.16
- **构建工具**: Vite 6.3.5
- **UI 组件库**: Vant 4.9.19
- **编程语言**: TypeScript 5.8.3
- **路由**: Vue Router 4.5.1
- **HTTP 客户端**: Axios 1.9.0

## 🚀 快速部署

### 方法一：自动化脚本部署

#### Windows 系统

```powershell
# 运行PowerShell部署脚本
.\deploy.ps1
```

#### Linux/macOS 系统

```bash
# 给脚本执行权限
chmod +x deploy.sh

# 运行部署脚本
./deploy.sh
```

### 方法二：手动构建部署

#### 1. 安装依赖

```bash
npm ci
```

#### 2. 构建生产版本

```bash
npm run build
```

#### 3. 部署到 Web 服务器

将 `dist/` 目录下的所有文件复制到您的 Web 服务器根目录

### 方法三：Docker 部署

#### 1. 构建 Docker 镜像

```bash
docker build -t zyyfront:latest .
```

#### 2. 运行容器

```bash
docker run -d --name zyyfront -p 80:80 --restart unless-stopped zyyfront:latest
```

#### 3. 使用 Docker Compose（推荐）

```bash
docker-compose up -d
```

## 🌐 Web 服务器配置

### Nginx 配置

项目已包含 `nginx.conf` 配置文件，主要特性：

- ✅ Vue Router 单页面应用支持
- ✅ API 代理转发到后端服务
- ✅ 静态资源缓存优化
- ✅ Gzip 压缩
- ✅ 移动端优化
- ✅ 安全头部设置

**配置要点：**

```nginx
# Vue Router单页面应用
location / {
    try_files $uri $uri/ /index.html;
}

# API代理
location /api/ {
    proxy_pass https://zyy.sxaliyun.cn;
}
```

### Apache 配置

在 `dist/` 目录创建 `.htaccess` 文件：

```apache
RewriteEngine On
RewriteIfFileExists %{REQUEST_FILENAME}
RewriteRule ^.*$ - [NC,L]
RewriteRule ^(.*) /index.html [NC,L]

# 启用压缩
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain text/html text/xml text/css text/javascript application/javascript application/json
</IfModule>

# 缓存设置
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType text/css "access plus 1 year"
    ExpiresByType application/javascript "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/svg+xml "access plus 1 year"
</IfModule>
```

### IIS 配置

在 `dist/` 目录创建 `web.config` 文件：

```xml
<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <system.webServer>
        <rewrite>
            <rules>
                <rule name="Vue Router" stopProcessing="true">
                    <match url=".*" />
                    <conditions logicalGrouping="MatchAll">
                        <add input="{REQUEST_FILENAME}" matchType="IsFile" negate="true" />
                        <add input="{REQUEST_FILENAME}" matchType="IsDirectory" negate="true" />
                    </conditions>
                    <action type="Rewrite" url="/" />
                </rule>
            </rules>
        </rewrite>
        <staticContent>
            <mimeMap fileExtension=".json" mimeType="application/json" />
        </staticContent>
        <httpCompression>
            <dynamicTypes>
                <add mimeType="application/json" enabled="true" />
            </dynamicTypes>
        </httpCompression>
    </system.webServer>
</configuration>
```

## 📱 移动端优化特性

- **响应式设计**: 适配各种屏幕尺寸
- **触摸优化**: 防止双击缩放、长按选择
- **加载动画**: 优雅的页面加载体验
- **PWA 支持**: 可添加到主屏幕
- **图片懒加载**: 优化移动端性能

## 🔧 环境变量配置

### 开发环境

```bash
NODE_ENV=development
VITE_API_BASE_URL=http://localhost:8000
```

### 生产环境

```bash
NODE_ENV=production
VITE_API_BASE_URL=https://zyy.sxaliyun.cn
```

## 🚀 性能优化

### 已实现的优化

1. **代码分割**: 路由级别的代码分割
2. **Tree Shaking**: 移除未使用的代码
3. **资源压缩**: CSS/JS 自动压缩
4. **图片优化**: WebP 格式支持
5. **缓存策略**: 静态资源长期缓存
6. **CDN 支持**: 静态资源 CDN 加速

### 构建输出分析

```bash
npm run build -- --report
```

## 🔒 安全配置

项目已包含以下安全措施：

- CSP 内容安全策略
- XSS 防护
- 点击劫持防护
- MIME 类型嗅探防护
- HTTPS 重定向

## 📊 监控和维护

### 性能监控

- 使用浏览器开发者工具监控
- 集成第三方监控服务（如：阿里云监控）

### 日志收集

- Nginx 访问日志
- 错误日志收集
- 用户行为分析

## 🆘 故障排除

### 常见问题

**1. 页面刷新 404 错误**

- 检查 Web 服务器是否配置了 Vue Router 重写规则

**2. API 请求失败**

- 检查代理配置是否正确
- 确认后端服务是否正常运行

**3. 静态资源加载失败**

- 检查资源路径配置
- 确认 CDN 配置是否正确

**4. 移动端样式异常**

- 检查 viewport 配置
- 确认 CSS 媒体查询是否正确

### 调试模式

```bash
# 本地预览生产构建
npm run preview

# 开发模式运行
npm run dev
```

## 📞 技术支持

如遇到部署问题，请检查：

1. Node.js 版本是否为 18+
2. 网络连接是否正常
3. 服务器权限是否足够
4. 防火墙设置是否正确

## 🎯 部署检查清单

- [ ] Node.js 环境安装完成
- [ ] 项目依赖安装成功
- [ ] 生产构建无错误
- [ ] Web 服务器配置正确
- [ ] API 代理配置验证
- [ ] 移动端兼容性测试
- [ ] 性能测试通过
- [ ] 安全配置验证

---

**部署完成后，您的中医智慧应用将具备：**

- 🏥 完整的中医知识体系
- 📱 优秀的移动端体验
- ⚡ 快速的页面加载速度
- 🔒 安全可靠的访问控制
