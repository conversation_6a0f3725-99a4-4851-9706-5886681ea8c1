import { ref } from 'vue';
import NewsCard from './NewsCard.vue';
import { showToast } from 'vant';
// 基础新闻项
const basicNewsItem = ref({
    id: 1,
    name: '我院成功举办中医药学术大会',
    desc: '本次大会汇聚了全国知名中医专家，共同探讨中医药发展新方向...',
    thumbnail: 'https://via.placeholder.com/100x80',
    category: '医院新闻',
    publishDate: '2023-01-01'
});
// 完整信息新闻项
const fullNewsItem = ref({
    id: 2,
    name: '中医养生：春季如何调理身体',
    desc: '春季是万物复苏的季节，也是调理身体的最佳时机。中医认为春季应该...',
    thumbnail: 'https://via.placeholder.com/100x80',
    category: '养生知识',
    publishDate: '2023-01-02',
    author: '张医生',
    views: 1234
});
// 新闻列表
const newsList = ref([
    {
        id: 3,
        name: '针灸治疗颈椎病的临床研究',
        desc: '通过对100例颈椎病患者的针灸治疗观察，发现针灸在改善颈椎病症状方面具有显著效果...',
        thumbnail: 'https://via.placeholder.com/100x80',
        category: '学术研究',
        publishDate: '2023-01-03'
    },
    {
        id: 4,
        name: '中药材质量控制新标准发布',
        desc: '国家药监局发布了新的中药材质量控制标准，进一步规范中药材的生产和流通...',
        thumbnail: 'https://via.placeholder.com/100x80',
        category: '行业动态',
        publishDate: '2023-01-04'
    },
    {
        id: 5,
        name: '传统中医与现代医学的融合发展',
        desc: '探讨传统中医理论与现代医学技术相结合的发展模式，为患者提供更好的医疗服务...',
        thumbnail: 'https://via.placeholder.com/100x80',
        category: '医学前沿',
        publishDate: '2023-01-05'
    }
]);
// 处理新闻点击
const handleNewsClick = (item) => {
    showToast(`点击了新闻: ${item.name}`);
    console.log('新闻点击事件:', item);
};
debugger; /* PartiallyEnd: #3632/scriptSetup.vue */
const __VLS_ctx = {};
let __VLS_components;
let __VLS_directives;
/** @type {__VLS_StyleScopedClasses['example-section']} */ ;
// CSS variable injection 
// CSS variable injection end 
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ class: "news-card-example" },
});
__VLS_asFunctionalElement(__VLS_intrinsicElements.h2, __VLS_intrinsicElements.h2)({});
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ class: "example-section" },
});
__VLS_asFunctionalElement(__VLS_intrinsicElements.h3, __VLS_intrinsicElements.h3)({});
/** @type {[typeof NewsCard, ]} */ ;
// @ts-ignore
const __VLS_0 = __VLS_asFunctionalComponent(NewsCard, new NewsCard({
    ...{ 'onClick': {} },
    newsItem: (__VLS_ctx.basicNewsItem),
}));
const __VLS_1 = __VLS_0({
    ...{ 'onClick': {} },
    newsItem: (__VLS_ctx.basicNewsItem),
}, ...__VLS_functionalComponentArgsRest(__VLS_0));
let __VLS_3;
let __VLS_4;
let __VLS_5;
const __VLS_6 = {
    onClick: (__VLS_ctx.handleNewsClick)
};
var __VLS_2;
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ class: "example-section" },
});
__VLS_asFunctionalElement(__VLS_intrinsicElements.h3, __VLS_intrinsicElements.h3)({});
/** @type {[typeof NewsCard, ]} */ ;
// @ts-ignore
const __VLS_7 = __VLS_asFunctionalComponent(NewsCard, new NewsCard({
    ...{ 'onClick': {} },
    newsItem: (__VLS_ctx.fullNewsItem),
    showMeta: (true),
}));
const __VLS_8 = __VLS_7({
    ...{ 'onClick': {} },
    newsItem: (__VLS_ctx.fullNewsItem),
    showMeta: (true),
}, ...__VLS_functionalComponentArgsRest(__VLS_7));
let __VLS_10;
let __VLS_11;
let __VLS_12;
const __VLS_13 = {
    onClick: (__VLS_ctx.handleNewsClick)
};
var __VLS_9;
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ class: "example-section" },
});
__VLS_asFunctionalElement(__VLS_intrinsicElements.h3, __VLS_intrinsicElements.h3)({});
/** @type {[typeof NewsCard, ]} */ ;
// @ts-ignore
const __VLS_14 = __VLS_asFunctionalComponent(NewsCard, new NewsCard({
    newsItem: (__VLS_ctx.basicNewsItem),
    clickable: (false),
}));
const __VLS_15 = __VLS_14({
    newsItem: (__VLS_ctx.basicNewsItem),
    clickable: (false),
}, ...__VLS_functionalComponentArgsRest(__VLS_14));
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ class: "example-section" },
});
__VLS_asFunctionalElement(__VLS_intrinsicElements.h3, __VLS_intrinsicElements.h3)({});
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ class: "news-list" },
});
for (const [item] of __VLS_getVForSourceType((__VLS_ctx.newsList))) {
    /** @type {[typeof NewsCard, ]} */ ;
    // @ts-ignore
    const __VLS_17 = __VLS_asFunctionalComponent(NewsCard, new NewsCard({
        ...{ 'onClick': {} },
        key: (item.id),
        newsItem: (item),
    }));
    const __VLS_18 = __VLS_17({
        ...{ 'onClick': {} },
        key: (item.id),
        newsItem: (item),
    }, ...__VLS_functionalComponentArgsRest(__VLS_17));
    let __VLS_20;
    let __VLS_21;
    let __VLS_22;
    const __VLS_23 = {
        onClick: (__VLS_ctx.handleNewsClick)
    };
    var __VLS_19;
}
/** @type {__VLS_StyleScopedClasses['news-card-example']} */ ;
/** @type {__VLS_StyleScopedClasses['example-section']} */ ;
/** @type {__VLS_StyleScopedClasses['example-section']} */ ;
/** @type {__VLS_StyleScopedClasses['example-section']} */ ;
/** @type {__VLS_StyleScopedClasses['example-section']} */ ;
/** @type {__VLS_StyleScopedClasses['news-list']} */ ;
var __VLS_dollars;
const __VLS_self = (await import('vue')).defineComponent({
    setup() {
        return {
            NewsCard: NewsCard,
            basicNewsItem: basicNewsItem,
            fullNewsItem: fullNewsItem,
            newsList: newsList,
            handleNewsClick: handleNewsClick,
        };
    },
});
export default (await import('vue')).defineComponent({
    setup() {
        return {};
    },
});
; /* PartiallyEnd: #4569/main.vue */
