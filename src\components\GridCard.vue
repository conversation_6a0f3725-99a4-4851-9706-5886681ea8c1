<template>
  <div class="grid-card-container">
    <div 
      v-for="item in items" 
      :key="item.id"
      class="grid-card-item"
      @click="handleCardClick(item)"
    >
      <!-- 图片区域 -->
      <div class="card-image-section">
        <img 
          :src="item.image" 
          :alt="item.title"
          class="card-image"
          @error="handleImageError($event, item)"
        />
        <!-- 右下角标识（替代视频时长） -->
        <div v-if="item.badge" class="card-badge">
          {{ item.badge }}
        </div>
      </div>

      <!-- 内容区域 -->
      <div class="card-content-section">
        <!-- 标题 -->
        <h3 class="card-title">{{ item.title }}</h3>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps, defineEmits } from 'vue';

// 卡片数据接口
export interface GridCardItem {
  id: string | number;
  title: string;
  image: string;
  badge?: string;           // 右下角标识（如"推荐"、"热门"等）
  [key: string]: any;       // 允许扩展字段
}

interface Props {
  items: GridCardItem[];
  clickable?: boolean;
  loading?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  clickable: true,
  loading: false
});

const emit = defineEmits<{
  cardClick: [item: GridCardItem];
  imageError: [item: GridCardItem];
}>();

// 处理卡片点击
const handleCardClick = (item: GridCardItem) => {
  if (props.clickable) {
    emit('cardClick', item);
  }
};

// 处理图片加载错误
const handleImageError = (event: Event, item: GridCardItem) => {
  const img = event.target as HTMLImageElement;
  img.src = '/default-image.png';
  emit('imageError', item);
};
</script>

<style scoped>
.grid-card-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
  padding: 0;
}

.grid-card-item {
  display: flex;
  flex-direction: column;
  background: #fff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  cursor: pointer;
}

.grid-card-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.grid-card-item:active {
  transform: translateY(-1px);
}

/* 图片区域 */
.card-image-section {
  position: relative;
  width: 100%;
  height: 120px;
  overflow: hidden;
}

.card-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.grid-card-item:hover .card-image {
  transform: scale(1.05);
}

/* 右下角标识 */
.card-badge {
  position: absolute;
  bottom: 8px;
  right: 8px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: 500;
  backdrop-filter: blur(4px);
}

/* 内容区域 */
.card-content-section {
  padding: 12px;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.card-title {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  line-height: 1.4;
  margin: 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  text-align: left;
}

/* 响应式调整 */
@media (max-width: 480px) {
  .grid-card-container {
    gap: 10px;
  }
  
  .card-image-section {
    height: 100px;
  }
  
  .card-content-section {
    padding: 10px;
  }
  
  .card-title {
    font-size: 13px;
  }
  
  .card-badge {
    bottom: 6px;
    right: 6px;
    font-size: 10px;
    padding: 1px 4px;
  }
}

@media (max-width: 320px) {
  .grid-card-container {
    gap: 8px;
  }
  
  .card-image-section {
    height: 80px;
  }
  
  .card-content-section {
    padding: 8px;
  }
  
  .card-title {
    font-size: 12px;
    -webkit-line-clamp: 1;
  }
}

/* 不可点击状态 */
.grid-card-item:not([clickable]) {
  cursor: default;
}

.grid-card-item:not([clickable]):hover {
  transform: none;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}
</style> 