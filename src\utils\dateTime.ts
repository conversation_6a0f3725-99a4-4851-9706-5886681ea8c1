/**
 * 时间格式化工具函数
 * 智能处理多种时间格式，包括：
 * - 标准格式：YYYY-MM-DD HH:mm:ss
 * - 不带年份：MM-DD HH:mm 或 MM/DD HH:mm
 * - 中文格式：12月25日 14:30 或 12月25日
 * - 纯日期：MM-DD 或 MM/DD
 * 
 * 对于不带年份的格式，自动使用当前年份补全
 */

/**
 * 智能解析时间字符串，支持多种格式
 * @param datetimeStr 时间字符串，支持多种格式
 * @returns Date对象，解析失败返回null
 */
const parseDateTime = (datetimeStr: string): Date | null => {
  if (!datetimeStr) return null;
  
  // 添加调试信息
  console.log('解析时间字符串:', datetimeStr);
  
  // 尝试直接解析标准格式
  let date = new Date(datetimeStr);
  if (!isNaN(date.getTime())) {
    console.log('直接解析成功:', date.getFullYear(), date.getMonth() + 1, date.getDate());
    // 检查年份是否合理，如果是2001年且原字符串不包含完整年份，可能是解析错误
    if (date.getFullYear() === 2001 && !datetimeStr.includes('2001')) {
      console.log('检测到可能的年份解析错误，尝试其他解析方式');
      // 继续执行下面的解析逻辑
    } else {
      return date;
    }
  }
  
  // 如果直接解析失败，尝试处理不带年份的格式
  const currentYear = new Date().getFullYear();
  
  // 处理格式：MM-DD HH:mm 或 MM-DD HH:mm:ss
  if (/^\d{1,2}-\d{1,2}\s+\d{1,2}:\d{1,2}(:\d{1,2})?$/.test(datetimeStr)) {
    const withYear = `${currentYear}-${datetimeStr}`;
    date = new Date(withYear);
    if (!isNaN(date.getTime())) {
      return date;
    }
  }
  
  // 处理格式：MM/DD HH:mm 或 MM/DD HH:mm:ss
  if (/^\d{1,2}\/\d{1,2}\s+\d{1,2}:\d{1,2}(:\d{1,2})?$/.test(datetimeStr)) {
    const withYear = `${currentYear}/${datetimeStr}`;
    date = new Date(withYear);
    if (!isNaN(date.getTime())) {
      return date;
    }
  }
  
  // 处理中文格式：12月25日 14:30
  if (/^\d{1,2}月\d{1,2}日\s+\d{1,2}:\d{1,2}$/.test(datetimeStr)) {
    const match = datetimeStr.match(/^(\d{1,2})月(\d{1,2})日\s+(\d{1,2}):(\d{1,2})$/);
    if (match) {
      const [, month, day, hour, minute] = match;
      date = new Date(currentYear, parseInt(month) - 1, parseInt(day), parseInt(hour), parseInt(minute));
      if (!isNaN(date.getTime())) {
        return date;
      }
    }
  }
  
  // 处理只有日期的格式：MM-DD 或 MM/DD
  if (/^\d{1,2}[-\/]\d{1,2}$/.test(datetimeStr)) {
    const withYear = datetimeStr.includes('-') ? `${currentYear}-${datetimeStr}` : `${currentYear}/${datetimeStr}`;
    date = new Date(withYear);
    if (!isNaN(date.getTime())) {
      return date;
    }
  }
  
  // 处理中文日期格式：12月25日
  if (/^\d{1,2}月\d{1,2}日$/.test(datetimeStr)) {
    const match = datetimeStr.match(/^(\d{1,2})月(\d{1,2})日$/);
    if (match) {
      const [, month, day] = match;
      date = new Date(currentYear, parseInt(month) - 1, parseInt(day));
      if (!isNaN(date.getTime())) {
        return date;
      }
    }
  }
  
  return null;
};

/**
 * 格式化datetime字符串为友好的显示格式
 * @param datetimeStr datetime字符串，支持多种格式
 * @param options 格式化选项
 * @returns 格式化后的时间字符串
 */
export const formatDateTime = (
  datetimeStr: string,
  options: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  }
): string => {
  if (!datetimeStr) return '';
  
  try {
    const date = parseDateTime(datetimeStr);
    if (!date) return datetimeStr; // 如果解析失败，返回原字符串
    
    return date.toLocaleDateString('zh-CN', options);
  } catch (error) {
    return datetimeStr; // 解析出错时返回原字符串
  }
};

/**
 * 格式化活动时间范围
 * @param startDate 开始时间 (datetime格式)
 * @param endDate 结束时间 (datetime格式)
 * @returns 格式化后的时间范围字符串
 */
export const formatActivityTimeRange = (startDate: string, endDate: string): string => {
  if (!startDate) return '时间待定';
  
  const startDateObj = parseDateTime(startDate);
  const endDateObj = endDate ? parseDateTime(endDate) : null;
  
  // 检查日期是否有效
  if (!startDateObj) {
    return '日期格式错误';
  }
  
  if (!endDate || startDate === endDate) {
    return formatDateTime(startDate, {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }
  
  if (endDateObj) {
    // 检查是否是同一天
    if (startDateObj.toDateString() === endDateObj.toDateString()) {
      // 同一天，只显示日期和时间范围
      const dateStr = startDateObj.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
      const startTime = startDateObj.toLocaleTimeString('zh-CN', {
        hour: '2-digit',
        minute: '2-digit'
      });
      const endTime = endDateObj.toLocaleTimeString('zh-CN', {
        hour: '2-digit',
        minute: '2-digit'
      });
      return `${dateStr} ${startTime}-${endTime}`;
    } else {
      // 不同天，显示完整的开始和结束时间
      const startFormatted = formatDateTime(startDate);
      const endFormatted = formatDateTime(endDate);
      return `${startFormatted} ~ ${endFormatted}`;
    }
  }
  
  return formatDateTime(startDate);
};

/**
 * 格式化报名截止时间
 * @param deadline 截止时间 (datetime格式)
 * @returns 格式化后的截止时间字符串
 */
export const formatDeadline = (deadline: string): string => {
  if (!deadline) return '暂无数据';
  
  return formatDateTime(deadline, {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
};