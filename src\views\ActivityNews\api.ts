import { get, post, requestWithRetry } from '../../api/request';
import { ACTIVITY_URLS, buildUrl } from '../../api/urls';


export function getActivityNewsList(params?: any) {
  return get<ActivityNewsListResponse>(ACTIVITY_URLS.NEWS, params);
}

export function getActivityNewsListlWithRetry(params?: any) {
  return requestWithRetry<ActivityNewsListResponse>(() => getActivityNewsList(params));
}



// 活动数据类型定义
export interface ActivityNewsItem {
  id: number;
  name: string;
  desc: string;
  thumbnail: string;
  viewtimes: string;
  create_time: string;
}
export interface ActivityNewsListResponse {
  results: ActivityNewsItem[];
  page: number;
  page_size: number;
  count: number;
  is_last_page: boolean;
  next: string | null;
  previous: string | null;
}