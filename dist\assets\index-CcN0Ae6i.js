import{s as M,v as R,G as $,a5 as z,d as E,u as F,a as G,r as y,k as D,l as V,c,o,b as i,g as s,L as W,w as l,H as U,j as m,t as u,i as g,f as _,F as j,e as q,I as A,_ as O}from"./index-BE8XLQ59.js";import{B as P}from"./index-jIY073my.js";import{T as Y}from"./index-CAfqjps3.js";import{E as J}from"./index-B27ddgxZ.js";import{G as K,a as Q}from"./GlobalHeader-lL88u8sR.js";/* empty css               */function X(p){return R($(z.NEWS,p))}function Z(p){return M(()=>X(p))}const ee={class:"news-detail-page"},te={key:0,class:"loading-container"},se={key:1,class:"error-container"},ae={key:2,class:"content"},ne={class:"section-container"},oe={class:"news-title"},ie={key:0,class:"news-tags"},le={class:"news-meta"},re={class:"meta-item"},ce={class:"meta-value"},ue={class:"meta-item"},de={class:"meta-value"},me={class:"news-image-container"},_e=["src","alt"],pe={class:"news-summary"},ve={class:"summary-header"},ye={class:"summary-content"},ge={key:0,class:"section-container"},fe={class:"section-header"},we={class:"section-title"},he=["innerHTML"],be=E({__name:"index",setup(p){F();const H=G(),f=y(!1),w=y(""),h=y(null),t=y({id:0,name:"",desc:"",thumbnail:"",content:"",create_time:"",viewtimes_display:"",cat_display:"",tags:[]}),T=D(()=>(parseInt(t.value.viewtimes_display)||0)>100),S=D(()=>{if(!t.value.create_time)return!1;const a=new Date(t.value.create_time),n=new Date().getTime()-a.getTime();return Math.ceil(n/(1e3*60*60*24))<=7}),x=D(()=>{const a=[];return t.value.cat_display?a.push(t.value.cat_display):a.push("医院新闻"),t.value.tags&&t.value.tags.length>0&&a.push(...t.value.tags),T.value&&a.push("热门"),S.value&&a.push("最新"),a}),B=a=>{if(!a)return"";const e=new Date(a),n=e.getFullYear(),v=String(e.getMonth()+1).padStart(2,"0"),b=String(e.getDate()).padStart(2,"0"),r=String(e.getHours()).padStart(2,"0"),d=String(e.getMinutes()).padStart(2,"0");return`${n}-${v}-${b} ${r}:${d}`},L=a=>{const e=["default","success","warning","danger"];return e[a%e.length]},N=async a=>{try{f.value=!0;const e=await Z(a);console.log("获取到医院新闻详情数据:",e);const n=e.hospital_news;t.value={id:n.id,name:n.name,desc:n.desc,thumbnail:n.thumbnail,content:n.content,create_time:n.create_time,viewtimes_display:n.viewtimes_display,cat_display:n.cat_display||"",tags:n.tags||[]}}catch(e){console.error("获取医院新闻详情失败:",e),w.value="获取医院新闻详情失败，请稍后再试"}finally{f.value=!1}};return V(()=>{h.value=Number(H.params.id),console.log("医院新闻详情ID:",h.value),N(h.value)}),(a,e)=>{const n=W,v=U,b=J,r=Y,d=A,C=P;return o(),c("div",ee,[i(K,{title:"医院新闻"}),f.value?(o(),c("div",te,[i(n,{type:"spinner",color:"#1989fa"}),e[1]||(e[1]=s("p",null,"加载中...",-1))])):w.value?(o(),c("div",se,[i(b,{description:"加载失败",image:"error"},{description:l(()=>[s("p",null,u(w.value),1)]),default:l(()=>[i(v,{round:"",type:"primary",onClick:e[0]||(e[0]=k=>N(t.value.id))},{default:l(()=>e[2]||(e[2]=[m("重试")])),_:1,__:[2]})]),_:1})])):(o(),c("div",ae,[s("div",ne,[s("h1",oe,u(t.value.name),1),x.value.length>0?(o(),c("div",ie,[t.value.cat_display?(o(),_(r,{key:0,type:"primary",size:"medium"},{default:l(()=>[m(u(t.value.cat_display),1)]),_:1})):(o(),_(r,{key:1,type:"primary",size:"medium"},{default:l(()=>e[3]||(e[3]=[m(" 医院新闻 ")])),_:1,__:[3]})),(o(!0),c(j,null,q(t.value.tags,(k,I)=>(o(),_(r,{key:k,type:L(I),size:"medium"},{default:l(()=>[m(u(k),1)]),_:2},1032,["type"]))),128)),T.value?(o(),_(r,{key:2,type:"danger",size:"medium"},{default:l(()=>e[4]||(e[4]=[m("🔥 热门")])),_:1,__:[4]})):g("",!0),S.value?(o(),_(r,{key:3,type:"warning",size:"medium"},{default:l(()=>e[5]||(e[5]=[m("✨ 最新")])),_:1,__:[5]})):g("",!0)])):g("",!0),s("div",le,[s("div",re,[i(d,{name:"clock-o"}),e[6]||(e[6]=s("span",{class:"meta-label"},"发布时间：",-1)),s("span",ce,u(B(t.value.create_time)),1)]),s("div",ue,[i(d,{name:"eye-o"}),e[7]||(e[7]=s("span",{class:"meta-label"},"浏览次数：",-1)),s("span",de,u(t.value.viewtimes_display),1)])]),s("div",me,[s("img",{src:t.value.thumbnail,alt:t.value.name,class:"news-image card-image-base image-hover-scale"},null,8,_e)]),s("div",pe,[s("div",ve,[i(d,{name:"info-o",color:"#1989fa"}),e[8]||(e[8]=s("span",null,"文章摘要",-1))]),s("div",ye,u(t.value.desc),1)])]),t.value.content?(o(),c("div",ge,[s("div",fe,[s("div",we,[i(d,{name:"description-o",color:"#4b8bf4"}),e[9]||(e[9]=s("span",null,"正文内容",-1))])]),s("div",{class:"news-content",innerHTML:t.value.content},null,8,he)])):g("",!0)])),i(C,{right:"16",bottom:"80"}),i(Q)])}}}),xe=O(be,[["__scopeId","data-v-c1502ac5"]]);export{xe as default};
