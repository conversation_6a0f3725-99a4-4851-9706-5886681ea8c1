<template>
  <div class="news-card" @click="handleClick">
    <div class="news-image-container">
      <img 
        :src="newsItem.thumbnail" 
        :alt="newsItem.name" 
        class="news-image"
        @error="handleImageError"
      />
      <div class="image-overlay"></div>
    </div>
    
    <div class="news-content">
      <div class="news-header">
        <van-tag 
          v-if="newsItem.category" 
          type="primary" 
          size="medium" 
          class="news-category"
        >
          {{ newsItem.category }}
        </van-tag>
        <span v-if="newsItem.publishDate" class="news-date">
          {{ formatDate(newsItem.publishDate) }}
        </span>
      </div>
      
      <h3 class="news-title">{{ newsItem.name }}</h3>
      
      <p v-if="newsItem.desc" class="news-description">
        {{ newsItem.desc }}
      </p>
      
      <div class="news-meta" v-if="showMeta">
        <div class="meta-item" v-if="newsItem.author">
          <van-icon name="user-o" size="12" />
          <span>{{ newsItem.author }}</span>
        </div>
        <div class="meta-item" v-if="newsItem.views">
          <van-icon name="eye-o" size="12" />
          <span>{{ newsItem.views }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps, defineEmits } from 'vue';

// 新闻项数据接口
export interface NewsItem {
  id: number | string;
  name: string;           // 标题
  desc?: string;          // 描述
  thumbnail: string;      // 缩略图
  category?: string;      // 分类
  publishDate?: string;   // 发布日期
  author?: string;        // 作者
  views?: number;         // 浏览量
}

interface Props {
  newsItem: NewsItem;
  showMeta?: boolean;     // 是否显示元信息（作者、浏览量等）
  clickable?: boolean;    // 是否可点击
}

const props = withDefaults(defineProps<Props>(), {
  showMeta: false,
  clickable: true
});

const emit = defineEmits<{
  click: [item: NewsItem];
  imageError: [item: NewsItem];
}>();

// 处理点击事件
const handleClick = () => {
  if (props.clickable) {
    emit('click', props.newsItem);
  }
};

// 处理图片加载错误
const handleImageError = () => {
  emit('imageError', props.newsItem);
};

// 格式化日期
const formatDate = (dateString: string): string => {
  try {
    const date = new Date(dateString);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}.${month}.${day}`;
  } catch {
    return dateString;
  }
};
</script>

<style scoped>
.news-card {
  display: flex;
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
  cursor: pointer;
  margin-bottom: 12px;
}

.news-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.news-card:active {
  transform: translateY(0);
}

.news-image-container {
  position: relative;
  width: 100px;
  height: 80px;
  flex-shrink: 0;
  overflow: hidden;
}

.news-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.news-card:hover .news-image {
  transform: scale(1.05);
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, transparent 0%, rgba(0, 0, 0, 0.1) 100%);
}

.news-content {
  flex: 1;
  padding: 12px 14px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  min-width: 0;
}

.news-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 6px;
}

.news-category {
  font-size: 11px;
  padding: 2px 6px;
  border-radius: 4px;
  background: #e8f4ff;
  color: #1989fa;
  border: none;
}

.news-date {
  font-size: 11px;
  color: #999;
  font-weight: 400;
}

.news-title {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  line-height: 1.4;
  margin: 0 0 6px 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.news-description {
  font-size: 12px;
  color: #666;
  line-height: 1.4;
  margin: 0 0 8px 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1;
}

.news-meta {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-top: auto;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 11px;
  color: #999;
}

.meta-item .van-icon {
  color: #ccc;
}

/* 响应式调整 */
@media (max-width: 480px) {
  .news-image-container {
    width: 80px;
    height: 64px;
  }
  
  .news-content {
    padding: 10px 12px;
  }
  
  .news-title {
    font-size: 13px;
  }
  
  .news-description {
    font-size: 11px;
  }
  
  .news-category {
    font-size: 10px;
    padding: 1px 4px;
  }
  
  .news-date {
    font-size: 10px;
  }
}

@media (max-width: 320px) {
  .news-image-container {
    width: 70px;
    height: 56px;
  }
  
  .news-content {
    padding: 8px 10px;
  }
  
  .news-title {
    font-size: 12px;
    -webkit-line-clamp: 1;
  }
  
  .news-description {
    font-size: 10px;
    -webkit-line-clamp: 1;
  }
}

/* 无点击状态 */
.news-card:not([clickable]) {
  cursor: default;
}

.news-card:not([clickable]):hover {
  transform: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}
</style> 