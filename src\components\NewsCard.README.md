# NewsCard 全局新闻卡片组件

## 概述

NewsCard 是一个全局的新闻卡片组件，用于展示新闻信息。组件采用横向布局，左侧显示缩略图，右侧显示新闻内容，包括分类标签、标题、描述、日期等信息。

## 功能特性

- ✅ **响应式设计**: 支持移动端和桌面端适配
- ✅ **灵活配置**: 支持显示/隐藏元信息、可点击/不可点击状态
- ✅ **交互效果**: 悬停动画、点击反馈
- ✅ **图片处理**: 支持图片加载错误处理
- ✅ **日期格式化**: 自动格式化日期显示
- ✅ **文本截断**: 标题和描述支持多行截断
- ✅ **TypeScript**: 完整的类型定义支持

## 组件位置

```
zyy-front/src/components/NewsCard.vue
```

## 数据接口

```typescript
interface NewsItem {
  id: number | string;
  name: string; // 标题
  desc?: string; // 描述
  thumbnail: string; // 缩略图
  category?: string; // 分类
  publishDate?: string; // 发布日期
  author?: string; // 作者
  views?: number; // 浏览量
}
```

## Props

| 参数      | 类型     | 默认值 | 说明                           |
| --------- | -------- | ------ | ------------------------------ |
| newsItem  | NewsItem | -      | 新闻数据对象                   |
| showMeta  | boolean  | false  | 是否显示元信息（作者、浏览量） |
| clickable | boolean  | true   | 是否可点击                     |

## Events

| 事件名     | 参数             | 说明               |
| ---------- | ---------------- | ------------------ |
| click      | (item: NewsItem) | 点击卡片时触发     |
| imageError | (item: NewsItem) | 图片加载失败时触发 |

## 基础用法

```vue
<template>
  <NewsCard :news-item="newsItem" @click="handleNewsClick" />
</template>

<script setup lang="ts">
import NewsCard, { type NewsItem } from "@/components/NewsCard.vue";

const newsItem: NewsItem = {
  id: 1,
  name: "我院成功举办中医药学术大会",
  desc: "本次大会汇聚了全国知名中医专家，共同探讨中医药发展新方向...",
  thumbnail: "/images/news-1.jpg",
  category: "医院新闻",
  publishDate: "2023-01-01",
};

const handleNewsClick = (item: NewsItem) => {
  console.log("点击了新闻:", item);
  // 跳转到详情页
  router.push(`/news/${item.id}`);
};
</script>
```

## 显示元信息

```vue
<template>
  <NewsCard :news-item="newsItem" :show-meta="true" @click="handleNewsClick" />
</template>

<script setup lang="ts">
const newsItem: NewsItem = {
  id: 2,
  name: "中医养生：春季如何调理身体",
  desc: "春季是万物复苏的季节，也是调理身体的最佳时机...",
  thumbnail: "/images/news-2.jpg",
  category: "养生知识",
  publishDate: "2023-01-02",
  author: "张医生",
  views: 1234,
};
</script>
```

## 新闻列表

```vue
<template>
  <div class="news-list">
    <NewsCard
      v-for="item in newsList"
      :key="item.id"
      :news-item="item"
      @click="handleNewsClick"
    />
  </div>
</template>

<script setup lang="ts">
const newsList: NewsItem[] = [
  // ... 新闻数据
];
</script>

<style scoped>
.news-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}
</style>
```

## 不可点击状态

```vue
<template>
  <NewsCard :news-item="newsItem" :clickable="false" />
</template>
```

## 样式定制

组件使用 scoped 样式，如需自定义样式，可以通过以下方式：

```vue
<template>
  <div class="custom-news-container">
    <NewsCard :news-item="newsItem" />
  </div>
</template>

<style>
.custom-news-container .news-card {
  /* 自定义样式 */
  border: 1px solid #e0e0e0;
  margin-bottom: 16px;
}
</style>
```

## 响应式断点

- **移动端** (≤480px): 缩略图 80x64px，字体适当缩小
- **小屏手机** (≤320px): 缩略图 70x56px，标题和描述单行显示

## 注意事项

1. **图片尺寸**: 建议缩略图比例为 5:4，确保显示效果最佳
2. **文本长度**: 标题建议不超过 30 字符，描述不超过 100 字符
3. **日期格式**: 支持标准日期字符串，会自动格式化为 YYYY.MM.DD 格式
4. **性能优化**: 在长列表中使用时，建议配合虚拟滚动组件

## 更新日志

### v1.0.0 (2023-12-XX)

- 初始版本发布
- 支持基础新闻卡片展示
- 响应式设计
- TypeScript 支持
