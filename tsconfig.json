{
  "compilerOptions": {
    "target": "ESNext",
    "module": "ESNext",
    "outDir": "./types",        // 👈 所有输出都放到这个目录
    "rootDir": "./src",         // 👈 明确源码目录
    "moduleResolution": "Node",
    "strict": true,
    "jsx": "preserve",
    "lib": ["ESNext", "DOM"],
    "esModuleInterop": true,
    "skipLibCheck": true,
    "types": ["vite/client", "node"],
    "baseUrl": ".",
    "paths": {
      "@/*": ["src/*"]
    }
  },
  "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue"]
}
