/* 活动详情页样式 */
.activity-detail-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding-bottom: 80px;
}

/* 移除自定义导航栏样式，使用全局页头 */

/* 加载和错误状态 */
.loading-container, .error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  padding: 20px;
}

.loading-container p {
  margin-top: 16px;
  color: #666;
  font-size: 14px;
}

/* 主要内容区域 */
.content {
  padding-top: 20px;
}

/* 活动封面图 */
.activity-hero {
  position: relative;
  width: calc(100% - 32px);
  height: 240px;
  overflow: hidden;
  margin: 0 auto;
  border-radius: 16px;
}

.hero-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 16px;
}

.hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to bottom, transparent 0%, rgba(0, 0, 0, 0.3) 100%);
  display: flex;
  align-items: flex-end;
  padding: 20px;
}

.activity-status {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  color: white;
  backdrop-filter: blur(10px);
}

.activity-status.upcoming {
  background: rgba(75, 139, 244, 0.9);
}

.activity-status.ongoing {
  background: rgba(16, 172, 132, 0.9);
}

.activity-status.ended {
  background: rgba(156, 163, 175, 0.9);
}

/* 活动主题区域 */
.activity-theme-section {
  padding: 20px 16px;
  background: white;
  margin: 20px 16px 0 16px;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 10;
}

.activity-title {
  font-size: 24px;
  font-weight: 700;
  color: #1a1a1a;
  margin: 0 0 8px 0;
  line-height: 1.3;
}

.activity-subtitle {
  font-size: 16px;
  color: #666;
  margin-bottom: 16px;
  line-height: 1.5;
}

.activity-stats {
  display: flex;
  gap: 20px;
  margin-top: 16px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  color: #666;
}

.stat-item .van-icon {
  color: #4b8bf4;
}

/* 活动时间地点信息容器 */
.activity-info-container {
  background: white;
  margin: 20px 16px 0 16px;
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-icon {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  font-size: 18px;
}

.info-icon.time-icon {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.info-icon.location-icon {
  background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
  color: white;
}

.info-content {
  flex: 1;
}

.info-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 4px;
}

.info-value {
  font-size: 16px;
  color: #333;
  font-weight: 500;
}

/* 信息卡片 */
.info-cards {
  padding: 0 16px;
  margin-top: 20px;
}

.info-card {
  display: flex;
  align-items: center;
  background: white;
  padding: 16px;
  border-radius: 12px;
  margin-bottom: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.info-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
}

.card-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 20px;
}

.time-icon {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.location-icon {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  color: white;
}

.target-icon {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;
}

.card-content {
  flex: 1;
}

.card-title {
  font-size: 14px;
  color: #666;
  margin-bottom: 4px;
}

.card-value {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  line-height: 1.4;
}

/* 内容区域 */
.content-sections {
  padding: 20px 16px;
}

.content-section {
  background: white;
  border-radius: 16px;
  margin-bottom: 20px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.section-header {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 20px 20px 16px;
  border-bottom: 1px solid #f0f0f0;
}

.section-header h2 {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.section-content {
  padding: 20px;
}

.section-content p {
  font-size: 16px;
  line-height: 1.6;
  color: #555;
  margin: 0;
}

/* 时间轴样式 */
.timeline {
  position: relative;
}

.timeline-item {
  display: flex;
  margin-bottom: 24px;
  position: relative;
}

.timeline-item:last-child {
  margin-bottom: 0;
}

.timeline-item::before {
  content: '';
  position: absolute;
  left: 11px;
  top: 24px;
  bottom: -24px;
  width: 2px;
  background: linear-gradient(to bottom, #4b8bf4, #e3f2fd);
}

.timeline-item:last-child::before {
  display: none;
}

.timeline-dot {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: linear-gradient(135deg, #4b8bf4, #1976d2);
  margin-right: 16px;
  flex-shrink: 0;
  box-shadow: 0 2px 8px rgba(75, 139, 244, 0.3);
}

.timeline-content {
  flex: 1;
  padding-top: 2px;
}

.step-time {
  font-size: 14px;
  font-weight: 600;
  color: #4b8bf4;
  margin-bottom: 4px;
}

.step-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.step-desc {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
}

/* 富文本内容 */
.rich-content {
  font-size: 16px;
  line-height: 1.8;
  color: #555;
}

.rich-content img {
  max-width: 100%;
  height: auto;
  border-radius: 8px;
  margin: 12px 0;
}

.rich-content p {
  margin-bottom: 16px;
}

.rich-content h1, .rich-content h2, .rich-content h3 {
  color: #333;
  margin: 24px 0 16px;
}

/* 注意事项列表 */
.notice-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.notice-list li {
  position: relative;
  padding: 12px 0 12px 24px;
  font-size: 15px;
  line-height: 1.6;
  color: #555;
  border-bottom: 1px solid #f5f5f5;
}

.notice-list li:last-child {
  border-bottom: none;
}

.notice-list li::before {
  content: '•';
  position: absolute;
  left: 0;
  top: 12px;
  color: #ff8c69;
  font-weight: bold;
  font-size: 16px;
}

/* 报名表单 */
.registration-section {
  background: white;
  border-radius: 16px;
  margin: 20px 16px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.registration-form {
  padding: 0 20px 20px;
}

.registration-form .van-field {
  margin-bottom: 16px;
  border-radius: 12px;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  transition: all 0.3s ease;
}

.registration-form .van-field:focus-within {
  border-color: #4b8bf4;
  box-shadow: 0 0 0 3px rgba(75, 139, 244, 0.1);
}

/* 底部操作栏 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 16px;
  border-top: 1px solid #e9ecef;
  display: flex;
  gap: 12px;
  z-index: 1000;
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
}

.action-btn {
  flex: 1;
  height: 48px;
  border-radius: 24px;
  font-size: 16px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.action-btn.secondary {
  background: #f8f9fa;
  color: #666;
  border: 1px solid #e9ecef;
}

.action-btn.secondary:hover {
  background: #e9ecef;
  transform: translateY(-1px);
}

.action-btn.primary {
  background: linear-gradient(135deg, #4b8bf4 0%, #1976d2 100%);
  border: none;
  box-shadow: 0 4px 15px rgba(75, 139, 244, 0.3);
}

.action-btn.primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(75, 139, 244, 0.4);
}

.action-btn.primary:disabled {
  background: #ccc;
  box-shadow: none;
  transform: none;
}

/* 响应式设计 */
@media (max-width: 375px) {
  .activity-title {
    font-size: 20px;
  }
  
  .info-card {
    padding: 12px;
  }
  
  .card-icon {
    width: 40px;
    height: 40px;
    font-size: 18px;
  }
  
  .section-content {
    padding: 16px;
  }
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.content-section {
  animation: fadeInUp 0.6s ease-out;
}

.info-card {
  animation: fadeInUp 0.6s ease-out;
}

.activity-theme-section {
  animation: fadeInUp 0.6s ease-out;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 4px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 2px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
