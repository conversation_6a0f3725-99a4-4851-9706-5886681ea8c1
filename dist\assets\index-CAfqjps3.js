import{d as S,a9 as k,R as B,S as I,b as l,b5 as P,b6 as T,I as h,X as v}from"./index-BE8XLQ59.js";const[x,s]=I("tag"),y={size:String,mark:Boolean,show:B,type:k("default"),color:String,plain:Boolean,round:Boolean,textColor:String,closeable:Boolean};var w=S({name:x,props:y,emits:["close"],setup(o,{slots:n,emit:r}){const c=a=>{a.stopPropagation(),r("close",a)},i=()=>o.plain?{color:o.textColor||o.color,borderColor:o.color}:{color:o.textColor,background:o.color},d=()=>{var a;const{type:u,mark:m,plain:g,round:C,size:e,closeable:b}=o,t={mark:m,plain:g,round:C};e&&(t[e]=e);const f=b&&l(h,{name:"cross",class:[s("close"),T],onClick:c},null);return l("span",{style:i(),class:s([t,u])},[(a=n.default)==null?void 0:a.call(n),f])};return()=>l(P,{name:o.closeable?"van-fade":void 0},{default:()=>[o.show?d():null]})}});const p=v(w);export{p as T};
