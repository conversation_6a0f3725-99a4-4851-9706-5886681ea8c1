#!/bin/bash

# 中医智慧前端项目 - 阿里云Ubuntu20服务器部署脚本
echo "🏥 中医智慧前端项目 - 阿里云Ubuntu20服务器部署"
echo "=================================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 检查是否为root用户
if [ "$EUID" -ne 0 ]; then
    echo -e "${RED}请使用sudo运行此脚本${NC}"
    exit 1
fi

echo -e "${BLUE}步骤1: 更新系统包${NC}"
apt update && apt upgrade -y

echo -e "${BLUE}步骤2: 安装必要的系统依赖${NC}"
apt install -y curl wget git build-essential

echo -e "${BLUE}步骤3: 安装Node.js 18.x${NC}"
# 添加NodeSource仓库
curl -fsSL https://deb.nodesource.com/setup_18.x | bash -

# 安装Node.js
apt install -y nodejs

# 验证安装
echo -e "${GREEN}Node.js版本:${NC}"
node --version
echo -e "${GREEN}npm版本:${NC}"
npm --version

echo -e "${BLUE}步骤4: 安装Nginx${NC}"
apt install -y nginx

echo -e "${BLUE}步骤5: 启动并启用Nginx${NC}"
systemctl start nginx
systemctl enable nginx

echo -e "${BLUE}步骤6: 配置防火墙${NC}"
# 允许HTTP和HTTPS流量
ufw allow 'Nginx Full'
ufw allow OpenSSH
ufw --force enable

echo -e "${GREEN}✅ 服务器环境准备完成！${NC}"
echo ""
echo -e "${YELLOW}接下来请按以下步骤操作：${NC}"
echo "1. 将项目文件上传到服务器"
echo "2. 运行项目构建脚本"
echo "3. 配置Nginx"
echo ""
echo -e "${BLUE}Nginx状态:${NC}"
systemctl status nginx --no-pager -l

echo -e "${BLUE}服务器IP地址:${NC}"
curl -s http://checkip.amazonaws.com || echo "无法获取公网IP" 