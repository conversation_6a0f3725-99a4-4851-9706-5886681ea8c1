import { get, requestWithRetry } from '../../api/request';
import { KNOWLEDGE_URLS, buildUrl } from '../../api/urls';

/**
 * 获取分类
 * @returns 分类数据
 */
export function getTcmKnowledgeCategories() {
  return get<TcmKnowledgeCategoriesResponse>(KNOWLEDGE_URLS.CATEGORIES);
}

/**
 * 根据分类获取列表
 * @param cat 分类ID
 * @param page 页码
 * @param pageSize 每页数量
 * @returns 列表数据
 */
export function getTcmKnowledgeByCategory(cat: string, page: number = 1, pageSize: number = 10) {
  return get<TcmKnowledgeListResponse>(KNOWLEDGE_URLS.KNOWLWDGE, {
    cat,
    page,
    page_size: pageSize
  });
}

/**
 * 带重试功能的获取分类
 * 在网络不稳定情况下使用
 * @returns 分类数据
 */

export function getTcmKnowledgeCategoriesWithRetry(cat: string, page: number = 1, pageSize: number = 10) {
  return requestWithRetry<TcmKnowledgeListResponse>(() => getTcmKnowledgeByCategory(
    cat,
    page,
    pageSize
  ));
}

// 分类接口
export interface TcmKnowledgeCategory {
  value: string;
  label: string;
}

// 分类响应接口
export interface TcmKnowledgeCategoriesResponse {
  categories: TcmKnowledgeCategory[];
}

// 信息接口
export interface TcmKnowledgeItem {
  id: number;
  name: string;
  title: string;
  job: string;
  cat_display: string;
  desc: string;
  thumbnail: string;
  category: string;
  tags?: string[];
}

// 列表响应接口
export interface TcmKnowledgeListResponse {
  results: TcmKnowledgeItem[];
  page: number;
  page_size: number;
  total: number;
  is_last_page: boolean;
}