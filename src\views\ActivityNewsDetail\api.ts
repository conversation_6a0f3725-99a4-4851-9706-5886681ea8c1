import { get, post, requestWithRetry } from '../../api/request';
import { ACTIVITY_URLS, buildUrl } from '../../api/urls';


export function getActivityNewsDetail(id: string | number) {
  return get<ActivityNewsDetailResponse>(buildUrl(ACTIVITY_URLS.NEWS, id));
}

export function getActivityDetailWithRetry(id: string | number) {
  return requestWithRetry<ActivityNewsDetailResponse>(() => getActivityNewsDetail(id));
}

export interface ActivityNewsDetailResponse {
  activity_news: {
    id: number;          
    name: string;          
    desc: string;        
    thumbnail: string;          
    content: string; 
    create_time: string;    
    viewtimes_display: string;
    cat_display?: string;
    tags?: string[];
  }
}
export interface ActivityNewsDetail {
    id: number;          
    name: string;          
    desc: string;        
    thumbnail: string;          
    content: string; 
    create_time: string;    
    viewtimes_display: string;
    cat_display?: string;
    tags?: string[];
}