<template>
  <div class="section-container">
    <SectionHeader title="医院新闻" icon="description-o" moreLink="/hospital-news-list" :showMore="true" />

    <!-- 加载状态 -->
    <div v-if="loading && !newsCardItems.length" class="loading-container">
      <van-loading size="24px">加载中...</van-loading>
    </div>

    <!-- 空状态 -->
    <van-empty v-else-if="!newsCardItems.length" description="暂无新闻" />

    <!-- 新闻列表 -->
    <div v-else class="news-list">
      <NewsCard 
        v-for="item in newsCardItems" 
        :key="item.id"
        :news-item="item"
        @click="handleNewsClick"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { useRouter } from 'vue-router';
import { showToast } from 'vant';
import { getHospitalNewsList } from '../api';
import SectionHeader from '../../../components/SectionHeader.vue';
import NewsCard from '../../../components/NewsCard.vue';
import type { HospitalNewsItem, NewsCardData } from '../../../types/news';
import { transformHospitalNewsListToNewsCard } from '../../../utils/newsTransform';

const router = useRouter();
const loading = ref<boolean>(false);
const error = ref<string>("");
const refreshing = ref<boolean>(false);
const pageSize = ref<number>(4);
const finished = ref<boolean>(false);

// 原始API数据
const items = ref<HospitalNewsItem[]>([]);

// 转换为NewsCard组件需要的数据格式
const newsCardItems = computed<NewsCardData[]>(() => {
  return transformHospitalNewsListToNewsCard(items.value);
});

// 加载列表数据
const loadItems = async () => {
  if (loading.value) return;
  loading.value = true;

  try {
    const res = await getHospitalNewsList();
    console.log('获取到医院新闻列表数据:', res);

    // 检查响应数据结构
    if (!res || !res.results || !Array.isArray(res.results)) {
      console.error('API响应格式不正确:', res);
      items.value = [];
      return;
    }

    console.log('响应数据项数量:', res.results.length);

    // 直接使用API返回的数据，不需要额外格式化
    items.value = res.results;
    console.log('设置的原始数据:', items.value);
    console.log('转换后的NewsCard数据:', newsCardItems.value);

  } catch (err) {
    console.error('获取医院新闻失败:', err);
    error.value = "获取医院新闻失败，请稍后再试";
    showToast('获取新闻失败，请稍后重试');
  } finally {
    loading.value = false;
    refreshing.value = false;
  }
};

// 处理新闻卡片点击
const handleNewsClick = (item: NewsCardData) => {
  console.log('点击了新闻:', item);
  router.push({ name: 'HospitalNewsDetail', params: { id: item.id } });
};

// 初始化
onMounted(() => {
  loadItems();
});
</script>

<style scoped>
/* 区块通用样式 */
.section-container {
  padding: 5px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

/* 加载状态 */
.loading-container {
  padding: 30px 0;
  text-align: center;
}

/* 新闻列表 */
.news-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

/* 响应式调整 */
@media (max-width: 480px) {
  .news-list {
    gap: 10px;
  }
}

@media (max-width: 320px) {
  .news-list {
    gap: 8px;
  }
}
</style>
