import{s as D,v as y,aw as C,d as M,u as I,r as o,y as x,l as B,c as _,o as m,b as u,w as g,F as E,e as R,f as G,g as b,_ as L}from"./index-BE8XLQ59.js";import{T as N,a as A}from"./index-CmzFzWyS.js";import"./index-sbxbYdRt.js";import{G as F,a as S}from"./GlobalHeader-lL88u8sR.js";import{C as V}from"./CommonCardList-1yAjUs8b.js";import"./index-B27ddgxZ.js";import"./index-ouF_E0m0.js";import"./index-CAfqjps3.js";function z(){return y(C.CATEGORIES)}function P(i,n=1,t=10){return y(C.MAINDEPT,{cat:i,page:n,page_size:t})}function U(i,n=1,t=10){return D(()=>P(i,n,t))}const W={class:"news-page category-tabs-container"},q={class:"section-container"},H={class:"news-grid"},O=M({__name:"index",setup(i){const n=I(),t=o(!1),r=o(!1),d=o([]),p=o(1),l=o(0),h=o(5),v=o([]),w=async()=>{const e=await z();console.log("获取到领导分类sdfsdfsd:",e);const s=e.categories.map(a=>({id:a.value,name:a.label}));v.value=[...s]};x(l,e=>{p.value=1,d.value=[],r.value=!1,f()});const f=async()=>{var s;if(r.value||t.value)return;t.value=!0;const e=(s=v.value[l.value])==null?void 0:s.id;console.log("当前选中标签ID:",e);try{const a=await U(e,p.value,h.value);d.value.push(...a.results),p.value+=1,r.value=a.is_last_page===!0}catch(a){console.error("加载失败:",a)}finally{t.value=!1}},T=e=>{n.push({name:"MainDeptDetail",params:{id:e.id}})};return B(()=>{w(),f()}),(e,s)=>{const a=N,k=A;return m(),_("div",W,[u(F,{title:"重点科室"}),u(k,{active:l.value,"onUpdate:active":s[0]||(s[0]=c=>l.value=c),class:"category-tabs",sticky:"","sticky-offset-top":0,swipeable:""},{default:g(()=>[(m(!0),_(E,null,R(v.value,c=>(m(),G(a,{key:c.id,title:c.name},{default:g(()=>[b("div",q,[b("div",H,[u(V,{items:d.value,loading:t.value,finished:r.value,"use-infinite-scroll":!0,onLoadMore:f,onCardClick:T},null,8,["items","loading","finished"])])])]),_:2},1032,["title"]))),128))]),_:1},8,["active"]),u(S)])}}}),ee=L(O,[["__scopeId","data-v-03d5226f"]]);export{ee as default};
