import{s as k,v as b,M as y,d as E,u as T,r as o,y as x,l as B,c as _,o as f,b as u,w as g,F as R,e as D,f as G,g as C,_ as L}from"./index-BE8XLQ59.js";import{T as N,a as F}from"./index-CmzFzWyS.js";import"./index-sbxbYdRt.js";import{G as S,a as V}from"./GlobalHeader-lL88u8sR.js";import{C as z}from"./CommonCardList-1yAjUs8b.js";import"./index-B27ddgxZ.js";import"./index-ouF_E0m0.js";import"./index-CAfqjps3.js";function U(){return b(y.CATEGORIES)}function W(i,n=1,t=10){return b(y.MEDICINE,{cat:i,page:n,page_size:t})}function q(i,n=1,t=10){return k(()=>W(i,n,t))}const A={class:"news-page category-tabs-container"},H={class:"section-container"},O={class:"news-grid"},j=E({__name:"index",setup(i){const n=T(),t=o(!1),r=o(!1),d=o([]),v=o(1),l=o(0),h=o(5),m=o([]),w=async()=>{const e=await U();console.log("获取到领导分类sdfsdfsd:",e);const s=e.categories.map(a=>({id:a.value,name:a.label}));m.value=[...s]};x(l,e=>{v.value=1,d.value=[],r.value=!1,p()});const p=async()=>{var s;if(r.value||t.value)return;t.value=!0;const e=(s=m.value[l.value])==null?void 0:s.id;console.log("当前选中标签ID:",e);try{const a=await q(e,v.value,h.value);d.value.push(...a.results),v.value+=1,r.value=a.is_last_page===!0}catch(a){console.error("加载失败:",a)}finally{t.value=!1}},I=e=>{n.push({name:"MedicineDetail",params:{id:e.id}})};return B(()=>{w(),p()}),(e,s)=>{const a=N,M=F;return f(),_("div",A,[u(S,{title:"特色制剂"}),u(M,{active:l.value,"onUpdate:active":s[0]||(s[0]=c=>l.value=c),class:"category-tabs",sticky:"","sticky-offset-top":0,swipeable:""},{default:g(()=>[(f(!0),_(R,null,D(m.value,c=>(f(),G(a,{key:c.id,title:c.name},{default:g(()=>[C("div",H,[C("div",O,[u(z,{items:d.value,loading:t.value,finished:r.value,"use-infinite-scroll":!0,onLoadMore:p,onCardClick:I},null,8,["items","loading","finished"])])])]),_:2},1032,["title"]))),128))]),_:1},8,["active"]),u(V)])}}}),ee=L(j,[["__scopeId","data-v-b6d37310"]]);export{ee as default};
