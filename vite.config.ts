import { fileURLToPath, URL } from 'node:url'; // 新增导入
import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import * as AutoImport from 'unplugin-auto-import/vite';
import * as Components from 'unplugin-vue-components/vite';
import { VantResolver } from '@vant/auto-import-resolver';

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  // 加载环境变量
  const env = loadEnv(mode, process.cwd(), '');
  
  return {
  plugins: [
    vue(),
    AutoImport.default({
      resolvers: [VantResolver()],
    }),
    Components.default({
      resolvers: [VantResolver()],
    }),
  ],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url))
    }
  },
  server: {
    proxy: {
      // 代理所有 /api 开头的请求
      '/api': {
        target: env.VITE_PROXY_TARGET || 'https://houma.sxaliyun.cn',
        changeOrigin: true,
        secure: false,
        rewrite: (path) => path
      }
    }
  }
  };
});
