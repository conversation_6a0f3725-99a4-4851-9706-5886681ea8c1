import {
  createRouter,
  createWebHistory,
  createWebHashHistory,
} from "vue-router";

const routes = [
  {
    path: "/",
    redirect: "/home", // 默认重定向到首页
  },
  {
    path: "/home",
    name: "Home",
    component: () => import("../views/Home/index.vue"),
  },
  {
    path: "/leader/:type",
    name: "Leader",
    component: () => import("../views/Leader/index.vue"),
  },
  {
    path: "/leader-detail/:id",
    name: "LeaderDetail",
    component: () => import("../views/LeaderDetail/index.vue"),
  },
  {
    path: "/doctor/:type",
    name: "Doctor",
    component: () => import("../views/Doctor/index.vue"),
  },
  {
    path: "/doctor-detail/:id",
    name: "DoctorDetail",
    component: () => import("../views/DoctorDetail/index.vue"),
  },
  {
    path: "/medicine/:type",
    name: "Medicine",
    component: () => import("../views/Medicine/index.vue"),
  },
  {
    path: "/medicine-detail/:id",
    name: "MedicineDetail",
    component: () => import("../views/MedicineDetail/index.vue"),
  },
  {
    path: "/dept/:type",
    name: "Dept",
    component: () => import("../views/Dept/index.vue"),
  },
  {
    path: "/dept-detail/:id",
    name: "DeptDetail",
    component: () => import("../views/DeptDetail/index.vue"),
  },
  // 科教图文模块
  {
    path: "/education-media",
    name: "EducationMedia",
    component: () => import("../views/EducationMedia/index.vue"),
  },
  {
    path: "/education-detail/:id",
    name: "EducationDetail",
    component: () => import("../views/EducationMediaDetail/index.vue"),
  },
// 中医文化模块
  {
    path: "/culture",
    name: "Culture",
    component: () => import("../views/Culture/index.vue"),
  },
  {
    path: "/culture-detail/:id",
    name: "CultureDetail",
    component: () => import("../views/CultureDetail/index.vue"),
  },
  // 中医案例模块
  {
    path: "/cases",
    name: "Cases",
    component: () => import("../views/Cases/index.vue"),
  },
  {
    path: "/cases-detail/:id",
    name: "CasesDetail",
    component: () => import("../views/CasesDetail/index.vue"),
  },
// 视频宣传模块
  {
    path: "/video",
    name: "Video",
    component: () => import("../views/Video/index.vue"),
  },
  {
    path: "/video-detail/:id",
    name: "VideoDetail",
    component: () => import("../views/VideoDetail/index.vue"),
  },
// 中医知识模块
  {
    path: "/tcm-knowledge",
    name: "TcmKnowledge",
    component: () => import("../views/TcmKnowledge/index.vue"),
  },
  {
    path: "/tcm-knowledge-detail/:id",
    name: "TcmKnowledgeDetail",
    component: () => import("../views/TcmKnowledgeDetail/index.vue"),
  },
  {
    path: "/knowledge",
    name: "Knowledge",
    component: () => import("../views/Knowledge/index.vue"),
  },
  {
    path: "/activity",
    name: "Activity",
    component: () => import("../views/Activity/index.vue"),
  },
  {
    path: "/activity-list",
    name: "ActivityList",
    component: () => import("../views/Activity/list.vue"),
  },
  {
    path: "/activity-detail/:id",
    name: "ActivityDetail",
    component: () => import("../views/ActivityDetail/index.vue"),
  },
  {
    path: "/activity-registration/:id",
    name: "ActivityRegistration",
    component: () => import("../views/ActivityRegistration/index.vue"),
  },
  {
    path: "/activity-news-list",
    name: "ActivityNewsList",
    component: () => import("../views/ActivityNews/list.vue"),
  },
  {
    path: "/activity-news-detail/:id",
    name: "ActivityNewsDetail",
    component: () => import("../views/ActivityNewsDetail/index.vue"),
  },
  {
    path: "/activity-regist/:id",
    name: "ActivityRegist",
    component: () => import("../views/ActivityRegistration/index.vue"),
  },
  {
    path: "/maindept/:type",
    name: "MainDept",
    component: () => import("../views/MainDept/index.vue"),
  },
  {
    path: "/maindept-detail/:id",
    name: "MainDeptDetail",
    component: () => import("../views/MainDeptDetail/index.vue"),
  },

  // 医院新闻
  {
    path: "/hospital-news-list",
    name: "HospitalNewsList", 
    component: () => import("../views/News/list.vue"),
  },
  {
    path: "/hospital-news-detail/:id",
    name: "HospitalNewsDetail",
    component: () => import("../views/NewsDetail/index.vue"),
  },


];

const router = createRouter({
  history: createWebHistory(), // 使用 History 模式以支持微信分享
  routes,
});

export default router;
