import{v as a,av as g,s as m,G as d}from"./index-BE8XLQ59.js";function h(t){return a(g.ACTIVITY,t)}function f(t){return m(()=>h(t))}function $(t){return f({...t,param:"current"})}function y(t){return f({...t,param:"past"})}function N(t){return f({...t,param:"upcoming"})}function p(t){return a(d(g.ACTIVITY,t))}function I(t){return a(g.HOME_CONTENT,t)}const c=t=>{if(!t)return null;console.log("解析时间字符串:",t);let n=new Date(t);if(!isNaN(n.getTime()))if(console.log("直接解析成功:",n.getFullYear(),n.getMonth()+1,n.getDate()),n.getFullYear()===2001&&!t.includes("2001"))console.log("检测到可能的年份解析错误，尝试其他解析方式");else return n;const e=new Date().getFullYear();if(/^\d{1,2}-\d{1,2}\s+\d{1,2}:\d{1,2}(:\d{1,2})?$/.test(t)){const i=`${e}-${t}`;if(n=new Date(i),!isNaN(n.getTime()))return n}if(/^\d{1,2}\/\d{1,2}\s+\d{1,2}:\d{1,2}(:\d{1,2})?$/.test(t)){const i=`${e}/${t}`;if(n=new Date(i),!isNaN(n.getTime()))return n}if(/^\d{1,2}月\d{1,2}日\s+\d{1,2}:\d{1,2}$/.test(t)){const i=t.match(/^(\d{1,2})月(\d{1,2})日\s+(\d{1,2}):(\d{1,2})$/);if(i){const[,r,o,u,l]=i;if(n=new Date(e,parseInt(r)-1,parseInt(o),parseInt(u),parseInt(l)),!isNaN(n.getTime()))return n}}if(/^\d{1,2}[-\/]\d{1,2}$/.test(t)){const i=t.includes("-")?`${e}-${t}`:`${e}/${t}`;if(n=new Date(i),!isNaN(n.getTime()))return n}if(/^\d{1,2}月\d{1,2}日$/.test(t)){const i=t.match(/^(\d{1,2})月(\d{1,2})日$/);if(i){const[,r,o]=i;if(n=new Date(e,parseInt(r)-1,parseInt(o)),!isNaN(n.getTime()))return n}}return null},s=(t,n={year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"})=>{if(!t)return"";try{const e=c(t);return e?e.toLocaleDateString("zh-CN",n):t}catch{return t}},A=(t,n)=>{if(!t)return"时间待定";const e=c(t),i=n?c(n):null;if(!e)return"日期格式错误";if(!n||t===n)return s(t,{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"});if(i)if(e.toDateString()===i.toDateString()){const r=e.toLocaleDateString("zh-CN",{year:"numeric",month:"long",day:"numeric"}),o=e.toLocaleTimeString("zh-CN",{hour:"2-digit",minute:"2-digit"}),u=i.toLocaleTimeString("zh-CN",{hour:"2-digit",minute:"2-digit"});return`${r} ${o}-${u}`}else{const r=s(t),o=s(n);return`${r} ~ ${o}`}return s(t)},v=t=>t?s(t,{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"}):"暂无数据";export{v as a,s as b,f as c,N as d,$ as e,A as f,I as g,y as h,p as i};
