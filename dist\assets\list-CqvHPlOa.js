import{d as g,a9 as B,R as N,S as x,b as s,X as D,u as G,r,k as P,l as z,c,o as n,g as u,f as I,L as S,i as V,w as p,j as m,t as E,H as L,_ as R}from"./index-BE8XLQ59.js";import{E as A}from"./index-B27ddgxZ.js";import{G as H,a as j}from"./GlobalHeader-lL88u8sR.js";import{G as F}from"./GridCard-D-EgbodX.js";import{g as M}from"./api-DTO_fYNL.js";const[T,W]=x("divider"),X={dashed:Boolean,hairline:N,vertical:Boolean,contentPosition:B("center")};var $=g({name:T,props:X,setup(t,{slots:i}){return()=>{var a;return s("div",{role:"separator",class:W({dashed:t.dashed,hairline:t.hairline,vertical:t.vertical,[`content-${t.contentPosition}`]:!!i.default&&!t.vertical})},[!t.vertical&&((a=i.default)==null?void 0:a.call(i))])}}});const q=D($),J={class:"news-page"},K={class:"section-container"},O={class:"news-grid"},Q={key:0,class:"loading-container"},U={key:2,class:"grid-container"},Y={key:0,class:"load-more-container"},Z={key:1,class:"no-more-tip"},ee=g({__name:"list",setup(t){const i=G(),a=r(!1),d=r(!1),l=r([]),_=r(1),f=r(5),h=P(()=>l.value.map(e=>({id:e.id,title:e.name,image:e.thumbnail,badge:"新闻",subtitle:e.desc,originalData:e}))),v=async()=>{if(!(d.value||a.value)){a.value=!0;try{const e=await M({page:_.value,page_size:f.value});console.log("加载新闻数据:",e),console.log("is_last_page:",e.is_last_page),l.value.push(...e.results),_.value+=1,d.value=e.is_last_page===!0}catch(e){console.error("加载新闻失败:",e)}finally{a.value=!1}}},y=e=>{const o=e.originalData||e;i.push({name:"ActivityNewsDetail",params:{id:o.id}})};return z(()=>{v()}),(e,o)=>{const k=S,b=A,w=L,C=q;return n(),c("div",J,[s(H,{title:"活动新闻"}),u("div",K,[u("div",O,[a.value&&l.value.length===0?(n(),c("div",Q,[s(k,{type:"spinner",color:"#4b8bf4",size:"32px"}),o[0]||(o[0]=u("p",null,"加载中...",-1))])):!a.value&&l.value.length===0?(n(),I(b,{key:1,description:"暂无活动新闻"})):(n(),c("div",U,[s(F,{items:h.value,onCardClick:y},null,8,["items"]),d.value?l.value.length>0?(n(),c("div",Z,[s(C,null,{default:p(()=>o[1]||(o[1]=[m("没有更多数据了")])),_:1,__:[1]})])):V("",!0):(n(),c("div",Y,[s(w,{loading:a.value,onClick:v,type:"primary",size:"small",round:""},{default:p(()=>[m(E(a.value?"加载中...":"加载更多"),1)]),_:1},8,["loading"])]))]))])]),s(j)])}}}),ie=R(ee,[["__scopeId","data-v-2294b2b2"]]);export{ie as default};
