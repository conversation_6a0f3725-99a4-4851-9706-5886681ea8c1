import { get, requestWithRetry } from '../../api/request';
import { CASES_URLS, buildUrl } from '../../api/urls';
/**
 * 获取领导详情
 * @param id 领导ID
 * @returns 领导详情数据
 */
export function getCaseDetail(id) {
    return get(buildUrl(CASES_URLS.CASE, id));
}
/**
 * 带重试功能的获取领导详情
 * 在网络不稳定情况下使用
 * @param id 领导ID
 * @returns 领导详情数据
 */
export function getCaseDetailWithRetry(id) {
    return requestWithRetry(() => getCaseDetail(id));
}
