# 中医智慧前端项目部署脚本 (Windows PowerShell)
Write-Host "开始部署中医智慧前端项目..." -ForegroundColor Green

# 检查Node.js环境
try {
    $nodeVersion = node --version
    Write-Host "Node.js版本: $nodeVersion" -ForegroundColor Yellow
} catch {
    Write-Host "错误: 未安装Node.js，请先安装Node.js 18+" -ForegroundColor Red
    exit 1
}

# 检查npm
try {
    $npmVersion = npm --version
    Write-Host "npm版本: $npmVersion" -ForegroundColor Yellow
} catch {
    Write-Host "错误: 未安装npm" -ForegroundColor Red
    exit 1
}

# 获取脚本所在目录
$scriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
Set-Location $scriptDir

Write-Host "当前目录: $(Get-Location)" -ForegroundColor Cyan

# 安装依赖
Write-Host "安装项目依赖..." -ForegroundColor Yellow
npm ci
if ($LASTEXITCODE -ne 0) {
    Write-Host "错误: 依赖安装失败" -ForegroundColor Red
    exit 1
}

# 构建项目
Write-Host "构建生产环境代码..." -ForegroundColor Yellow
npm run build
if ($LASTEXITCODE -ne 0) {
    Write-Host "错误: 构建失败" -ForegroundColor Red
    exit 1
}

# 检查构建是否成功
if (!(Test-Path "dist")) {
    Write-Host "错误: 构建失败，未找到dist目录" -ForegroundColor Red
    exit 1
}

Write-Host "构建成功! 生产文件位于 dist/ 目录" -ForegroundColor Green

# 询问部署方式
Write-Host ""
Write-Host "请选择部署方式:" -ForegroundColor Cyan
Write-Host "1) 复制到IIS网站目录"
Write-Host "2) 使用Docker部署"
Write-Host "3) 启动本地预览服务器"
Write-Host "4) 仅构建（已完成）"

$choice = Read-Host "请输入选项 (1-4)"

switch ($choice) {
    "1" {
        $webDir = Read-Host "请输入IIS网站目录路径 (例如: C:\inetpub\wwwroot\zyyfront)"
        if (Test-Path $webDir) {
            Write-Host "复制文件到 $webDir ..." -ForegroundColor Yellow
            Copy-Item -Path "dist\*" -Destination $webDir -Recurse -Force
            Write-Host "部署完成! 文件已复制到 $webDir" -ForegroundColor Green
        } else {
            Write-Host "错误: 目录 $webDir 不存在，是否创建? (y/n)" -ForegroundColor Yellow
            $create = Read-Host
            if ($create -eq "y" -or $create -eq "Y") {
                New-Item -ItemType Directory -Path $webDir -Force
                Copy-Item -Path "dist\*" -Destination $webDir -Recurse -Force
                Write-Host "目录已创建并部署完成!" -ForegroundColor Green
            } else {
                Write-Host "部署已取消" -ForegroundColor Red
                exit 1
            }
        }
    }
    "2" {
        try {
            docker --version | Out-Null
            Write-Host "使用Docker构建镜像..." -ForegroundColor Yellow
            docker build -t zyyfront:latest .
            Write-Host "启动容器..." -ForegroundColor Yellow
            docker run -d --name zyyfront -p 80:80 --restart unless-stopped zyyfront:latest
            Write-Host "Docker部署完成! 应用运行在 http://localhost" -ForegroundColor Green
        } catch {
            Write-Host "错误: 未安装Docker或Docker未启动" -ForegroundColor Red
            exit 1
        }
    }
    "3" {
        Write-Host "启动本地预览服务器..." -ForegroundColor Yellow
        npm run preview
    }
    "4" {
        Write-Host "仅构建完成，dist目录包含所有生产文件" -ForegroundColor Green
    }
    default {
        Write-Host "无效选项" -ForegroundColor Red
        exit 1
    }
}

Write-Host ""
Write-Host "部署完成! 🎉" -ForegroundColor Green
Write-Host "项目特性:" -ForegroundColor Cyan
Write-Host "- 响应式移动端设计"
Write-Host "- API代理已配置"
Write-Host "- 静态资源优化"
Write-Host "- Vue Router单页面应用支持"
Write-Host "- 中医智慧主题UI" 