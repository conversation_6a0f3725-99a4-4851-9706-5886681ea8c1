/* 基础容器样式 */
.video-promotion-container {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
  padding-bottom: 0;
  position: relative;
  width: 100%;
  max-width: 100vw;
  overflow-x: hidden;
  box-sizing: border-box;
}

/* 分类标签样式 */
.category-tabs {
  background: #fff;
  border-radius: 0 0 16px 16px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  margin-bottom: 16px;
}

.category-tabs .van-tabs__nav {
  background: #fff;
  padding: 0 8px;
}

.category-tabs .van-tab {
  font-size: 14px;
  font-weight: 500;
  color: #666;
  transition: all 0.3s ease;
  flex: 1;
  min-width: 0;
}

.category-tabs .van-tab--active {
  color: #ff8c69;
  font-weight: 600;
}

.category-tabs .van-tabs__line {
  background: linear-gradient(90deg, #ff8c69, #ff6b35);
  height: 3px;
  border-radius: 2px;
}

/* 标签页内容 */
.tab-content {
  padding: 0;
  min-height: 60vh;
  box-sizing: border-box;
  width: 100%;
}

/* 视频列表 */
.video-list {
  display: grid;
  gap: 16px;
  margin: 0 15px;
  padding: 5px;
  width: auto;
  box-sizing: border-box;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.video-card {
  background: #fff;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  cursor: pointer;
  border: 1px solid rgba(255, 140, 105, 0.1);
  width: 100%;
  box-sizing: border-box;
}

.video-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(255, 140, 105, 0.15);
  border-color: rgba(255, 140, 105, 0.2);
}

.video-thumbnail-wrapper {
  position: relative;
  height: 200px;
  overflow: hidden;
  width: 100%;
  background: #000;
}

.video-thumbnail {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.video-card:hover .video-thumbnail {
  transform: scale(1.05);
}

.play-button {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 60px;
  height: 60px;
  background: rgba(0, 0, 0, 0.7);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.video-card:hover .play-button {
  background: rgba(255, 140, 105, 0.9);
  transform: translate(-50%, -50%) scale(1.1);
}

.video-duration {
  position: absolute;
  bottom: 8px;
  right: 8px;
  padding: 2px 6px;
  background: rgba(0, 0, 0, 0.8);
  color: #fff;
  font-size: 11px;
  border-radius: 4px;
  font-weight: 500;
}

.video-quality {
  position: absolute;
  top: 8px;
  right: 8px;
  padding: 2px 6px;
  background: rgba(255, 140, 105, 0.9);
  color: #fff;
  font-size: 10px;
  border-radius: 4px;
  font-weight: 500;
}

.video-tag {
  position: absolute;
  top: 8px;
  left: 8px;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
  color: #fff;
  backdrop-filter: blur(10px);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  white-space: nowrap;
}

.video-tag.promotional {
  background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
}

.video-tag.educational {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.video-tag.documentary {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.video-tag.interview {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.featured-badge {
  position: absolute;
  top: 40px;
  left: 8px;
  padding: 2px 6px;
  background: linear-gradient(135deg, #ffd700 0%, #ffb347 100%);
  color: #333;
  font-size: 10px;
  border-radius: 8px;
  font-weight: 600;
  text-shadow: none;
}

.video-content {
  padding: 16px;
  box-sizing: border-box;
}

.video-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0 0 8px 0;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-break: break-word;
}

.video-summary {
  font-size: 13px;
  color: #666;
  line-height: 1.5;
  margin: 0 0 12px 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-break: break-word;
}

.video-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: #999;
  flex-wrap: wrap;
  gap: 8px;
}

.meta-left {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.meta-right {
  display: flex;
  gap: 12px;
  align-items: center;
  flex-wrap: wrap;
}

.meta-right span {
  display: flex;
  align-items: center;
  gap: 4px;
  white-space: nowrap;
}

.author {
  color: #ff8c69;
  font-weight: 500;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: #666;
}

.loading-container p {
  margin-top: 12px;
  font-size: 14px;
}

/* 错误状态 */
.error-container {
  padding: 20px;
}

/* 加载更多 */
.load-more {
  text-align: center;
  margin-top: 24px;
  padding: 16px;
  width: 100%;
}

.load-more .van-button {
  min-width: 120px;
  border-radius: 20px;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .video-list {
    margin: 0 10px;
    padding: 10px;
  }
  
  .video-thumbnail-wrapper {
    height: 180px;
  }
  
  .video-content {
    padding: 12px;
  }
  
  .video-title {
    font-size: 15px;
  }
  
  .video-summary {
    font-size: 12px;
  }
  
  .play-button {
    width: 50px;
    height: 50px;
  }
}

@media (max-width: 480px) {
  .video-list {
    margin: 0 8px;
    padding: 8px;
  }
  
  .video-thumbnail-wrapper {
    height: 160px;
  }
  
  .video-content {
    padding: 10px;
  }
  
  .video-title {
    font-size: 14px;
  }
  
  .video-summary {
    font-size: 11px;
  }
  
  .video-meta {
    font-size: 11px;
  }
  
  .play-button {
    width: 40px;
    height: 40px;
  }
  
  .play-button .van-icon {
    font-size: 18px;
  }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
  .video-card:hover {
    transform: none;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  }
  
  .video-card:hover .video-thumbnail {
    transform: none;
  }
  
  .video-card:hover .play-button {
    background: rgba(0, 0, 0, 0.7);
    transform: translate(-50%, -50%);
  }
}

/* 触摸反馈 */
.video-card:active {
  transform: translateY(-2px) scale(0.98);
}

.play-button:active {
  transform: translate(-50%, -50%) scale(0.9);
}

/* 视频网格布局优化 */
@media (min-width: 768px) {
  .video-list {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  }
}

@media (min-width: 1024px) {
  .video-list {
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  }
}