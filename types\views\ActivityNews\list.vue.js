import { ref, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import GlobalHeader from '../../components/GlobalHeader.vue';
import GlobalFooter from '../../components/GlobalFooter.vue';
import GridCard from '../../components/GridCard.vue';
import { getActivityNewsListlWithRetry } from './api'; // 假设支持分页参数
const router = useRouter();
const loading = ref(false);
const finished = ref(false);
const items = ref([]);
const page = ref(1);
const pageSize = ref(5); // 每次加载5条数据
// 将活动新闻数据转换为GridCard需要的格式
const gridItems = computed(() => {
    return items.value.map((item) => ({
        id: item.id,
        title: item.name,
        image: item.thumbnail,
        badge: '新闻',
        subtitle: item.desc,
        originalData: item
    }));
});
const loadItems = async () => {
    if (finished.value || loading.value)
        return;
    loading.value = true;
    try {
        const res = await getActivityNewsListlWithRetry({
            page: page.value,
            page_size: pageSize.value,
        });
        console.log('加载新闻数据:', res);
        console.log('is_last_page:', res.is_last_page);
        items.value.push(...res.results);
        page.value += 1;
        finished.value = res.is_last_page === true;
    }
    catch (error) {
        console.error('加载新闻失败:', error);
    }
    finally {
        loading.value = false;
    }
};
const handleCardClick = (news) => {
    router.push({ name: 'ActivityNewsDetail', params: { id: news.id } });
};
// GridCard点击处理
const handleGridCardClick = (item) => {
    const newsData = item.originalData || item;
    router.push({ name: 'ActivityNewsDetail', params: { id: newsData.id } });
};
onMounted(() => {
    loadItems();
});
debugger; /* PartiallyEnd: #3632/scriptSetup.vue */
const __VLS_ctx = {};
let __VLS_components;
let __VLS_directives;
/** @type {__VLS_StyleScopedClasses['loading-container']} */ ;
/** @type {__VLS_StyleScopedClasses['no-more-tip']} */ ;
/** @type {__VLS_StyleScopedClasses['section-container']} */ ;
/** @type {__VLS_StyleScopedClasses['load-more-container']} */ ;
// CSS variable injection 
// CSS variable injection end 
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ class: "news-page" },
});
/** @type {[typeof GlobalHeader, ]} */ ;
// @ts-ignore
const __VLS_0 = __VLS_asFunctionalComponent(GlobalHeader, new GlobalHeader({
    title: "活动新闻",
}));
const __VLS_1 = __VLS_0({
    title: "活动新闻",
}, ...__VLS_functionalComponentArgsRest(__VLS_0));
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ class: "section-container" },
});
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ class: "news-grid" },
});
if (__VLS_ctx.loading && __VLS_ctx.items.length === 0) {
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
        ...{ class: "loading-container" },
    });
    const __VLS_3 = {}.VanLoading;
    /** @type {[typeof __VLS_components.VanLoading, typeof __VLS_components.vanLoading, ]} */ ;
    // @ts-ignore
    const __VLS_4 = __VLS_asFunctionalComponent(__VLS_3, new __VLS_3({
        type: "spinner",
        color: "#4b8bf4",
        size: "32px",
    }));
    const __VLS_5 = __VLS_4({
        type: "spinner",
        color: "#4b8bf4",
        size: "32px",
    }, ...__VLS_functionalComponentArgsRest(__VLS_4));
    __VLS_asFunctionalElement(__VLS_intrinsicElements.p, __VLS_intrinsicElements.p)({});
}
else if (!__VLS_ctx.loading && __VLS_ctx.items.length === 0) {
    const __VLS_7 = {}.VanEmpty;
    /** @type {[typeof __VLS_components.VanEmpty, typeof __VLS_components.vanEmpty, ]} */ ;
    // @ts-ignore
    const __VLS_8 = __VLS_asFunctionalComponent(__VLS_7, new __VLS_7({
        description: "暂无活动新闻",
    }));
    const __VLS_9 = __VLS_8({
        description: "暂无活动新闻",
    }, ...__VLS_functionalComponentArgsRest(__VLS_8));
}
else {
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
        ...{ class: "grid-container" },
    });
    /** @type {[typeof GridCard, ]} */ ;
    // @ts-ignore
    const __VLS_11 = __VLS_asFunctionalComponent(GridCard, new GridCard({
        ...{ 'onCardClick': {} },
        items: (__VLS_ctx.gridItems),
    }));
    const __VLS_12 = __VLS_11({
        ...{ 'onCardClick': {} },
        items: (__VLS_ctx.gridItems),
    }, ...__VLS_functionalComponentArgsRest(__VLS_11));
    let __VLS_14;
    let __VLS_15;
    let __VLS_16;
    const __VLS_17 = {
        onCardClick: (__VLS_ctx.handleGridCardClick)
    };
    var __VLS_13;
    if (!__VLS_ctx.finished) {
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "load-more-container" },
        });
        const __VLS_18 = {}.VanButton;
        /** @type {[typeof __VLS_components.VanButton, typeof __VLS_components.vanButton, typeof __VLS_components.VanButton, typeof __VLS_components.vanButton, ]} */ ;
        // @ts-ignore
        const __VLS_19 = __VLS_asFunctionalComponent(__VLS_18, new __VLS_18({
            ...{ 'onClick': {} },
            loading: (__VLS_ctx.loading),
            type: "primary",
            size: "small",
            round: true,
        }));
        const __VLS_20 = __VLS_19({
            ...{ 'onClick': {} },
            loading: (__VLS_ctx.loading),
            type: "primary",
            size: "small",
            round: true,
        }, ...__VLS_functionalComponentArgsRest(__VLS_19));
        let __VLS_22;
        let __VLS_23;
        let __VLS_24;
        const __VLS_25 = {
            onClick: (__VLS_ctx.loadItems)
        };
        __VLS_21.slots.default;
        (__VLS_ctx.loading ? '加载中...' : '加载更多');
        var __VLS_21;
    }
    else if (__VLS_ctx.items.length > 0) {
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "no-more-tip" },
        });
        const __VLS_26 = {}.VanDivider;
        /** @type {[typeof __VLS_components.VanDivider, typeof __VLS_components.vanDivider, typeof __VLS_components.VanDivider, typeof __VLS_components.vanDivider, ]} */ ;
        // @ts-ignore
        const __VLS_27 = __VLS_asFunctionalComponent(__VLS_26, new __VLS_26({}));
        const __VLS_28 = __VLS_27({}, ...__VLS_functionalComponentArgsRest(__VLS_27));
        __VLS_29.slots.default;
        var __VLS_29;
    }
}
/** @type {[typeof GlobalFooter, ]} */ ;
// @ts-ignore
const __VLS_30 = __VLS_asFunctionalComponent(GlobalFooter, new GlobalFooter({}));
const __VLS_31 = __VLS_30({}, ...__VLS_functionalComponentArgsRest(__VLS_30));
/** @type {__VLS_StyleScopedClasses['news-page']} */ ;
/** @type {__VLS_StyleScopedClasses['section-container']} */ ;
/** @type {__VLS_StyleScopedClasses['news-grid']} */ ;
/** @type {__VLS_StyleScopedClasses['loading-container']} */ ;
/** @type {__VLS_StyleScopedClasses['grid-container']} */ ;
/** @type {__VLS_StyleScopedClasses['load-more-container']} */ ;
/** @type {__VLS_StyleScopedClasses['no-more-tip']} */ ;
var __VLS_dollars;
const __VLS_self = (await import('vue')).defineComponent({
    setup() {
        return {
            GlobalHeader: GlobalHeader,
            GlobalFooter: GlobalFooter,
            GridCard: GridCard,
            loading: loading,
            finished: finished,
            items: items,
            gridItems: gridItems,
            loadItems: loadItems,
            handleGridCardClick: handleGridCardClick,
        };
    },
});
export default (await import('vue')).defineComponent({
    setup() {
        return {};
    },
});
; /* PartiallyEnd: #4569/main.vue */
