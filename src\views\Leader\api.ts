import { get, requestWithRetry } from '../../api/request';
import { LEADER_URLS, buildUrl } from '../../api/urls';

/**
 * 获取领导分类
 * @returns 领导分类数据
 */
export function getLeaderCategories() {
  return get<LeaderCategoriesResponse>(LEADER_URLS.CATEGORIES);
}

/**
 * 根据分类获取领导列表
 * @param cat 分类ID
 * @param page 页码
 * @param pageSize 每页数量
 * @returns 领导列表数据
 */
export function getLeadersByCategory(cat: string, page: number = 1, pageSize: number = 6) {
  return get<LeaderListResponse>(LEADER_URLS.LEADER, {
    cat,
    page,
    page_size: pageSize
  });
}

/**
 * 带重试功能的获取领导分类
 * 在网络不稳定情况下使用
 * @returns 领导分类数据
 */

export function getLeaderCategoriesWithRetry(cat: string, page: number = 1, pageSize: number = 6) {
  return requestWithRetry<LeaderListResponse>(() => getLeadersByCategory(cat, page, pageSize));
}

// 领导分类接口
export interface LeaderCategory {
  value: string;
  label: string;
}

// 领导分类响应接口
export interface LeaderCategoriesResponse {
  categories: LeaderCategory[];
}

// 领导信息接口
export interface LeaderItem {
  id: number;
  name: string;
  title: string;
  job: string;
  cat_display: string;
  desc: string;
  thumbnail: string;
  category: string;
  tags?: string[];
}

// 领导列表响应接口
export interface LeaderListResponse {
  results: LeaderItem[];
  page: number;
  page_size: number;
  total: number;
  is_last_page: boolean;
}