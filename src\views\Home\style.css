/* 移动端优化基础样式 */
* {
  box-sizing: border-box;
  -webkit-tap-highlight-color: transparent;
}

img {
  max-width: 100%;
  height: auto;
  display: block; /* 移除图片底部空白 */
}

html, body {
  margin: 0;
  padding: 0;
  width: 100%;
  height: 100%;
  font-size: 16px;
  line-height: 1.5;
  font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, Segoe UI, Arial, Roboto, 'PingFang SC', 'miui', 'Hiragino Sans GB', 'Microsoft Yahei', sans-serif;
  overflow-x: hidden; /* 确保整个页面没有水平滚动 */
}

.home-container {
  padding-bottom: 80px; /* 为固定的底部导航栏留出空间 */
  background: linear-gradient(to bottom, #f7f8fa 0%, #f0f2f5 100%);
  width: 100%;
  max-width: 100%;
  overflow-x: hidden;
  position: relative;
}

.nav-wrapper {
  width: 100%;
}

.home-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #4b8bf4, #7cb9e8);
  z-index: 0;
  opacity: 0.1;
}



.decorative-footer {
  background: linear-gradient(135deg, #4b8bf4, #7cb9e8);
  color: #fff;
  text-align: center;
  padding: 20px;
  margin-top: 20px;
  position: relative;
  overflow: hidden;
}

.decorative-footer::before {
  content: '中医智慧';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) rotate(-15deg);
  font-size: 120px;
  font-weight: bold;
  color: rgba(255, 255, 255, 0.1);
  z-index: 0;
}

.decorative-footer p {
  position: relative;
  z-index: 1;
  margin: 8px 0;
  font-size: 14px;
  opacity: 0.9;
}

.decorative-footer p:first-child {
  font-weight: bold;
  font-size: 16px;
}



/* 页脚样式 */
.footer {
  background-color: #fff;
  padding: 20px; /* 增加内边距 */
  margin-top: 20px; /* 增加上边距 */
  border-top: 1px solid #f2f3f5; /* 添加顶部边框 */
}

.footer-content {
  display: flex;
  flex-direction: column; /* 默认垂直堆叠 */
  align-items: center;
  margin-bottom: 20px;
}

.footer-logo {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 20px; /* 增加下边距 */
}

.footer-logo-text {
  font-size: 18px; /* 增大字体 */
  font-weight: bold;
  margin-top: 8px; /* 增加上边距 */
  color: #323233;
}

.footer-links {
  display: flex;
  flex-wrap: wrap;
  justify-content: center; /* 居中对齐 */
  gap: 20px; /* 增加组之间的间距 */
  margin-bottom: 20px;
}

.footer-link-group {
  display: flex;
  flex-direction: column;
  align-items: flex-start; /* 左对齐 */
  min-width: 100px; /* 调整最小宽度 */
  margin-bottom: 0; /* 移除组之间的额外间距 */
}

.footer-link-title {
  font-size: 16px; /* 增大字体 */
  font-weight: bold;
  margin-bottom: 10px; /* 增加下边距 */
  color: #323233;
}

.footer-link-item {
  font-size: 14px; /* 增大字体 */
  color: #646566;
  margin-bottom: 8px; /* 增加下边距 */
  cursor: pointer;
}

.footer-link-item:hover {
  color: #1989fa;
}

.footer-social {
  display: flex;
  gap: 15px; /* 增加图标间距 */
  margin-top: 10px;
}

.footer-copyright {
  text-align: center;
  font-size: 12px;
  color: #969799;
  border-top: 1px solid #f2f3f5;
  padding-top: 16px;
}

.footer-copyright p {
  margin: 4px 0;
}

/* 响应式调整 */
@media (min-width: 768px) {
  .footer-content {
    flex-direction: row; /* 桌面端并排显示 */
    justify-content: space-around;
    align-items: flex-start;
  }

  .footer-logo {
    margin-bottom: 0;
  }

  .footer-links {
    justify-content: space-between;
    flex-grow: 1;
    max-width: 600px; /* 限制最大宽度 */
  }

  .footer-link-group {
    align-items: center; /* 居中对齐 */
  }
}

/* 动画延迟类 */
.delay-100 {
  animation-delay: 0.1s;
}

.delay-200 {
  animation-delay: 0.2s;
}

.delay-300 {
  animation-delay: 0.3s;
}

/* 响应式调整 */
@media (max-width: 480px) {
  /* 通用样式调整 */
  
  /* 轮播图调整 */
  .swipe-image {
    height: 180px; /* 减小轮播图高度 */
  }
  
  .swipe-caption {
    padding: 12px;
    font-size: 14px; /* 减小字体大小 */
  }
  
  /* 功能按钮区调整 */
  .icon-wrapper {
    width: 40px; /* 减小图标容器大小 */
    height: 40px;
  }
  
  /* 视频列表调整 */
  .video-list {
    grid-template-columns: repeat(auto-fill, minmax(130px, 1fr));
  }
  
  /* 页脚调整 */
  .footer-links {
    flex-direction: column;
    align-items: center;
    gap: 15px;
  }
  
  .footer-link-group {
    width: 100%;
    margin-bottom: 15px;
  }
}

/* 小屏幕手机适配 */
@media (max-width: 320px) {
  
  .swipe-image {
    height: 160px;
  }
  
  .video-list {
    grid-template-columns: 1fr 1fr; /* 强制两列布局 */
  }
  
  .subscribe-form {
    max-width: 100%; /* 确保表单在小屏幕上能完全显示 */
  }
}

/* 触摸优化 */
@media (hover: none) {
  /* 针对触摸设备的优化 */
  .grid-item-content, .article-card, .video-card, .news-cell, .footer-link-item {
    cursor: pointer; /* 明确表示可点击 */
  }
  
  .footer-link-item:active {
    color: #1989fa; /* 触摸时的颜色变化，替代hover效果 */
  }
}
