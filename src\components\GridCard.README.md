# GridCard 宫格卡片组件

一个现代化的宫格卡片组件，参考视频卡片样式设计，支持左右排列（一排 2 个），适用于文本内容展示。

## 📋 功能特性

- ✅ **宫格布局** - 左右排列，一排显示 2 个卡片
- ✅ **视频风格设计** - 参考视频卡片的现代化设计
- ✅ **响应式设计** - 适配不同屏幕尺寸
- ✅ **交互动画** - 悬停效果、点击反馈
- ✅ **图片处理** - 自动裁剪、错误处理、悬停缩放
- ✅ **右下角标识** - 支持推荐、热门等标识
- ✅ **TypeScript 支持** - 完整的类型定义

## 🎨 组件结构

```
┌─────────────────────────────────┐
│                                 │
│          图片区域               │
│                    [标识]       │
├─────────────────────────────────┤
│          标题文字               │
└─────────────────────────────────┘
```

## 📦 安装使用

### 基础用法

```vue
<template>
  <GridCard :items="cardItems" @card-click="handleCardClick" />
</template>

<script setup lang="ts">
import GridCard from "@/components/GridCard.vue";
import type { GridCardItem } from "@/components/GridCard.vue";

const cardItems: GridCardItem[] = [
  {
    id: 1,
    title: "名医讲堂：高血压的中医治疗方法",
    image: "/path/to/image.jpg",
  },
];

const handleCardClick = (item: GridCardItem) => {
  console.log("点击了卡片:", item);
};
</script>
```

### 带右下角标识的用法

```vue
<template>
  <GridCard :items="cardItems" />
</template>

<script setup lang="ts">
const cardItems: GridCardItem[] = [
  {
    id: 1,
    title: "最新医疗技术突破",
    image: "/path/to/image.jpg",
    badge: "推荐", // 右下角标识
  },
];
</script>
```

## 🔧 API 参考

### Props

| 参数        | 类型             | 默认值  | 说明         |
| ----------- | ---------------- | ------- | ------------ |
| `items`     | `GridCardItem[]` | `[]`    | 卡片数据数组 |
| `clickable` | `boolean`        | `true`  | 是否可点击   |
| `loading`   | `boolean`        | `false` | 加载状态     |

### Events

| 事件名        | 参数                   | 说明             |
| ------------- | ---------------------- | ---------------- |
| `card-click`  | `(item: GridCardItem)` | 卡片点击事件     |
| `image-error` | `(item: GridCardItem)` | 图片加载错误事件 |

### 数据类型

#### GridCardItem

```typescript
interface GridCardItem {
  id: string | number; // 唯一标识
  title: string; // 标题
  image: string; // 图片URL
  badge?: string; // 右下角标识（如"推荐"、"热门"等）
  [key: string]: any; // 扩展字段
}
```

## 🎨 样式特点

### 设计风格

- **现代卡片设计** - 12px 圆角，柔和阴影
- **悬停动效** - 向上浮起 3px，阴影加深
- **图片缩放** - 悬停时图片放大 1.05 倍
- **右下角标识** - 半透明黑色背景，白色文字

### 布局比例

- **图片区域** - 固定高度 120px（桌面端）
- **内容区域** - 自适应高度，包含标题
- **标题** - 最多显示 2 行，超出省略，居中对齐

## 📱 响应式特性

| 屏幕尺寸 | 图片高度 | 字体调整 |
| -------- | -------- | -------- |
| >480px   | 120px    | 标准     |
| ≤480px   | 100px    | 缩小     |
| ≤320px   | 80px     | 最小     |

## 🔍 使用场景

- **医疗资讯** - 医院新闻、健康知识文章
- **专家讲座** - 名医讲堂、健康讲座
- **科普内容** - 疾病科普、养生知识
- **教育内容** - 医学教育、培训课程

## 💡 最佳实践

1. **图片规格** - 建议使用 16:9 或 4:3 比例的图片
2. **标题长度** - 建议控制在 25 字以内，确保 2 行显示
3. **标识文字** - 建议使用 2-4 个字的简短标识
4. **内容质量** - 确保图片清晰，标题准确描述内容

## 🎯 设计亮点

1. **视频风格** - 参考视频卡片设计，适配文本内容
2. **信息层次** - 图片吸引注意，标题传达信息
3. **交互反馈** - 丰富的悬停和点击动画效果
4. **空间利用** - 合理的空间分配，信息展示清晰
5. **品牌一致** - 与整体设计风格保持一致

## 🐛 常见问题

### Q: 如何设置右下角标识？

A: 在数据中添加 `badge` 字段即可，支持任意文本。

### Q: 如何自定义卡片高度？

A: 卡片高度会根据内容自动调整，图片部分为固定高度。

### Q: 如何禁用点击？

A: 设置 `clickable="false"` 即可禁用点击功能。

## 📄 更新日志

### v2.1.0

- ✅ 移除作者信息显示
- ✅ 简化卡片结构
- ✅ 标题居中对齐
- ✅ 优化内容区域布局

### v2.0.0

- ✅ 重新设计，参考视频卡片样式
- ✅ 简化数据结构，专注文本内容
- ✅ 添加右下角标识功能
- ✅ 提升响应式体验

### v1.0.0

- ✅ 初始版本发布
