import { get, requestWithRetry } from '../../api/request';
import { CULTURE_URLS, buildUrl } from '../../api/urls';

/**
 * 获取分类
 * @returns 分类数据
 */
export function getCultureCategories() {
  return get<CultureCategoriesResponse>(CULTURE_URLS.CATEGORIES);
}

/**
 * 根据分类获取列表
 * @param cat 分类ID
 * @param page 页码
 * @param pageSize 每页数量
 * @returns 列表数据
 */
export function getCultureByCategory(cat: string, page: number = 1, pageSize: number = 10) {
  return get<CultureListResponse>(CULTURE_URLS.CULTURE, {
    cat,
    page,
    page_size: pageSize
  });
}

/**
 * 带重试功能的获取分类
 * 在网络不稳定情况下使用
 * @returns 分类数据
 */

export function getCultureCategoriesWithRetry(cat: string, page: number = 1, pageSize: number = 10) {
  return requestWithRetry<CultureListResponse>(() => getCultureByCategory(
    cat,
    page,
    pageSize
  ));
}

// 分类接口
export interface CultureCategory {
  value: string;
  label: string;
}

// 分类响应接口
export interface CultureCategoriesResponse {
  categories: CultureCategory[];
}

// 信息接口
export interface CultureItem {
  id: number;
  name: string;
  title: string;
  job: string;
  cat_display: string;
  desc: string;
  thumbnail: string;
  category: string;
  tags?: string[];
}

// 列表响应接口
export interface CultureListResponse {
  results: CultureItem[];
  page: number;
  page_size: number;
  total: number;
  is_last_page: boolean;
}