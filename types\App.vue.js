import { ref, onMounted } from "vue";
import axiosInstance from "./api"; // 引入封装的axios
import "vant/lib/index.css"; // 引入Vant样式
import "animate.css"; // 引入Animate.css
// 删除未使用的变量 message
const apiData = ref(null);
// const showAnimate = ref(false); // Removed unused variable
const fetchData = async () => {
    try {
        // 模拟API请求，实际项目中替换为您的后端接口
        const response = await axiosInstance.get("/some-data");
        apiData.value = response.data;
        console.log("API Data:", apiData.value);
    }
    catch (error) {
        console.error("Error fetching data:", error);
    }
};
// 删除未使用的函数 toggleAnimate
onMounted(() => {
    fetchData();
});
debugger; /* PartiallyEnd: #3632/scriptSetup.vue */
const __VLS_ctx = {};
let __VLS_components;
let __VLS_directives;
/** @type {__VLS_StyleScopedClasses['van-tabbar-item--active']} */ ;
/** @type {__VLS_StyleScopedClasses['icon-wrapper']} */ ;
/** @type {__VLS_StyleScopedClasses['icon-wrapper']} */ ;
/** @type {__VLS_StyleScopedClasses['icon-wrapper']} */ ;
/** @type {__VLS_StyleScopedClasses['active']} */ ;
/** @type {__VLS_StyleScopedClasses['van-icon']} */ ;
/** @type {__VLS_StyleScopedClasses['van-tabbar-item__text']} */ ;
/** @type {__VLS_StyleScopedClasses['van-tabbar-item']} */ ;
/** @type {__VLS_StyleScopedClasses['van-tabbar-item--active']} */ ;
/** @type {__VLS_StyleScopedClasses['van-tabbar-item']} */ ;
/** @type {__VLS_StyleScopedClasses['van-tabbar-item--active']} */ ;
/** @type {__VLS_StyleScopedClasses['van-tabbar-item']} */ ;
/** @type {__VLS_StyleScopedClasses['van-tabbar-item--active']} */ ;
/** @type {__VLS_StyleScopedClasses['icon-wrapper']} */ ;
/** @type {__VLS_StyleScopedClasses['van-tabbar-item--active']} */ ;
/** @type {__VLS_StyleScopedClasses['van-tabbar-item']} */ ;
/** @type {__VLS_StyleScopedClasses['van-tabbar-item--active']} */ ;
/** @type {__VLS_StyleScopedClasses['icon-wrapper']} */ ;
/** @type {__VLS_StyleScopedClasses['icon-wrapper']} */ ;
/** @type {__VLS_StyleScopedClasses['van-icon']} */ ;
/** @type {__VLS_StyleScopedClasses['van-tabbar-item__text']} */ ;
/** @type {__VLS_StyleScopedClasses['custom-tabbar']} */ ;
/** @type {__VLS_StyleScopedClasses['van-tabbar-item']} */ ;
// CSS variable injection 
// CSS variable injection end 
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ class: "container" },
});
const __VLS_0 = {}.RouterView;
/** @type {[typeof __VLS_components.RouterView, typeof __VLS_components.routerView, ]} */ ;
// @ts-ignore
const __VLS_1 = __VLS_asFunctionalComponent(__VLS_0, new __VLS_0({}));
const __VLS_2 = __VLS_1({}, ...__VLS_functionalComponentArgsRest(__VLS_1));
const __VLS_4 = {}.VanTabbar;
/** @type {[typeof __VLS_components.VanTabbar, typeof __VLS_components.vanTabbar, typeof __VLS_components.VanTabbar, typeof __VLS_components.vanTabbar, ]} */ ;
// @ts-ignore
const __VLS_5 = __VLS_asFunctionalComponent(__VLS_4, new __VLS_4({
    route: true,
    fixed: true,
    placeholder: true,
    ...{ class: "custom-tabbar" },
}));
const __VLS_6 = __VLS_5({
    route: true,
    fixed: true,
    placeholder: true,
    ...{ class: "custom-tabbar" },
}, ...__VLS_functionalComponentArgsRest(__VLS_5));
__VLS_7.slots.default;
const __VLS_8 = {}.VanTabbarItem;
/** @type {[typeof __VLS_components.VanTabbarItem, typeof __VLS_components.vanTabbarItem, typeof __VLS_components.VanTabbarItem, typeof __VLS_components.vanTabbarItem, ]} */ ;
// @ts-ignore
const __VLS_9 = __VLS_asFunctionalComponent(__VLS_8, new __VLS_8({
    replace: true,
    to: "/home",
    icon: "home-o",
    ...{ class: "tabbar-item" },
}));
const __VLS_10 = __VLS_9({
    replace: true,
    to: "/home",
    icon: "home-o",
    ...{ class: "tabbar-item" },
}, ...__VLS_functionalComponentArgsRest(__VLS_9));
__VLS_11.slots.default;
{
    const { icon: __VLS_thisSlot } = __VLS_11.slots;
    const [{ active }] = __VLS_getSlotParams(__VLS_thisSlot);
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
        ...{ class: "icon-wrapper" },
        ...{ class: ({ active }) },
    });
    const __VLS_12 = {}.VanIcon;
    /** @type {[typeof __VLS_components.VanIcon, typeof __VLS_components.vanIcon, ]} */ ;
    // @ts-ignore
    const __VLS_13 = __VLS_asFunctionalComponent(__VLS_12, new __VLS_12({
        name: "home-o",
    }));
    const __VLS_14 = __VLS_13({
        name: "home-o",
    }, ...__VLS_functionalComponentArgsRest(__VLS_13));
}
__VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({});
var __VLS_11;
const __VLS_16 = {}.VanTabbarItem;
/** @type {[typeof __VLS_components.VanTabbarItem, typeof __VLS_components.vanTabbarItem, typeof __VLS_components.VanTabbarItem, typeof __VLS_components.vanTabbarItem, ]} */ ;
// @ts-ignore
const __VLS_17 = __VLS_asFunctionalComponent(__VLS_16, new __VLS_16({
    replace: true,
    to: "/knowledge",
    icon: "bulb-o",
    ...{ class: "tabbar-item" },
}));
const __VLS_18 = __VLS_17({
    replace: true,
    to: "/knowledge",
    icon: "bulb-o",
    ...{ class: "tabbar-item" },
}, ...__VLS_functionalComponentArgsRest(__VLS_17));
__VLS_19.slots.default;
{
    const { icon: __VLS_thisSlot } = __VLS_19.slots;
    const [{ active }] = __VLS_getSlotParams(__VLS_thisSlot);
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
        ...{ class: "icon-wrapper" },
        ...{ class: ({ active }) },
    });
    const __VLS_20 = {}.VanIcon;
    /** @type {[typeof __VLS_components.VanIcon, typeof __VLS_components.vanIcon, ]} */ ;
    // @ts-ignore
    const __VLS_21 = __VLS_asFunctionalComponent(__VLS_20, new __VLS_20({
        name: "bulb-o",
    }));
    const __VLS_22 = __VLS_21({
        name: "bulb-o",
    }, ...__VLS_functionalComponentArgsRest(__VLS_21));
}
__VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({});
var __VLS_19;
const __VLS_24 = {}.VanTabbarItem;
/** @type {[typeof __VLS_components.VanTabbarItem, typeof __VLS_components.vanTabbarItem, typeof __VLS_components.VanTabbarItem, typeof __VLS_components.vanTabbarItem, ]} */ ;
// @ts-ignore
const __VLS_25 = __VLS_asFunctionalComponent(__VLS_24, new __VLS_24({
    replace: true,
    to: "/activity",
    icon: "fire-o",
    ...{ class: "tabbar-item" },
}));
const __VLS_26 = __VLS_25({
    replace: true,
    to: "/activity",
    icon: "fire-o",
    ...{ class: "tabbar-item" },
}, ...__VLS_functionalComponentArgsRest(__VLS_25));
__VLS_27.slots.default;
{
    const { icon: __VLS_thisSlot } = __VLS_27.slots;
    const [{ active }] = __VLS_getSlotParams(__VLS_thisSlot);
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
        ...{ class: "icon-wrapper" },
        ...{ class: ({ active }) },
    });
    const __VLS_28 = {}.VanIcon;
    /** @type {[typeof __VLS_components.VanIcon, typeof __VLS_components.vanIcon, ]} */ ;
    // @ts-ignore
    const __VLS_29 = __VLS_asFunctionalComponent(__VLS_28, new __VLS_28({
        name: "fire-o",
    }));
    const __VLS_30 = __VLS_29({
        name: "fire-o",
    }, ...__VLS_functionalComponentArgsRest(__VLS_29));
}
__VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({});
var __VLS_27;
var __VLS_7;
/** @type {__VLS_StyleScopedClasses['container']} */ ;
/** @type {__VLS_StyleScopedClasses['custom-tabbar']} */ ;
/** @type {__VLS_StyleScopedClasses['tabbar-item']} */ ;
/** @type {__VLS_StyleScopedClasses['icon-wrapper']} */ ;
/** @type {__VLS_StyleScopedClasses['active']} */ ;
/** @type {__VLS_StyleScopedClasses['tabbar-item']} */ ;
/** @type {__VLS_StyleScopedClasses['icon-wrapper']} */ ;
/** @type {__VLS_StyleScopedClasses['active']} */ ;
/** @type {__VLS_StyleScopedClasses['tabbar-item']} */ ;
/** @type {__VLS_StyleScopedClasses['icon-wrapper']} */ ;
/** @type {__VLS_StyleScopedClasses['active']} */ ;
var __VLS_dollars;
const __VLS_self = (await import('vue')).defineComponent({
    setup() {
        return {};
    },
});
export default (await import('vue')).defineComponent({
    setup() {
        return {};
    },
});
; /* PartiallyEnd: #4569/main.vue */
