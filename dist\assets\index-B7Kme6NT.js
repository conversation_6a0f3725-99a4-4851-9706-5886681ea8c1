import{s as R,v as b,C as y,d as x,u as L,r as o,y as U,l as B,c as _,o as f,b as u,w as g,F as E,e as I,f as G,g as C,_ as F}from"./index-BE8XLQ59.js";import{T as S,a as V}from"./index-CmzFzWyS.js";import"./index-sbxbYdRt.js";import{G as z,a as D}from"./GlobalHeader-lL88u8sR.js";import{C as M}from"./CommonCardList-1yAjUs8b.js";import"./index-B27ddgxZ.js";import"./index-ouF_E0m0.js";import"./index-CAfqjps3.js";function N(){return b(y.CATEGORIES)}function W(r,n=1,t=10){return b(y.CULTURE,{cat:r,page:n,page_size:t})}function q(r,n=1,t=10){return R(()=>W(r,n,t))}const A={class:"news-page category-tabs-container"},H={class:"section-container"},O={class:"news-grid"},j=x({__name:"index",setup(r){const n=L(),t=o(!1),l=o(!1),d=o([]),v=o(1),i=o(0),h=o(5),m=o([]),w=async()=>{const e=await N();console.log("获取到领导分类sdfsdfsd:",e);const s=e.categories.map(a=>({id:a.value,name:a.label}));m.value=[...s]};U(i,e=>{v.value=1,d.value=[],l.value=!1,p()});const p=async()=>{var s;if(l.value||t.value)return;t.value=!0;const e=(s=m.value[i.value])==null?void 0:s.id;console.log("当前选中标签ID:",e);try{const a=await q(e,v.value,h.value);d.value.push(...a.results),v.value+=1,l.value=a.is_last_page===!0}catch(a){console.error("加载失败:",a)}finally{t.value=!1}},T=e=>{n.push({name:"CultureDetail",params:{id:e.id}})};return B(()=>{w(),p()}),(e,s)=>{const a=S,k=V;return f(),_("div",A,[u(z,{title:"中医文化"}),u(k,{active:i.value,"onUpdate:active":s[0]||(s[0]=c=>i.value=c),class:"category-tabs",sticky:"","sticky-offset-top":0,swipeable:""},{default:g(()=>[(f(!0),_(E,null,I(m.value,c=>(f(),G(a,{key:c.id,title:c.name},{default:g(()=>[C("div",H,[C("div",O,[u(M,{items:d.value,loading:t.value,finished:l.value,"use-infinite-scroll":!0,onLoadMore:p,onCardClick:T},null,8,["items","loading","finished"])])])]),_:2},1032,["title"]))),128))]),_:1},8,["active"]),u(D)])}}}),ee=F(j,[["__scopeId","data-v-88e51576"]]);export{ee as default};
