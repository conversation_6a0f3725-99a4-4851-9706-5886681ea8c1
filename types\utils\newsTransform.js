/**
 * 将API数据转换为NewsCard组件需要的格式
 * @param item 医院新闻API数据
 * @returns NewsCard组件数据格式
 */
export function transformHospitalNewsToNewsCard(item) {
    return {
        id: item.id,
        name: item.name,
        desc: item.desc,
        thumbnail: item.thumbnail,
        category: '医院新闻', // 固定分类
        publishDate: item.create_time,
        views: item.viewtimes ? parseInt(item.viewtimes) : undefined
    };
}
/**
 * 批量转换医院新闻数据
 * @param items 医院新闻API数据数组
 * @returns NewsCard组件数据格式数组
 */
export function transformHospitalNewsListToNewsCard(items) {
    return items.map(transformHospitalNewsToNewsCard);
}
/**
 * 格式化日期显示
 * @param dateString 日期字符串
 * @returns 格式化后的日期
 */
export function formatNewsDate(dateString) {
    if (!dateString)
        return undefined;
    try {
        const date = new Date(dateString);
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        return `${year}.${month}.${day}`;
    }
    catch {
        return dateString;
    }
}
