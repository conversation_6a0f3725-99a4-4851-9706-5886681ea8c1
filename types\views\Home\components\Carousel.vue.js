import { ref, watch, onMounted, defineProps, defineEmits } from "vue";
import { getAdvertiseList } from "../api";
import { useRouter } from "vue-router";
import { showToast } from "vant";
const props = defineProps({
    position: {
        type: String,
        required: true,
        description: "广告位置标识，用于获取不同位置的广告数据",
    },
    autoplay: {
        type: Number,
        default: 3000,
        description: "轮播图自动切换时间间隔(ms)",
    },
    height: {
        type: String,
        default: "200px",
        description: "轮播图高度",
    },
});
const emits = defineEmits(["click"]);
const router = useRouter();
// 轮播图数据
const swipeImages = ref([]);
// 当前轮播图索引
const currentSwipe = ref(0);
// 加载状态
const isLoading = ref(true);
// 图片加载状态追踪
const imageLoadingStates = ref([]);
// 图片URL处理 - 确保返回完整的URL
const getFullImageUrl = (thumbnail) => {
    if (!thumbnail)
        return "";
    // 如果已经是完整URL，直接返回
    if (thumbnail.startsWith("http://") || thumbnail.startsWith("https://")) {
        return thumbnail;
    }
    // 如果是相对路径，添加基础域名
    const baseUrl = import.meta.env.MODE === "development"
        ? "https://houma.sxaliyun.cn"
        : "https://houma.sxaliyun.cn";
    // 确保路径以/开头
    const path = thumbnail.startsWith("/") ? thumbnail : `/${thumbnail}`;
    return `${baseUrl}${path}`;
};
// 图片加载成功事件
const onImageLoad = (index) => {
    imageLoadingStates.value[index] = false;
    console.log(`轮播图${index + 1}加载成功`);
};
// 图片加载失败事件
const onImageError = (index) => {
    imageLoadingStates.value[index] = false;
    console.error(`轮播图${index + 1}加载失败`);
};
// 预加载图片
const preloadImages = async (images) => {
    console.log("开始预加载轮播图片...");
    // 初始化加载状态
    imageLoadingStates.value = new Array(images.length).fill(true);
    // 预加载第一张图片（最重要）
    if (images.length > 0) {
        const firstImage = new Image();
        firstImage.src = getFullImageUrl(images[0].thumbnail);
        firstImage.onload = () => {
            console.log("首张轮播图预加载完成");
            imageLoadingStates.value[0] = false;
        };
        firstImage.onerror = () => {
            console.error("首张轮播图预加载失败");
            imageLoadingStates.value[0] = false;
        };
    }
    // 预加载其他图片（低优先级）
    images.slice(1).forEach((item, index) => {
        setTimeout(() => {
            const img = new Image();
            img.src = getFullImageUrl(item.thumbnail);
            img.onload = () => {
                console.log(`轮播图${index + 2}预加载完成`);
            };
        }, (index + 1) * 200); // 延迟预加载，避免阻塞首张图片
    });
};
// 轮播图切换事件
const onSwipeChange = (index) => {
    currentSwipe.value = index;
};
// 获取轮播图数据
const fetchAdvertiseData = async () => {
    try {
        isLoading.value = true;
        // 根据position参数获取对应位置的广告数据
        const res = await getAdvertiseList(props.position);
        console.log(`获取到${props.position}位置的轮播列表:`, res);
        // 检查响应数据结构
        if (!res || !Array.isArray(res) || res.length === 0) {
            swipeImages.value = [];
            return;
        }
        // 将API返回的数据转换为正确的AdvertiseItem格式
        const listData = res.map((item) => ({
            id: typeof item.id === "number" ? item.id : Number(item.id) || 0,
            name: item.name || "",
            linkurl: item.linkurl || "",
            thumbnail: item.thumbnail || item.url || "", // 确保thumbnail存在
        }));
        swipeImages.value = [...listData];
        // 调试信息：输出轮播图数据和链接
        console.log(`${props.position}位置轮播图数据:`, swipeImages.value);
        swipeImages.value.forEach((item, index) => {
            console.log(`轮播图${index + 1}: ${item.name} -> ${item.linkurl}`);
            console.log(`处理后的图片URL: ${getFullImageUrl(item.thumbnail)}`);
        });
        // 预加载图片
        preloadImages(swipeImages.value);
    }
    catch (error) {
        console.error(`获取${props.position}位置的广告数据失败:`, error);
        showToast("获取数据失败，请稍后重试");
    }
    finally {
        isLoading.value = false;
    }
};
/**
 * 处理轮播图点击事件 - 智能链接处理
 * 支持的链接格式：
 * 1. 完整项目URL: https://houma.sxaliyun.cn/#/tcm-knowledge-detail/3
 * 2. 外部链接: https://www.baidu.com
 * 3. 内部路由: /tcm-knowledge-detail/3
 * 4. 相对路径: tcm-knowledge-detail/3 (会自动加上/)
 */
// 处理卡片点击
const handleItemClick = (item) => {
    if (!item.linkurl) {
        showToast("链接地址不存在");
        return;
    }
    console.log("轮播图点击链接:", item.linkurl);
    // 触发自定义点击事件，允许父组件处理导航
    emits("click", item);
    // 智能链接处理
    try {
        const linkUrl = item.linkurl.trim();
        // 空链接检查
        if (!linkUrl) {
            showToast("链接地址为空");
            return;
        }
        // 1. 检查是否是完整的项目URL（包含域名和Hash路由）
        if (linkUrl.includes("#/")) {
            // 提取Hash后面的路由路径
            const hashIndex = linkUrl.indexOf("#/");
            const routePath = linkUrl.substring(hashIndex + 1); // 去掉#，保留/
            console.log("提取的路由路径:", routePath);
            // 验证提取的路径是否有效
            if (routePath && routePath.length > 1) {
                router.push(routePath);
            }
            else {
                showToast("无效的路由路径");
            }
            return;
        }
        // 2. 检查是否是外部链接（http/https开头但不包含项目Hash路由）
        if ((linkUrl.startsWith("http://") || linkUrl.startsWith("https://")) &&
            !linkUrl.includes("#/")) {
            // 外部链接在新窗口打开
            window.open(linkUrl, "_blank");
            return;
        }
        // 3. 检查是否是项目内部路由路径（以/开头）
        if (linkUrl.startsWith("/")) {
            router.push(linkUrl);
            return;
        }
        // 4. 其他情况，尝试作为相对路径处理（自动添加/前缀）
        const relativePath = `/${linkUrl}`;
        console.log("作为相对路径处理:", relativePath);
        router.push(relativePath);
    }
    catch (error) {
        console.error("轮播图链接跳转失败:", error);
        showToast("链接跳转失败，请联系管理员检查链接格式");
    }
};
// 组件挂载时获取数据
onMounted(() => {
    fetchAdvertiseData();
});
// 监听position变化，重新获取数据
watch(() => props.position, (newPosition, oldPosition) => {
    if (newPosition !== oldPosition) {
        fetchAdvertiseData();
    }
}, { immediate: false });
debugger; /* PartiallyEnd: #3632/scriptSetup.vue */
const __VLS_ctx = {};
let __VLS_components;
let __VLS_directives;
/** @type {__VLS_StyleScopedClasses['swipe-container']} */ ;
/** @type {__VLS_StyleScopedClasses['swipe-item-content']} */ ;
/** @type {__VLS_StyleScopedClasses['swipe-image']} */ ;
/** @type {__VLS_StyleScopedClasses['indicator-dot']} */ ;
/** @type {__VLS_StyleScopedClasses['skeleton-item']} */ ;
/** @type {__VLS_StyleScopedClasses['skeleton-item']} */ ;
/** @type {__VLS_StyleScopedClasses['skeleton-item']} */ ;
/** @type {__VLS_StyleScopedClasses['swipe-image']} */ ;
/** @type {__VLS_StyleScopedClasses['swipe-image']} */ ;
// CSS variable injection 
// CSS variable injection end 
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ class: "section-container" },
});
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ class: "swipe-container animate__animated animate__fadeIn" },
});
if (__VLS_ctx.isLoading) {
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
        ...{ class: "skeleton-swipe" },
    });
    for (const [i] of __VLS_getVForSourceType((3))) {
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "skeleton-item" },
            key: (i),
        });
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "skeleton-image" },
        });
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "skeleton-caption" },
        });
    }
}
else if (__VLS_ctx.swipeImages.length > 0) {
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({});
    const __VLS_0 = {}.VanSwipe;
    /** @type {[typeof __VLS_components.VanSwipe, typeof __VLS_components.vanSwipe, typeof __VLS_components.VanSwipe, typeof __VLS_components.vanSwipe, ]} */ ;
    // @ts-ignore
    const __VLS_1 = __VLS_asFunctionalComponent(__VLS_0, new __VLS_0({
        ...{ 'onChange': {} },
        autoplay: (__VLS_ctx.autoplay),
        showIndicators: (false),
        ...{ class: "my-swipe" },
    }));
    const __VLS_2 = __VLS_1({
        ...{ 'onChange': {} },
        autoplay: (__VLS_ctx.autoplay),
        showIndicators: (false),
        ...{ class: "my-swipe" },
    }, ...__VLS_functionalComponentArgsRest(__VLS_1));
    let __VLS_4;
    let __VLS_5;
    let __VLS_6;
    const __VLS_7 = {
        onChange: (__VLS_ctx.onSwipeChange)
    };
    __VLS_3.slots.default;
    for (const [item, index] of __VLS_getVForSourceType((__VLS_ctx.swipeImages))) {
        const __VLS_8 = {}.VanSwipeItem;
        /** @type {[typeof __VLS_components.VanSwipeItem, typeof __VLS_components.vanSwipeItem, typeof __VLS_components.VanSwipeItem, typeof __VLS_components.vanSwipeItem, ]} */ ;
        // @ts-ignore
        const __VLS_9 = __VLS_asFunctionalComponent(__VLS_8, new __VLS_8({
            ...{ 'onClick': {} },
            key: (item.id),
        }));
        const __VLS_10 = __VLS_9({
            ...{ 'onClick': {} },
            key: (item.id),
        }, ...__VLS_functionalComponentArgsRest(__VLS_9));
        let __VLS_12;
        let __VLS_13;
        let __VLS_14;
        const __VLS_15 = {
            onClick: (...[$event]) => {
                if (!!(__VLS_ctx.isLoading))
                    return;
                if (!(__VLS_ctx.swipeImages.length > 0))
                    return;
                __VLS_ctx.handleItemClick(item);
            }
        };
        __VLS_11.slots.default;
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "swipe-item-content" },
        });
        if (index === 0) {
            __VLS_asFunctionalElement(__VLS_intrinsicElements.img)({
                ...{ onLoad: (...[$event]) => {
                        if (!!(__VLS_ctx.isLoading))
                            return;
                        if (!(__VLS_ctx.swipeImages.length > 0))
                            return;
                        if (!(index === 0))
                            return;
                        __VLS_ctx.onImageLoad(index);
                    } },
                ...{ onError: (...[$event]) => {
                        if (!!(__VLS_ctx.isLoading))
                            return;
                        if (!(__VLS_ctx.swipeImages.length > 0))
                            return;
                        if (!(index === 0))
                            return;
                        __VLS_ctx.onImageError(index);
                    } },
                src: (__VLS_ctx.getFullImageUrl(item.thumbnail)),
                ...{ class: "swipe-image" },
                ...{ style: ({ height: __VLS_ctx.height }) },
                alt: (item.name),
            });
        }
        else {
            __VLS_asFunctionalElement(__VLS_intrinsicElements.img)({
                ...{ onLoad: (...[$event]) => {
                        if (!!(__VLS_ctx.isLoading))
                            return;
                        if (!(__VLS_ctx.swipeImages.length > 0))
                            return;
                        if (!!(index === 0))
                            return;
                        __VLS_ctx.onImageLoad(index);
                    } },
                ...{ onError: (...[$event]) => {
                        if (!!(__VLS_ctx.isLoading))
                            return;
                        if (!(__VLS_ctx.swipeImages.length > 0))
                            return;
                        if (!!(index === 0))
                            return;
                        __VLS_ctx.onImageError(index);
                    } },
                ...{ class: "swipe-image" },
                ...{ style: ({ height: __VLS_ctx.height }) },
                alt: (item.name),
            });
            __VLS_asFunctionalDirective(__VLS_directives.vLazy)(null, { ...__VLS_directiveBindingRestFields, value: (__VLS_ctx.getFullImageUrl(item.thumbnail)) }, null, null);
        }
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "swipe-caption" },
        });
        (item.name);
        if (__VLS_ctx.imageLoadingStates[index]) {
            __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
                ...{ class: "image-loading" },
            });
            const __VLS_16 = {}.VanLoading;
            /** @type {[typeof __VLS_components.VanLoading, typeof __VLS_components.vanLoading, ]} */ ;
            // @ts-ignore
            const __VLS_17 = __VLS_asFunctionalComponent(__VLS_16, new __VLS_16({
                type: "spinner",
                color: "#fff",
            }));
            const __VLS_18 = __VLS_17({
                type: "spinner",
                color: "#fff",
            }, ...__VLS_functionalComponentArgsRest(__VLS_17));
        }
        var __VLS_11;
    }
    var __VLS_3;
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
        ...{ class: "custom-indicator" },
    });
    for (const [_, index] of __VLS_getVForSourceType((__VLS_ctx.swipeImages))) {
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            key: (index),
            ...{ class: (['indicator-dot', { active: __VLS_ctx.currentSwipe === index }]) },
        });
    }
}
else {
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
        ...{ class: "no-data" },
    });
    const __VLS_20 = {}.VanEmpty;
    /** @type {[typeof __VLS_components.VanEmpty, typeof __VLS_components.vanEmpty, ]} */ ;
    // @ts-ignore
    const __VLS_21 = __VLS_asFunctionalComponent(__VLS_20, new __VLS_20({
        image: "empty",
        description: "暂无数据",
    }));
    const __VLS_22 = __VLS_21({
        image: "empty",
        description: "暂无数据",
    }, ...__VLS_functionalComponentArgsRest(__VLS_21));
}
/** @type {__VLS_StyleScopedClasses['section-container']} */ ;
/** @type {__VLS_StyleScopedClasses['swipe-container']} */ ;
/** @type {__VLS_StyleScopedClasses['animate__animated']} */ ;
/** @type {__VLS_StyleScopedClasses['animate__fadeIn']} */ ;
/** @type {__VLS_StyleScopedClasses['skeleton-swipe']} */ ;
/** @type {__VLS_StyleScopedClasses['skeleton-item']} */ ;
/** @type {__VLS_StyleScopedClasses['skeleton-image']} */ ;
/** @type {__VLS_StyleScopedClasses['skeleton-caption']} */ ;
/** @type {__VLS_StyleScopedClasses['my-swipe']} */ ;
/** @type {__VLS_StyleScopedClasses['swipe-item-content']} */ ;
/** @type {__VLS_StyleScopedClasses['swipe-image']} */ ;
/** @type {__VLS_StyleScopedClasses['swipe-image']} */ ;
/** @type {__VLS_StyleScopedClasses['swipe-caption']} */ ;
/** @type {__VLS_StyleScopedClasses['image-loading']} */ ;
/** @type {__VLS_StyleScopedClasses['custom-indicator']} */ ;
/** @type {__VLS_StyleScopedClasses['active']} */ ;
/** @type {__VLS_StyleScopedClasses['indicator-dot']} */ ;
/** @type {__VLS_StyleScopedClasses['no-data']} */ ;
var __VLS_dollars;
const __VLS_self = (await import('vue')).defineComponent({
    setup() {
        return {
            swipeImages: swipeImages,
            currentSwipe: currentSwipe,
            isLoading: isLoading,
            imageLoadingStates: imageLoadingStates,
            getFullImageUrl: getFullImageUrl,
            onImageLoad: onImageLoad,
            onImageError: onImageError,
            onSwipeChange: onSwipeChange,
            handleItemClick: handleItemClick,
        };
    },
    emits: {},
    props: {
        position: {
            type: String,
            required: true,
            description: "广告位置标识，用于获取不同位置的广告数据",
        },
        autoplay: {
            type: Number,
            default: 3000,
            description: "轮播图自动切换时间间隔(ms)",
        },
        height: {
            type: String,
            default: "200px",
            description: "轮播图高度",
        },
    },
});
export default (await import('vue')).defineComponent({
    setup() {
        return {};
    },
    emits: {},
    props: {
        position: {
            type: String,
            required: true,
            description: "广告位置标识，用于获取不同位置的广告数据",
        },
        autoplay: {
            type: Number,
            default: 3000,
            description: "轮播图自动切换时间间隔(ms)",
        },
        height: {
            type: String,
            default: "200px",
            description: "轮播图高度",
        },
    },
});
; /* PartiallyEnd: #4569/main.vue */
