import{a8 as ee,R as ue,Q as W,a9 as re,d as te,Y as ve,S as de,$ as Fe,b as o,a1 as Ne,aa as G,I as Q,X as be,T as Ue,ab as we,ac as _e,ad as ge,ae as Ge,af as We,ag as He,ah as Ke,ai as Ye,aj as Xe,r as M,Z as xe,k as q,U as ce,y as Ie,B as me,l as Pe,ak as Je,al as Se,am as Qe,an as Ze,O as et,j as J,a2 as ye,ao as tt,ap as nt,aq as at,ar as lt,as as ot,at as it,au as Te,av as Ee,u as st,a as rt,q as ct,c as ie,o as se,g as n,L as ut,w as O,H as dt,t as F,p as Ce,P as ft}from"./index-BE8XLQ59.js";import{u as mt,E as gt}from"./index-B27ddgxZ.js";import{b as vt,f as bt,i as ht}from"./dateTime-CGItJ1-U.js";import{s as N}from"./function-call-BUl5915X.js";const[yt,Z]=de("cell"),Be={tag:re("div"),icon:String,size:String,title:W,value:W,label:W,center:Boolean,isLink:Boolean,border:ue,iconPrefix:String,valueClass:ee,labelClass:ee,titleClass:ee,titleStyle:null,arrowDirection:String,required:{type:[Boolean,String],default:null},clickable:{type:Boolean,default:null}},_t=ve({},Be,Ne);var kt=te({name:yt,props:_t,setup(e,{slots:l}){const u=Fe(),d=()=>{if(l.label||G(e.label))return o("div",{class:[Z("label"),e.labelClass]},[l.label?l.label():e.label])},s=()=>{var c;if(l.title||G(e.title)){const y=(c=l.title)==null?void 0:c.call(l);return Array.isArray(y)&&y.length===0?void 0:o("div",{class:[Z("title"),e.titleClass],style:e.titleStyle},[y||o("span",null,[e.title]),d()])}},r=()=>{const c=l.value||l.default;if(c||G(e.value))return o("div",{class:[Z("value"),e.valueClass]},[c?c():o("span",null,[e.value])])},T=()=>{if(l.icon)return l.icon();if(e.icon)return o(Q,{name:e.icon,class:Z("left-icon"),classPrefix:e.iconPrefix},null)},S=()=>{if(l["right-icon"])return l["right-icon"]();if(e.isLink){const c=e.arrowDirection&&e.arrowDirection!=="right"?`arrow-${e.arrowDirection}`:"arrow";return o(Q,{name:c,class:Z("right-icon")},null)}};return()=>{var c;const{tag:y,size:b,center:x,border:f,isLink:h,required:I}=e,_=(c=e.clickable)!=null?c:h,i={center:x,required:!!I,clickable:_,borderless:!f};return b&&(i[b]=!!b),o(y,{class:Z(i),role:_?"button":void 0,tabindex:_?0:void 0,onClick:u},{default:()=>{var m;return[T(),s(),r(),S(),(m=l.extra)==null?void 0:m.call(l)]}})}}});const St=be(kt),[Ct,pt]=de("form"),Vt={colon:Boolean,disabled:Boolean,readonly:Boolean,required:[Boolean,String],showError:Boolean,labelWidth:W,labelAlign:String,inputAlign:String,scrollToError:Boolean,scrollToErrorPosition:String,validateFirst:Boolean,submitOnEnter:ue,showErrorMessage:ue,errorMessageAlign:String,validateTrigger:{type:[String,Array],default:"onBlur"}};var wt=te({name:Ct,props:Vt,emits:["submit","failed"],setup(e,{emit:l,slots:u}){const{children:d,linkChildren:s}=Ue(we),r=i=>i?d.filter(m=>i.includes(m.name)):d,T=i=>new Promise((m,w)=>{const L=[];r(i).reduce((ne,H)=>ne.then(()=>{if(!L.length)return H.validate().then(K=>{K&&L.push(K)})}),Promise.resolve()).then(()=>{L.length?w(L):m()})}),S=i=>new Promise((m,w)=>{const L=r(i);Promise.all(L.map(E=>E.validate())).then(E=>{E=E.filter(Boolean),E.length?w(E):m()})}),c=i=>{const m=d.find(w=>w.name===i);return m?new Promise((w,L)=>{m.validate().then(E=>{E?L(E):w()})}):Promise.reject()},y=i=>typeof i=="string"?c(i):e.validateFirst?T(i):S(i),b=i=>{typeof i=="string"&&(i=[i]),r(i).forEach(w=>{w.resetValidation()})},x=()=>d.reduce((i,m)=>(i[m.name]=m.getValidationStatus(),i),{}),f=(i,m)=>{d.some(w=>w.name===i?(w.$el.scrollIntoView(m),!0):!1)},h=()=>d.reduce((i,m)=>(m.name!==void 0&&(i[m.name]=m.formValue.value),i),{}),I=()=>{const i=h();y().then(()=>l("submit",i)).catch(m=>{l("failed",{values:i,errors:m});const{scrollToError:w,scrollToErrorPosition:L}=e;w&&m[0].name&&f(m[0].name,L?{block:L}:void 0)})},_=i=>{ge(i),I()};return s({props:e}),_e({submit:I,validate:y,getValues:h,scrollToField:f,resetValidation:b,getValidationStatus:x}),()=>{var i;return o("form",{class:pt(),onSubmit:_},[(i=u.default)==null?void 0:i.call(u)])}}});const xt=be(wt);function Ae(e){return Array.isArray(e)?!e.length:e===0?!1:!e}function It(e,l){if(Ae(e)){if(l.required)return!1;if(l.validateEmpty===!1)return!0}return!(l.pattern&&!l.pattern.test(String(e)))}function Pt(e,l){return new Promise(u=>{const d=l.validator(e,l);if(Ye(d)){d.then(u);return}u(d)})}function pe(e,l){const{message:u}=l;return Ke(u)?u(e,l):u||""}function Tt({target:e}){e.composing=!0}function Ve({target:e}){e.composing&&(e.composing=!1,e.dispatchEvent(new Event("input")))}function Et(e,l){const u=Ge();e.style.height="auto";let d=e.scrollHeight;if(We(l)){const{maxHeight:s,minHeight:r}=l;s!==void 0&&(d=Math.min(d,s)),r!==void 0&&(d=Math.max(d,r))}d&&(e.style.height=`${d}px`,He(u))}function Bt(e,l){return e==="number"&&(e="text",l??(l="decimal")),e==="digit"&&(e="tel",l??(l="numeric")),{type:e,inputmode:l}}function U(e){return[...e].length}function he(e,l){return[...e].slice(0,l).join("")}const[At,$]=de("field"),Mt={id:String,name:String,leftIcon:String,rightIcon:String,autofocus:Boolean,clearable:Boolean,maxlength:W,max:Number,min:Number,formatter:Function,clearIcon:re("clear"),modelValue:et(""),inputAlign:String,placeholder:String,autocomplete:String,autocapitalize:String,autocorrect:String,errorMessage:String,enterkeyhint:String,clearTrigger:re("focus"),formatTrigger:re("onChange"),spellcheck:{type:Boolean,default:null},error:{type:Boolean,default:null},disabled:{type:Boolean,default:null},readonly:{type:Boolean,default:null},inputmode:String},Lt=ve({},Be,Mt,{rows:W,type:re("text"),rules:Array,autosize:[Boolean,Object],labelWidth:W,labelClass:ee,labelAlign:String,showWordLimit:Boolean,errorMessageAlign:String,colon:{type:Boolean,default:null}});var Rt=te({name:At,props:Lt,emits:["blur","focus","clear","keypress","clickInput","endValidate","startValidate","clickLeftIcon","clickRightIcon","update:modelValue"],setup(e,{emit:l,slots:u}){const d=mt(),s=Xe({status:"unvalidated",focused:!1,validateMessage:""}),r=M(),T=M(),S=M(),{parent:c}=xe(we),y=()=>{var t;return String((t=e.modelValue)!=null?t:"")},b=t=>{if(G(e[t]))return e[t];if(c&&G(c.props[t]))return c.props[t]},x=q(()=>{const t=b("readonly");if(e.clearable&&!t){const g=y()!=="",v=e.clearTrigger==="always"||e.clearTrigger==="focus"&&s.focused;return g&&v}return!1}),f=q(()=>S.value&&u.input?S.value():e.modelValue),h=q(()=>{var t;const g=b("required");return g==="auto"?(t=e.rules)==null?void 0:t.some(v=>v.required):g}),I=t=>t.reduce((g,v)=>g.then(()=>{if(s.status==="failed")return;let{value:V}=f;if(v.formatter&&(V=v.formatter(V,v)),!It(V,v)){s.status="failed",s.validateMessage=pe(V,v);return}if(v.validator)return Ae(V)&&v.validateEmpty===!1?void 0:Pt(V,v).then(A=>{A&&typeof A=="string"?(s.status="failed",s.validateMessage=A):A===!1&&(s.status="failed",s.validateMessage=pe(V,v))})}),Promise.resolve()),_=()=>{s.status="unvalidated",s.validateMessage=""},i=()=>l("endValidate",{status:s.status,message:s.validateMessage}),m=(t=e.rules)=>new Promise(g=>{_(),t?(l("startValidate"),I(t).then(()=>{s.status==="failed"?(g({name:e.name,message:s.validateMessage}),i()):(s.status="passed",g(),i())})):g()}),w=t=>{if(c&&e.rules){const{validateTrigger:g}=c.props,v=Se(g).includes(t),V=e.rules.filter(A=>A.trigger?Se(A.trigger).includes(t):v);V.length&&m(V)}},L=t=>{var g;const{maxlength:v}=e;if(G(v)&&U(t)>+v){const V=y();if(V&&U(V)===+v)return V;const A=(g=r.value)==null?void 0:g.selectionEnd;if(s.focused&&A){const Y=[...t],X=Y.length-+v;return Y.splice(A-X,X),Y.join("")}return he(t,+v)}return t},E=(t,g="onChange")=>{var v,V;const A=t;t=L(t);const Y=U(A)-U(t);if(e.type==="number"||e.type==="digit"){const j=e.type==="number";if(t=Qe(t,j,j),g==="onBlur"&&t!==""&&(e.min!==void 0||e.max!==void 0)){const z=Ze(+t,(v=e.min)!=null?v:-1/0,(V=e.max)!=null?V:1/0);+t!==z&&(t=z.toString())}}let X=0;if(e.formatter&&g===e.formatTrigger){const{formatter:j,maxlength:z}=e;if(t=j(t),G(z)&&U(t)>+z&&(t=he(t,+z)),r.value&&s.focused){const{selectionEnd:fe}=r.value,ke=he(A,fe);X=U(j(ke))-U(ke)}}if(r.value&&r.value.value!==t)if(s.focused){let{selectionStart:j,selectionEnd:z}=r.value;if(r.value.value=t,G(j)&&G(z)){const fe=U(t);Y?(j-=Y,z-=Y):X&&(j+=X,z+=X),r.value.setSelectionRange(Math.min(j,fe),Math.min(z,fe))}}else r.value.value=t;t!==e.modelValue&&l("update:modelValue",t)},ne=t=>{t.target.composing||E(t.target.value)},H=()=>{var t;return(t=r.value)==null?void 0:t.blur()},K=()=>{var t;return(t=r.value)==null?void 0:t.focus()},C=()=>{const t=r.value;e.type==="textarea"&&e.autosize&&t&&Et(t,e.autosize)},a=t=>{s.focused=!0,l("focus",t),me(C),b("readonly")&&H()},k=t=>{s.focused=!1,E(y(),"onBlur"),l("blur",t),!b("readonly")&&(w("onBlur"),me(C),tt())},B=t=>l("clickInput",t),p=t=>l("clickLeftIcon",t),P=t=>l("clickRightIcon",t),D=t=>{ge(t),l("update:modelValue",""),l("clear",t)},ae=q(()=>{if(typeof e.error=="boolean")return e.error;if(c&&c.props.showError&&s.status==="failed")return!0}),le=q(()=>{const t=b("labelWidth"),g=b("labelAlign");if(t&&g!=="top")return{width:ce(t)}}),oe=t=>{t.keyCode===13&&(!(c&&c.props.submitOnEnter)&&e.type!=="textarea"&&ge(t),e.type==="search"&&H()),l("keypress",t)},R=()=>e.id||`${d}-input`,Le=()=>s.status,Re=()=>{const t=$("control",[b("inputAlign"),{error:ae.value,custom:!!u.input,"min-height":e.type==="textarea"&&!e.autosize}]);if(u.input)return o("div",{class:t,onClick:B},[u.input()]);const g={id:R(),ref:r,name:e.name,rows:e.rows!==void 0?+e.rows:void 0,class:t,disabled:b("disabled"),readonly:b("readonly"),autofocus:e.autofocus,placeholder:e.placeholder,autocomplete:e.autocomplete,autocapitalize:e.autocapitalize,autocorrect:e.autocorrect,enterkeyhint:e.enterkeyhint,spellcheck:e.spellcheck,"aria-labelledby":e.label?`${d}-label`:void 0,"data-allow-mismatch":"attribute",onBlur:k,onFocus:a,onInput:ne,onClick:B,onChange:Ve,onKeypress:oe,onCompositionend:Ve,onCompositionstart:Tt};return e.type==="textarea"?o("textarea",ye(g,{inputmode:e.inputmode}),null):o("input",ye(Bt(e.type,e.inputmode),g),null)},$e=()=>{const t=u["left-icon"];if(e.leftIcon||t)return o("div",{class:$("left-icon"),onClick:p},[t?t():o(Q,{name:e.leftIcon,classPrefix:e.iconPrefix},null)])},qe=()=>{const t=u["right-icon"];if(e.rightIcon||t)return o("div",{class:$("right-icon"),onClick:P},[t?t():o(Q,{name:e.rightIcon,classPrefix:e.iconPrefix},null)])},De=()=>{if(e.showWordLimit&&e.maxlength){const t=U(y());return o("div",{class:$("word-limit")},[o("span",{class:$("word-num")},[t]),J("/"),e.maxlength])}},ze=()=>{if(c&&c.props.showErrorMessage===!1)return;const t=e.errorMessage||s.validateMessage;if(t){const g=u["error-message"],v=b("errorMessageAlign");return o("div",{class:$("error-message",v)},[g?g({message:t}):t])}},Oe=()=>{const t=b("labelWidth"),g=b("labelAlign"),v=b("colon")?":":"";if(u.label)return[u.label(),v];if(e.label)return o("label",{id:`${d}-label`,for:u.input?void 0:R(),"data-allow-mismatch":"attribute",onClick:V=>{ge(V),K()},style:g==="top"&&t?{width:ce(t)}:void 0},[e.label+v])},je=()=>[o("div",{class:$("body")},[Re(),x.value&&o(Q,{ref:T,name:e.clearIcon,class:$("clear")},null),qe(),u.button&&o("div",{class:$("button")},[u.button()])]),De(),ze()];return _e({blur:H,focus:K,validate:m,formValue:f,resetValidation:_,getValidationStatus:Le}),nt(at,{customValue:S,resetValidation:_,validateWithTrigger:w}),Ie(()=>e.modelValue,()=>{E(y()),_(),w("onChange"),me(C)}),Pe(()=>{E(y(),e.formatTrigger),me(C)}),Je("touchstart",D,{target:q(()=>{var t;return(t=T.value)==null?void 0:t.$el})}),()=>{const t=b("disabled"),g=b("labelAlign"),v=$e(),V=()=>{const A=Oe();return g==="top"?[v,A].filter(Boolean):A||[]};return o(St,{size:e.size,class:$({error:ae.value,disabled:t,[`label-${g}`]:g}),center:e.center,border:e.border,isLink:e.isLink,clickable:e.clickable,titleStyle:le.value,valueClass:$("value"),titleClass:[$("label",[g,{required:h.value}]),e.labelClass],arrowDirection:e.arrowDirection},{icon:v&&g!=="top"?()=>v:null,title:V,value:je,extra:u.extra})}}});const $t=be(Rt),[qt,qn]=de("checkbox-group"),Dt=Symbol(qt),Me={name:ee,disabled:Boolean,iconSize:W,modelValue:ee,checkedColor:String,labelPosition:String,labelDisabled:Boolean};var zt=te({props:ve({},Me,{bem:lt(Function),role:String,shape:String,parent:Object,checked:Boolean,bindGroup:ue,indeterminate:{type:Boolean,default:null}}),emits:["click","toggle"],setup(e,{emit:l,slots:u}){const d=M(),s=f=>{if(e.parent&&e.bindGroup)return e.parent.props[f]},r=q(()=>{if(e.parent&&e.bindGroup){const f=s("disabled")||e.disabled;if(e.role==="checkbox"){const h=s("modelValue").length,I=s("max"),_=I&&h>=+I;return f||_&&!e.checked}return f}return e.disabled}),T=q(()=>s("direction")),S=q(()=>{const f=e.checkedColor||s("checkedColor");if(f&&e.checked&&!r.value)return{borderColor:f,backgroundColor:f}}),c=q(()=>e.shape||s("shape")||"round"),y=f=>{const{target:h}=f,I=d.value,_=I===h||(I==null?void 0:I.contains(h));!r.value&&(_||!e.labelDisabled)&&l("toggle"),l("click",f)},b=()=>{var f,h;const{bem:I,checked:_,indeterminate:i}=e,m=e.iconSize||s("iconSize");return o("div",{ref:d,class:I("icon",[c.value,{disabled:r.value,checked:_,indeterminate:i}]),style:c.value!=="dot"?{fontSize:ce(m)}:{width:ce(m),height:ce(m),borderColor:(f=S.value)==null?void 0:f.borderColor}},[u.icon?u.icon({checked:_,disabled:r.value}):c.value!=="dot"?o(Q,{name:i?"minus":"success",style:S.value},null):o("div",{class:I("icon--dot__icon"),style:{backgroundColor:(h=S.value)==null?void 0:h.backgroundColor}},null)])},x=()=>{const{checked:f}=e;if(u.default)return o("span",{class:e.bem("label",[e.labelPosition,{disabled:r.value}])},[u.default({checked:f,disabled:r.value})])};return()=>{const f=e.labelPosition==="left"?[x(),b()]:[b(),x()];return o("div",{role:e.role,class:e.bem([{disabled:r.value,"label-disabled":e.labelDisabled},T.value]),tabindex:r.value?void 0:0,"aria-checked":e.checked,onClick:y},[f])}}});const[Ot,jt]=de("checkbox"),Ft=ve({},Me,{shape:String,bindGroup:ue,indeterminate:{type:Boolean,default:null}});var Nt=te({name:Ot,props:Ft,emits:["change","update:modelValue"],setup(e,{emit:l,slots:u}){const{parent:d}=xe(Dt),s=S=>{const{name:c}=e,{max:y,modelValue:b}=d.props,x=b.slice();if(S)!(y&&x.length>=+y)&&!x.includes(c)&&(x.push(c),e.bindGroup&&d.updateValue(x));else{const f=x.indexOf(c);f!==-1&&(x.splice(f,1),e.bindGroup&&d.updateValue(x))}},r=q(()=>d&&e.bindGroup?d.props.modelValue.indexOf(e.name)!==-1:!!e.modelValue),T=(S=!r.value)=>{d&&e.bindGroup?s(S):l("update:modelValue",S),e.indeterminate!==null&&l("change",S)};return Ie(()=>e.modelValue,S=>{e.indeterminate===null&&l("change",S)}),_e({toggle:T,props:e,checked:r}),ot(()=>e.modelValue),()=>o(zt,ye({bem:jt,role:"checkbox",parent:d,checked:r.value,onToggle:T},e),it(u,["default","icon"]))}});const Ut=be(Nt);function Gt(e){return Te(Ee.REGIST,e)}function Wt(e){return Te(Ee.SENDSMS,e)}const Ht={class:"activity-registration-page"},Kt={class:"custom-header"},Yt={class:"header-content"},Xt={key:0,class:"loading-container"},Jt={key:1,class:"error-container"},Qt={key:2,class:"success-page"},Zt={class:"success-container"},en={class:"success-header"},tn={class:"registration-info-card"},nn={class:"info-item"},an={class:"value"},ln={class:"info-item"},on={class:"value"},sn={class:"info-item"},rn={class:"value"},cn={class:"info-item"},un={class:"value"},dn={class:"info-item"},fn={class:"value"},mn={class:"qrcode-card"},gn={class:"qrcode-container"},vn=["src"],bn={class:"success-actions"},hn={key:3,class:"content"},yn={class:"activity-info-card"},_n={class:"activity-image-container"},kn=["src","alt"],Sn={class:"activity-info"},Cn={class:"activity-title"},pn={class:"activity-meta"},Vn={class:"meta-item"},wn={class:"meta-item"},xn={class:"registration-form-container"},In={class:"form-section"},Pn={class:"agreement-section"},Tn={class:"submit-section"},En={class:"agreement-popup"},Bn={class:"popup-header"},An={class:"popup-footer"},Dn=te({__name:"index",setup(e){const l=st(),u=rt(),d=M(!1),s=M(""),r=M(!1),T=M(!1),S=M(),c=M(!1),y=M(0),b=M(null),x=M(!1),f=M({id:0,regno:"",name:"",phone:"",age:"",create_time:"",qrcode:""}),h=M({name:"",phone:"",verificationCode:"",age:"",agreeTerms:!1}),I=()=>{l.back()},_=M({id:0,name:"",cat:"",cat_name:"",desc:"",thumbnail:"",isfree:0,startdate:"",enddate:"",views:0,location:"",content:""}),i=async()=>{const C=Array.isArray(u.params.id)?u.params.id[0]:u.params.id;if(console.log("获取活动详情，ID:",C),!C){s.value="活动ID无效";return}try{d.value=!0;const a=await ht(C);console.log("获取到活动详情数据:",a);const k=a.activity;_.value={id:k.id,name:k.name,cat:k.cat,cat_name:k.cat_name,desc:k.desc,startdate:k.startdate,enddate:k.enddate,location:k.location,thumbnail:k.thumbnail,views:k.views,isfree:k.isfree,content:k.content||""}}catch(a){console.error("获取活动详情失败:",a),s.value="获取活动详情失败，请稍后再试"}finally{d.value=!1}},m=q(()=>/^1[3-9]\d{9}$/.test(h.value.phone)),w=()=>y.value>0?`${y.value}s`:c.value?"发送中":"获取验证码",L=async()=>{if(!m.value){N({type:"fail",message:"请输入正确的手机号"});return}try{c.value=!0,console.log("发送验证码到手机号:",h.value.phone);const C=await Wt({phone:h.value.phone});if("message"in C)N({type:"success",message:C.message});else if("error"in C){N({type:"fail",message:C.error});return}y.value=60,b.value=setInterval(()=>{y.value--,y.value<=0&&(clearInterval(b.value),b.value=null)},1e3)}catch(C){console.error("发送短信失败:",C),N({type:"fail",message:"发送失败，请稍后再试"})}finally{c.value=!1}},E=()=>{T.value=!0},ne=()=>{h.value.agreeTerms=!0,T.value=!1,N({type:"success",message:"已同意协议"})},H=async C=>{var a,k;r.value=!0,console.log("提交报名信息:",C);try{if(await((a=S.value)==null?void 0:a.validate()),!h.value.phone||!C.verificationCode){N({type:"fail",message:"手机号和验证码不能为空"});return}const B={activity:_.value.id,name:C.name,phone:h.value.phone,code:C.verificationCode,age:C.age};console.log("发送报名请求参数:",B);const p=await Gt(B);if(console.log("报名接口返回:",p),"success"in p&&p.success===!1){let D=p.message||"报名失败";p.errors&&(D=Object.entries(p.errors).map(([le,oe])=>`${le}: ${oe.join(", ")}`).join("; ")||D),N({type:"fail",message:D});return}const P="results"in p?p.results:null;if(!P)throw new Error("报名失败：返回数据格式错误");f.value={id:P.id||0,regno:P.regno||"",name:P.name||"",phone:P.phone||"",age:P.age||"",create_time:P.create_time||"",qrcode:P.qrcode||""},x.value=!0,N({type:"success",message:"报名成功！"}),console.log("报名成功:",P)}catch(B){console.error("报名失败:",B);let p="报名失败，请稍后再试";if((k=B==null?void 0:B.response)!=null&&k.data){const P=B.response.data;P.message?p=P.message:P.errors&&(p=Object.values(P.errors).flat().join(", "))}else B!=null&&B.message&&(p=B.message);N({type:"fail",message:p})}finally{r.value=!1}},K=()=>{b.value&&(clearInterval(b.value),b.value=null)};return Pe(()=>{i()}),ct(()=>{K()}),(C,a)=>{const k=Q,B=ut,p=dt,P=gt,D=$t,ae=Ut,le=xt,oe=ft;return se(),ie("div",Ht,[n("div",Kt,[n("div",Yt,[o(k,{name:"arrow-left",class:"back-icon",onClick:I}),a[7]||(a[7]=n("span",{class:"header-title"},"活动报名",-1)),a[8]||(a[8]=n("div",{class:"header-placeholder"},null,-1))])]),d.value?(se(),ie("div",Xt,[o(B,{type:"spinner",color:"#4b8bf4",size:"32px"}),a[9]||(a[9]=n("p",null,"加载中...",-1))])):s.value?(se(),ie("div",Jt,[o(P,{description:"加载失败",image:"error"},{description:O(()=>[n("p",null,F(s.value),1)]),default:O(()=>[o(p,{round:"",type:"primary",onClick:i},{default:O(()=>a[10]||(a[10]=[J("重试")])),_:1,__:[10]})]),_:1})])):x.value?(se(),ie("div",Qt,[n("div",Zt,[n("div",en,[o(k,{name:"checked",class:"success-icon"}),a[11]||(a[11]=n("h2",null,"报名成功！",-1)),a[12]||(a[12]=n("p",null,"您已成功报名参加此活动",-1))]),n("div",tn,[a[18]||(a[18]=n("h3",null,"报名信息",-1)),n("div",nn,[a[13]||(a[13]=n("span",{class:"label"},"报名编号：",-1)),n("span",an,F(f.value.regno),1)]),n("div",ln,[a[14]||(a[14]=n("span",{class:"label"},"姓名：",-1)),n("span",on,F(f.value.name),1)]),n("div",sn,[a[15]||(a[15]=n("span",{class:"label"},"手机号：",-1)),n("span",rn,F(f.value.phone),1)]),n("div",cn,[a[16]||(a[16]=n("span",{class:"label"},"年龄：",-1)),n("span",un,F(f.value.age)+"岁",1)]),n("div",dn,[a[17]||(a[17]=n("span",{class:"label"},"报名时间：",-1)),n("span",fn,F(Ce(vt)(f.value.create_time)),1)])]),n("div",mn,[a[19]||(a[19]=n("h3",null,"活动入场码",-1)),a[20]||(a[20]=n("p",null,"请保存此二维码，活动当天凭此码入场",-1)),n("div",gn,[n("img",{src:f.value.qrcode,alt:"活动二维码",class:"qrcode-image"},null,8,vn)])]),a[22]||(a[22]=n("div",{class:"tips-card"},[n("h3",null,"温馨提示"),n("ul",null,[n("li",null,"请妥善保存报名编号和二维码"),n("li",null,"活动开始前30分钟开始入场"),n("li",null,"请携带有效身份证件"),n("li",null,"如有疑问请联系客服：************")])],-1)),n("div",bn,[o(p,{round:"",block:"",type:"primary",onClick:I},{default:O(()=>a[21]||(a[21]=[J(" 返回活动详情 ")])),_:1,__:[21]})])])])):(se(),ie("div",hn,[n("div",yn,[n("div",_n,[n("img",{src:_.value.thumbnail,alt:_.value.name,class:"activity-image"},null,8,kn)]),n("div",Sn,[n("h2",Cn,F(_.value.name),1),n("div",pn,[n("div",Vn,[o(k,{name:"clock-o"}),n("span",null,F(Ce(bt)(_.value.startdate,_.value.enddate)),1)]),n("div",wn,[o(k,{name:"location-o"}),n("span",null,F(_.value.location||"待定"),1)])])])]),n("div",xn,[a[25]||(a[25]=n("div",{class:"form-header"},[n("h3",null,"报名信息"),n("p",null,"请填写以下信息完成报名")],-1)),o(le,{onSubmit:H,ref_key:"formRef",ref:S},{default:O(()=>[n("div",In,[o(D,{modelValue:h.value.name,"onUpdate:modelValue":a[0]||(a[0]=R=>h.value.name=R),name:"name",label:"姓名","label-width":"70px",placeholder:"请输入您的真实姓名",rules:[{required:!0,message:"请填写姓名"}],"left-icon":"user-o",clearable:""},null,8,["modelValue"]),o(D,{modelValue:h.value.phone,"onUpdate:modelValue":a[1]||(a[1]=R=>h.value.phone=R),name:"mobile",label:"手机号","label-width":"70px",placeholder:"请输入手机号码",rules:[{required:!0,message:"请填写手机号"},{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号"}],"left-icon":"phone-o",clearable:""},null,8,["modelValue"]),o(D,{modelValue:h.value.verificationCode,"onUpdate:modelValue":a[2]||(a[2]=R=>h.value.verificationCode=R),name:"verificationCode",label:"验证码","label-width":"70px",placeholder:"请输入短信验证码",rules:[{required:!0,message:"请输入验证码"}],"left-icon":"shield-o",clearable:""},{button:O(()=>[o(p,{size:"small",type:"primary",disabled:!m.value||y.value>0,loading:c.value,onClick:L},{default:O(()=>[J(F(w()),1)]),_:1},8,["disabled","loading"])]),_:1},8,["modelValue"]),o(D,{modelValue:h.value.age,"onUpdate:modelValue":a[3]||(a[3]=R=>h.value.age=R),name:"age",label:"年龄","label-width":"70px",placeholder:"请输入年龄",type:"number",rules:[{required:!0,message:"请填写年龄"},{pattern:/^(?:[1-9][0-9]?|1[01][0-9]|120)$/,message:"请输入正确的年龄(1-120)"}],"left-icon":"friends-o",clearable:""},null,8,["modelValue"])]),n("div",Pn,[o(ae,{modelValue:h.value.agreeTerms,"onUpdate:modelValue":a[4]||(a[4]=R=>h.value.agreeTerms=R)},{default:O(()=>[a[23]||(a[23]=J(" 我已阅读并同意 ")),n("span",{class:"agreement-link",onClick:E},"《活动参与协议》")]),_:1,__:[23]},8,["modelValue"])]),n("div",Tn,[o(p,{round:"",block:"",type:"primary","native-type":"submit",loading:r.value,disabled:!h.value.agreeTerms,"loading-text":"提交中...",class:"submit-btn"},{default:O(()=>a[24]||(a[24]=[J(" 确认报名 ")])),_:1,__:[24]},8,["loading","disabled"])])]),_:1},512)])])),o(oe,{show:T.value,"onUpdate:show":a[6]||(a[6]=R=>T.value=R),position:"bottom",style:{height:"70%"}},{default:O(()=>[n("div",En,[n("div",Bn,[a[26]||(a[26]=n("h3",null,"活动参与协议",-1)),o(k,{name:"cross",onClick:a[5]||(a[5]=R=>T.value=!1)})]),a[28]||(a[28]=n("div",{class:"popup-content"},[n("div",{class:"agreement-content"},[n("h4",null,"一、活动参与条件"),n("p",null,"1. 参与者须年满18周岁，具有完全民事行为能力；"),n("p",null,"2. 参与者身体健康，无传染性疾病；"),n("p",null,"3. 参与者须如实填写报名信息。"),n("h4",null,"二、活动安全"),n("p",null,"1. 参与者须遵守活动现场的安全规定；"),n("p",null,"2. 如有身体不适，请及时告知工作人员；"),n("p",null,"3. 活动期间请保管好个人物品。"),n("h4",null,"三、免责声明"),n("p",null,"1. 参与者因个人原因造成的损失，主办方不承担责任；"),n("p",null,"2. 不可抗力因素导致活动取消或延期，主办方不承担赔偿责任；"),n("p",null,"3. 活动最终解释权归主办方所有。"),n("h4",null,"四、个人信息保护"),n("p",null,"1. 主办方将严格保护参与者的个人信息；"),n("p",null,"2. 个人信息仅用于活动组织和联系；"),n("p",null,"3. 未经同意，不会向第三方泄露个人信息。")])],-1)),n("div",An,[o(p,{round:"",block:"",type:"primary",onClick:ne},{default:O(()=>a[27]||(a[27]=[J(" 我已阅读并同意 ")])),_:1,__:[27]})])])]),_:1},8,["show"])])}}});export{Dn as default};
