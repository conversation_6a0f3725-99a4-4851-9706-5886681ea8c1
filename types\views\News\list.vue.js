import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import GlobalHeader from '../../components/GlobalHeader.vue';
import GlobalFooter from '../../components/GlobalFooter.vue';
import CommonCardList from '../../components/CommonCardList.vue';
import { getHospitalNewsListlWithRetry } from './api'; // 假设支持分页参数
const router = useRouter();
const loading = ref(false);
const finished = ref(false);
const items = ref([]);
const page = ref(1);
const pageSize = ref(5); // 每次加载10条数据
const loadItems = async () => {
    if (finished.value || loading.value)
        return;
    loading.value = true;
    try {
        const res = await getHospitalNewsListlWithRetry({
            page: page.value,
            page_size: pageSize.value,
        });
        console.log('加载新闻数据:', res);
        console.log('is_last_page:', res.is_last_page);
        items.value.push(...res.results);
        page.value += 1;
        finished.value = res.is_last_page === true;
    }
    catch (error) {
        console.error('加载新闻失败:', error);
    }
    finally {
        loading.value = false;
    }
};
const handleCardClick = (news) => {
    router.push({ name: 'HospitalNewsDetail', params: { id: news.id } });
};
onMounted(() => {
    loadItems();
});
debugger; /* PartiallyEnd: #3632/scriptSetup.vue */
const __VLS_ctx = {};
let __VLS_components;
let __VLS_directives;
// CSS variable injection 
// CSS variable injection end 
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ class: "news-page" },
});
/** @type {[typeof GlobalHeader, ]} */ ;
// @ts-ignore
const __VLS_0 = __VLS_asFunctionalComponent(GlobalHeader, new GlobalHeader({
    title: "医院新闻",
}));
const __VLS_1 = __VLS_0({
    title: "医院新闻",
}, ...__VLS_functionalComponentArgsRest(__VLS_0));
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ class: "section-container" },
});
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ class: "news-grid" },
});
/** @type {[typeof CommonCardList, ]} */ ;
// @ts-ignore
const __VLS_3 = __VLS_asFunctionalComponent(CommonCardList, new CommonCardList({
    ...{ 'onLoadMore': {} },
    ...{ 'onCardClick': {} },
    items: (__VLS_ctx.items),
    loading: (__VLS_ctx.loading),
    finished: (__VLS_ctx.finished),
    useInfiniteScroll: (true),
}));
const __VLS_4 = __VLS_3({
    ...{ 'onLoadMore': {} },
    ...{ 'onCardClick': {} },
    items: (__VLS_ctx.items),
    loading: (__VLS_ctx.loading),
    finished: (__VLS_ctx.finished),
    useInfiniteScroll: (true),
}, ...__VLS_functionalComponentArgsRest(__VLS_3));
let __VLS_6;
let __VLS_7;
let __VLS_8;
const __VLS_9 = {
    onLoadMore: (__VLS_ctx.loadItems)
};
const __VLS_10 = {
    onCardClick: (__VLS_ctx.handleCardClick)
};
var __VLS_5;
/** @type {[typeof GlobalFooter, ]} */ ;
// @ts-ignore
const __VLS_11 = __VLS_asFunctionalComponent(GlobalFooter, new GlobalFooter({}));
const __VLS_12 = __VLS_11({}, ...__VLS_functionalComponentArgsRest(__VLS_11));
/** @type {__VLS_StyleScopedClasses['news-page']} */ ;
/** @type {__VLS_StyleScopedClasses['section-container']} */ ;
/** @type {__VLS_StyleScopedClasses['news-grid']} */ ;
var __VLS_dollars;
const __VLS_self = (await import('vue')).defineComponent({
    setup() {
        return {
            GlobalHeader: GlobalHeader,
            GlobalFooter: GlobalFooter,
            CommonCardList: CommonCardList,
            loading: loading,
            finished: finished,
            items: items,
            loadItems: loadItems,
            handleCardClick: handleCardClick,
        };
    },
});
export default (await import('vue')).defineComponent({
    setup() {
        return {};
    },
});
; /* PartiallyEnd: #4569/main.vue */
