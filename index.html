<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
    <meta name="description" content="中医智慧 - 传承千年中医文化，守护您的健康" />
    <meta name="keywords" content="中医,中药,养生,健康,传统医学,中医文化" />
    <meta name="author" content="中医智慧团队" />
    
    <!-- Canonical URL -->
    <link rel="canonical" href="https://gujiao.sxaliyun.cn/" />
    
    <!-- Open Graph / Facebook / 微信分享 -->
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://gujiao.sxaliyun.cn/" />
    <meta property="og:title" content="中医智慧 - 传承千年中医文化，守护您的健康" />
    <meta property="og:description" content="专业的中医健康平台，提供中医知识、养生指导、专家咨询等服务。传承千年中医文化，守护您的健康。" />
    <meta property="og:image" content="https://gujiao.sxaliyun.cn/images/og-image.png" />
    <meta property="og:image:secure_url" content="https://gujiao.sxaliyun.cn/images/og-image.png" />
    <meta property="og:image:type" content="image/png" />
    <meta property="og:image:width" content="1200" />
    <meta property="og:image:height" content="630" />
    <meta property="og:image:alt" content="中医智慧 - 传承千年中医文化，守护您的健康" />
    <meta property="og:site_name" content="中医智慧" />
    <meta property="og:locale" content="zh_CN" />
    
    <!-- Twitter Card -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="中医智慧 - 传承千年中医文化，守护您的健康" />
    <meta name="twitter:description" content="专业的中医健康平台，提供中医知识、养生指导、专家咨询等服务。传承千年中医文化，守护您的健康。" />
    <meta name="twitter:image" content="https://gujiao.sxaliyun.cn/images/og-image.png" />
    
    <!-- 微信分享优化 -->
    <meta name="wxcard" content="true" />
    <meta name="msapplication-TileColor" content="#4b8bf4" />
    <meta name="theme-color" content="#4b8bf4" />

    <!-- 微信分享专用标签 -->
    <meta itemprop="name" content="中医智慧 - 传承千年中医文化，守护您的健康" />
    <meta itemprop="description" content="专业的中医健康平台，提供中医知识、养生指导、专家咨询等服务。传承千年中医文化，守护您的健康。" />
    <meta itemprop="image" content="https://gujiao.sxaliyun.cn/images/og-image.png" />

    <!-- 微信JS-SDK配置 -->
    <script src="https://res.wx.qq.com/open/js/jweixin-1.6.0.js"></script>
    
    <!-- 移动端优化 -->
    <meta name="format-detection" content="telephone=no" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
    <meta name="apple-mobile-web-app-title" content="中医智慧" />
    
    <!-- 防止页面缩放 -->
    <meta name="msapplication-tap-highlight" content="no" />
    
    <title>中医智慧 - 传承千年中医文化，守护您的健康</title>
    
    <!-- 预加载关键资源 -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    
    <style>
      /* 防止页面闪烁 */
      html, body {
        margin: 0;
        padding: 0;
        height: 100%;
        overflow-x: hidden;
        -webkit-text-size-adjust: 100%;
        -ms-text-size-adjust: 100%;
        text-size-adjust: 100%;
      }
      
      /* 加载动画 */
      #loading {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9999;
        transition: opacity 0.5s ease;
      }
      
      .loading-spinner {
        width: 40px;
        height: 40px;
        border: 3px solid rgba(255, 255, 255, 0.3);
        border-top: 3px solid white;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
      
      .loading-text {
        color: white;
        margin-top: 20px;
        font-size: 16px;
        font-weight: 500;
      }
      
      /* 隐藏加载动画 */
      .loading-hidden {
        opacity: 0;
        pointer-events: none;
      }
    </style>
  </head>
  <body>
    <!-- 加载动画 -->
    <div id="loading">
      <div style="text-align: center;">
        <div class="loading-spinner"></div>
        <div class="loading-text">中医智慧加载中...</div>
      </div>
    </div>
    
    <div id="app"></div>
    
    <script type="module" src="/src/main.ts"></script>
    
    <script>
      // 页面加载完成后隐藏加载动画
      window.addEventListener('load', function() {
        setTimeout(function() {
          const loading = document.getElementById('loading');
          if (loading) {
            loading.classList.add('loading-hidden');
            setTimeout(function() {
              loading.style.display = 'none';
            }, 500);
          }
        }, 800);
      });
      
      // 防止双击缩放
      let lastTouchEnd = 0;
      document.addEventListener('touchend', function (event) {
        const now = (new Date()).getTime();
        if (now - lastTouchEnd <= 300) {
          event.preventDefault();
        }
        lastTouchEnd = now;
      }, false);
      
      // 防止长按选择文本
      document.addEventListener('selectstart', function(e) {
        if (e.target.tagName !== 'INPUT' && e.target.tagName !== 'TEXTAREA') {
          e.preventDefault();
        }
      });
    </script>
  </body>
</html>
