/* 动画效果 */
@keyframes blinkBorder {
  0% {
    box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.8);
  }
  50% {
    box-shadow: 0 0 0 4px rgba(255, 255, 255, 0.4);
  }
  100% {
    box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.8);
  }
}

/* 基础容器样式 */
.knowledge-container {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
  padding-bottom: 80px; /* 为固定的底部导航栏留出空间 */
  position: relative;
  width: 100%;
  max-width: 100vw;
  overflow-x: hidden;
  box-sizing: border-box;
}

/* 顶部导航栏 */
.knowledge-header {
  position: sticky;
  top: 0;
  z-index: 1000; /* 提高z-index，确保页头不被遮盖 */
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  box-shadow: 0 2px 20px rgba(102, 126, 234, 0.3);
  /* 移除 backdrop-filter，消除蒙版效果 */
  width: 100%;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  max-width: 100%;
  min-height: 44px;
  box-sizing: border-box;
  border-radius: 8px;
  margin: 4px;
  animation: blinkBorder 2s infinite ease-in-out;
}

.page-title {
  font-size: 18px;
  font-weight: 600;
  color: #fff;
  margin: 0;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  flex: 1;
  text-align: center;
}

.header-content .van-icon {
  color: #fff;
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 8px;
  border-radius: 50%;
  flex-shrink: 0;
}

.header-content .van-icon:hover {
  background-color: rgba(255, 255, 255, 0.2);
  transform: scale(1.1);
}

/* 分类导航 */
.category-nav {
  margin-top: 0;
  width: 100%;
}

.category-nav .van-tabs {
  background: #fff;
  border-radius: 0 0 16px 16px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.category-nav .van-tabs__nav {
  background: #fff;
  padding: 0 8px;
}

.category-nav .van-tab {
  font-size: 14px;
  font-weight: 500;
  color: #666;
  transition: all 0.3s ease;
  flex: 1;
  min-width: 0;
}

.category-nav .van-tab--active {
  color: #667eea;
  font-weight: 600;
}

.category-nav .van-tabs__line {
  background: linear-gradient(90deg, #667eea, #764ba2);
  height: 3px;
  border-radius: 2px;
}

/* 标签页内容 */
.tab-content {
  /* 移除tab-content的padding，让内部元素控制自己的边距 */
  padding: 0;
  min-height: 60vh;
  box-sizing: border-box;
  width: 100%;
}

/* 精选推荐区域 */
.featured-section {
  margin: 15px 15px 24px 15px; /* 上右下左 */
  padding: 5px;
  width: auto; /* 宽度由margin和padding控制 */
  box-sizing: border-box;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 18px;
  font-weight: 600;
  color: #333;
  padding-left: 4px;
}

.featured-swipe {
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  width: 100%;
}

.featured-item {
  position: relative;
  height: 200px;
  cursor: pointer;
  overflow: hidden;
  width: 100%;
}

.featured-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.featured-item:hover .featured-image {
  transform: scale(1.05);
}

.featured-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  color: #fff;
  padding: 24px 16px 16px;
}

.featured-title {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 8px 0;
  line-height: 1.4;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
}

.featured-desc {
  font-size: 13px;
  opacity: 0.9;
  margin: 0 0 12px 0;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.featured-meta {
  display: flex;
  gap: 16px;
  font-size: 12px;
  opacity: 0.8;
}

/* 功能菜单区域 */
/* .function-menu-section {
  margin-bottom: 24px;
  width: 100%;
} */ /* 此处原先的function-menu-section是空的，且在template中没有使用，可以删除 */

/* 移除重复的.function-grid定义，使用后面统一的定义 */

/* 移除.function-grid .van-grid-item的额外padding，与首页保持一致 */

/* 移除第一处重复的.grid-item-content定义，使用后面统一的定义 */

.grid-item-content:hover {
  transform: translateY(-5px);
}

.icon-wrapper {
  width: 48px;
  height: 48px;
  border-radius: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
  transition: all 0.3s ease;
}

.grid-item-content:active .icon-wrapper {
  transform: scale(0.95);
}

/* 选中状态的图标容器 */
.icon-wrapper.active {
  border-radius: 24px !important; /* 圆形 */
  box-shadow: 0 2px 8px rgba(75, 139, 244, 0.3);
  transform: scale(1.05);
}

/* 选中状态的图标 */
.icon-wrapper.active .van-icon {
  color: #fff !important;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.2));
}

.grid-text {
  font-size: 13px;
  font-weight: 500;
  color: #333;
  text-align: center;
  line-height: 1.2;
  transition: all 0.3s ease;
}

/* 选中状态的文字 */
.grid-text.active {
  color: #4b8bf4 !important;
  font-weight: 600;
}

/* 选中状态的底部指示器 */
.grid-item-content.active::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 50%;
  transform: translateX(-50%);
  width: 4px;
  height: 4px;
  background: #4b8bf4;
  border-radius: 50%;
  animation: dotFadeIn 0.3s ease;
}

@keyframes dotFadeIn {
  0% {
    opacity: 0;
    transform: translateX(-50%) scale(0);
  }
  100% {
    opacity: 1;
    transform: translateX(-50%) scale(1);
  }
}

/* 加载状态样式 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: #666;
}

.loading-container p {
  margin-top: 12px;
  font-size: 14px;
}

/* 知识卡片列表 */
.knowledge-list {
  display: grid;
  gap: 16px;
  margin: 0 15px; /* 左右边距与section-container一致 */
  padding: 5px; /* 内部填充与section-container一致 */
  width: auto; /* 宽度由margin和padding控制 */
  box-sizing: border-box;
  background-color: #fff; /* 添加背景色 */
  border-radius: 8px; /* 添加圆角 */
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05); /* 添加阴影 */
}

.knowledge-card {
  background: #fff;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  cursor: pointer;
  border: 1px solid rgba(102, 126, 234, 0.1);
  width: 100%;
  box-sizing: border-box;
}

.knowledge-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(102, 126, 234, 0.15);
  border-color: rgba(102, 126, 234, 0.2);
}

.card-image-wrapper {
  position: relative;
  height: 160px;
  overflow: hidden;
  width: 100%;
}

.card-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.knowledge-card:hover .card-image {
  transform: scale(1.05);
}

.card-tag {
  position: absolute;
  top: 12px;
  left: 12px;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 11px;
  font-weight: 500;
  color: #fff;
  backdrop-filter: blur(10px);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  white-space: nowrap;
}

.card-tag.health {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.card-tag.medicine {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.card-tag.acupuncture {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.card-tag.diet {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
  color: #333;
  text-shadow: none;
}

.card-tag.exercise {
  background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
  color: #333;
  text-shadow: none;
}

.card-content {
  padding: 16px;
  box-sizing: border-box;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0 0 8px 0;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-break: break-word;
}

.card-summary {
  font-size: 13px;
  color: #666;
  line-height: 1.5;
  margin: 0 0 12px 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-break: break-word;
}

.card-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: #999;
  flex-wrap: wrap;
  gap: 8px;
}

.meta-left {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.meta-right {
  display: flex;
  gap: 12px;
  align-items: center;
  flex-wrap: wrap;
}

.meta-right span {
  display: flex;
  align-items: center;
  gap: 4px;
  white-space: nowrap;
}

.author {
  color: #667eea;
  font-weight: 500;
}

/* 加载更多 */
.load-more {
  text-align: center;
  margin-top: 24px;
  padding: 16px;
  width: 100%;
}

.load-more .van-button {
  min-width: 120px;
  border-radius: 20px;
  font-weight: 500;
}

/* 悬浮操作按钮 */
.floating-actions {
  position: fixed;
  right: 16px;
  bottom: 100px;
  z-index: 99;
}

.floating-btn {
  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
  backdrop-filter: blur(10px);
  border: none;
  font-weight: 500;
}

.chat-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
  }
  50% {
    box-shadow: 0 4px 30px rgba(102, 126, 234, 0.5);
  }
  100% {
    box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
  }
}

/* 底部装饰 */
.decorative-footer {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
  text-align: center;
  padding: 16px 16px 12px; /* 减少垂直padding */
  margin-top: 16px; /* 减少margin-top */
  position: relative;
  overflow: hidden;
  width: 100%;
  box-sizing: border-box;
}

.decorative-footer::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent
  );
}

.footer-content {
  max-width: 300px;
  margin: 0 auto;
}

.footer-logo {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-bottom: 12px;
}

.footer-logo span {
  font-size: 18px;
  font-weight: 600;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.footer-slogan {
  font-size: 14px;
  margin: 0 0 8px 0;
  opacity: 0.9;
  font-weight: 500;
}

.footer-copyright {
  font-size: 12px;
  margin: 0;
  opacity: 0.7;
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate__fadeInUp {
  animation: fadeInUp 0.6s ease-out;
}

.animate__fadeInDown {
  animation: fadeInDown 0.6s ease-out;
}

/* 精品文章专区样式 */
.featured-articles-section {
  margin: 5px 5px 20px 5px;
  padding: 5px;
  width: auto;
  box-sizing: border-box;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.featured-articles-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr)); /* 响应式列 */
  gap: 16px;
  margin-top: 16px;
}

.article-card {
  background: #fff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  cursor: pointer;
  border: 1px solid rgba(255, 151, 106, 0.1);
}

.article-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(255, 151, 106, 0.15);
  border-color: rgba(255, 151, 106, 0.2);
}

.article-image {
  width: 100%;
  height: 120px; /* 固定图片高度 */
  object-fit: cover;
  transition: transform 0.5s ease;
}

.article-card:hover .article-image {
  transform: scale(1.05);
}

.article-content {
  padding: 12px;
}

.article-title {
  font-size: 15px;
  font-weight: 600;
  color: #333;
  margin: 0 0 6px 0;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-break: break-word;
}

.article-summary {
  font-size: 12px;
  color: #666;
  line-height: 1.5;
  margin: 0 0 10px 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-break: break-word;
}

.article-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 11px;
  color: #999;
}

.article-views,
.article-likes {
  display: flex;
  align-items: center;
  gap: 4px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .featured-articles-grid {
    grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
  }
}

@media (max-width: 480px) {
  .featured-articles-grid {
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  }
  .article-image {
    height: 100px;
  }
  .article-title {
    font-size: 14px;
  }
  .article-summary {
    font-size: 11px;
  }
}

/* 响应式设计 - 移动端优先 */

/* 超小屏幕手机 (320px - 374px) */
@media (max-width: 374px) {
  .knowledge-container {
    padding: 0 8px;
  }

  .header-content {
    padding: 8px 10px;
    min-height: 40px;
  }

  .page-title {
    font-size: 15px;
  }

  .header-content .van-icon {
    padding: 6px;
    font-size: 18px;
  }

  .category-nav .van-tab {
    font-size: 13px;
    padding: 8px 4px;
  }

  .tab-content {
    padding: 8px;
  }

  .section-title {
    font-size: 16px;
    margin-bottom: 12px;
  }

  .featured-item {
    height: 150px;
  }

  .featured-overlay {
    padding: 12px 10px 10px;
  }

  .featured-title {
    font-size: 14px;
    margin-bottom: 6px;
  }

  .featured-desc {
    font-size: 11px;
    margin-bottom: 8px;
  }

  .featured-meta {
    font-size: 10px;
  }

  /* 响应式样式 - 小屏幕 (小于 375px) */
  @media (max-width: 374px) {
    /* 功能菜单响应式 - 超小屏幕 */
    :deep(.first-row .van-grid-item) {
      flex: 0 0 50%;
      max-width: 50%;
    }

    :deep(.second-row .van-grid-item) {
      flex: 0 0 33.33%;
      max-width: 33.33%;
    }

    .grid-item-content {
      padding: 12px 6px;
    }

    .icon-wrapper {
      width: 40px;
      height: 40px;
      margin-bottom: 6px;
    }

    .grid-text {
      font-size: 12px;
    }
  }

  /* 响应式样式 - 中小屏幕 (375px - 413px) */
  @media (min-width: 375px) and (max-width: 413px) {
    /* 功能菜单响应式 - 小屏幕 */
    :deep(.first-row .van-grid-item) {
      flex: 0 0 50%;
      max-width: 50%;
    }

    :deep(.second-row .van-grid-item) {
      flex: 0 0 33.33%;
      max-width: 33.33%;
    }

    .grid-item-content {
      padding: 14px 8px;
    }

    .icon-wrapper {
      width: 44px;
      height: 44px;
      margin-bottom: 8px;
      border-radius: 12px;
    }

    .grid-text {
      font-size: 12px;
    }
  }

  /* 响应式样式 - 中屏幕 (414px - 767px) */
  @media (min-width: 414px) and (max-width: 767px) {
    /* 功能菜单响应式 - 中屏幕 */
    :deep(.first-row .van-grid-item) {
      flex: 0 0 50%;
      max-width: 50%;
    }

    :deep(.second-row .van-grid-item) {
      flex: 0 0 33.33%;
      max-width: 33.33%;
    }

    .grid-item-content {
      padding: 16px 10px;
    }

    .icon-wrapper {
      width: 48px;
      height: 48px;
      margin-bottom: 8px;
      border-radius: 14px;
    }

    .grid-text {
      font-size: 13px;
    }
  }
  
  .card-image-wrapper {
    height: 120px;
  }
  
  .card-content {
    padding: 10px;
  }
  
  .card-title {
    font-size: 14px;
    margin-bottom: 6px;
  }
  
  .card-summary {
    font-size: 12px;
    margin-bottom: 8px;
  }
  
  .card-meta {
    font-size: 11px;
  }
  
  .card-tag {
    font-size: 10px;
    padding: 3px 8px;
  }

  .floating-actions {
    right: 8px;
    bottom: 70px;
  }

  .floating-btn {
    font-size: 12px;
    padding: 6px 10px;
    min-width: 60px;
  }

  .decorative-footer {
    padding: 16px 10px 12px;
  }

  .footer-logo span {
    font-size: 16px;
  }

  .footer-slogan {
    font-size: 13px;
  }

  .footer-copyright {
    font-size: 11px;
  }
}

/* 小屏幕手机 (< 375px) */
@media (max-width: 374px) {
  .header-content {
    padding: 8px 10px;
  }
  
  .page-title {
    font-size: 15px;
  }
  
  .tab-content {
    padding: 10px;
  }
  
  .featured-item {
    height: 150px;
  }
  
  /* 功能菜单响应式 - 超小屏幕 */
  .function-grid {
    /* 移除 grid-template-columns 和 gap，由 van-grid-item 的 flex 属性控制 */
  }
  
  .grid-item-content {
    padding: 12px 6px;
  }
  
  .icon-wrapper {
    width: 40px;
    height: 40px;
    margin-bottom: 6px;
  }
  
  .grid-text {
    font-size: 12px;
  }
  
  .card-image-wrapper {
    height: 120px;
  }
  
  .card-content {
    padding: 10px;
  }
  
  .card-title {
    font-size: 14px;
    margin-bottom: 6px;
  }
  
  .card-summary {
    font-size: 12px;
    margin-bottom: 8px;
  }
  
  .card-meta {
    font-size: 11px;
  }
  
  .card-tag {
    font-size: 10px;
    padding: 3px 8px;
  }
  
  .floating-actions {
    right: 8px;
    bottom: 70px;
  }
  
  .floating-btn {
    font-size: 12px;
    padding: 6px 10px;
    min-width: 60px;
  }
  
  .decorative-footer {
    padding: 16px 10px 12px;
  }
  
  .footer-logo span {
    font-size: 16px;
  }
  
  .footer-slogan {
    font-size: 13px;
  }
  
  .footer-copyright {
    font-size: 11px;
  }
}

/* 小屏幕手机 (375px - 413px) */
@media (min-width: 375px) and (max-width: 413px) {
  .header-content {
    padding: 10px 12px;
  }
  
  .page-title {
    font-size: 16px;
  }
  
  .tab-content {
    padding: 12px;
  }
  
  .featured-item {
    height: 170px;
  }
  
  /* 功能菜单响应式 - 小屏幕 */
  .function-grid {
    /* 移除 grid-template-columns 和 gap，由 van-grid-item 的 flex 属性控制 */
  }
  
  .grid-item-content {
    padding: 14px 8px;
  }
  
  .icon-wrapper {
    width: 44px;
    height: 44px;
    margin-bottom: 8px;
  }
  
  .grid-text {
    font-size: 12px;
  }
  
  .card-image-wrapper {
    height: 140px;
  }
  
  .card-content {
    padding: 12px;
  }
  
  .floating-actions {
    right: 12px;
    bottom: 85px;
  }
}

/* 中等屏幕手机 (414px - 767px) */
@media (min-width: 414px) and (max-width: 767px) {
  .header-content {
    padding: 12px 16px;
  }
  
  .tab-content {
    padding: 16px;
  }
  
  .featured-item {
    height: 200px;
  }
  
  /* 功能菜单响应式 - 中等屏幕 */
  .function-grid {
    /* 移除 grid-template-columns 和 gap，由 van-grid-item 的 flex 属性控制 */
  }
  
  .grid-item-content {
    padding: 16px 10px;
  }
  
  .icon-wrapper {
    width: 48px;
    height: 48px;
    margin-bottom: 8px;
  }
  
  .grid-text {
    font-size: 13px;
  }
  
  .card-image-wrapper {
    height: 160px;
  }
  
  .knowledge-list {
    gap: 16px;
  }
  
  .floating-actions {
    right: 16px;
    bottom: 100px;
  }
}

/* 平板和大屏幕 (768px+) */
@media (min-width: 768px) {
  .knowledge-container {
    max-width: 768px;
    margin: 0 auto;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
  }
  
  .knowledge-list {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }
  
  .tab-content {
    padding: 20px;
  }
  
  .featured-item {
    height: 220px;
  }
  
  /* 功能菜单响应式 - 平板和大屏幕 */
  .function-grid {
    /* 移除 grid-template-columns 和 gap，由 van-grid-item 的 flex 属性控制 */
  }
  
  .grid-item-content {
    padding: 16px 12px;
  }
  
  .icon-wrapper {
    width: 56px;
    height: 56px;
    margin-bottom: 10px;
    border-radius: 16px;
  }
  
  .grid-text {
    font-size: 15px;
  }
}

/* 横屏模式 */
@media (orientation: landscape) and (max-height: 500px) {
  /* 功能菜单响应式 - 横屏模式 */
  .function-grid {
    /* 移除 grid-template-columns 和 gap，由 van-grid-item 的 flex 属性控制 */
  }
  
  .grid-item-content {
    padding: 10px 8px;
  }
  
  .icon-wrapper {
    width: 40px;
    height: 40px;
    margin-bottom: 6px;
  }
  
  .grid-text {
    font-size: 12px;
  }
}

/* iPhone X/11/12/13/14/15 系列适配 */
@media (min-width: 375px) and (max-width: 428px) and (-webkit-device-pixel-ratio: 3) {
  .knowledge-container {
    padding-bottom: env(safe-area-inset-bottom, 0px); /* 保留安全区域适配，但默认为0 */
  }

  .floating-actions {
    bottom: calc(env(safe-area-inset-bottom, 20px) + 80px);
  }
}

/* 高分辨率屏幕适配 */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .knowledge-container {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  .featured-image,
  .card-image {
    image-rendering: -webkit-optimize-contrast;
  }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
  .knowledge-card:hover {
    transform: none;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  }

  .featured-item:hover .featured-image {
    transform: none;
  }

  .knowledge-card:hover .card-image {
    transform: none;
  }

  .header-content .van-icon:hover {
    background-color: transparent;
    transform: none;
  }

  /* 移除功能项的hover效果，因为触摸设备不需要 */
  /* .function-item:hover {
    transform: none;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
  }
  
  .function-item:hover::before {
    transform: scaleX(0);
  }
  
  .function-item:hover .function-icon-wrapper {
    transform: none;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  }
  
  .function-item:hover .function-icon-wrapper .van-icon {
    color: inherit !important;
  } */
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 4px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 2px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #5a6fd8, #6a4190);
}

/* 触摸反馈 */
.knowledge-card:active {
  transform: translateY(-2px) scale(0.98);
}

.floating-btn:active {
  transform: scale(0.95);
}

.header-content .van-icon:active {
  transform: scale(0.9);
}

/* 加载状态 */
.van-loading {
  color: #667eea;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: #999;
}

.empty-state .van-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-state p {
  font-size: 14px;
  margin: 0;
}

/* 减少动画的用户偏好 */
@media (prefers-reduced-motion: reduce) {
  .knowledge-card,
  .featured-image,
  .card-image,
  .floating-btn,
  .function-item,
  .function-icon-wrapper {
    transition: none;
  }

  .animate__fadeInUp,
  .animate__fadeInDown {
    animation: none;
  }

  .chat-btn {
    animation: none;
  }
}

/* 确保所有元素不会超出容器 */
* {
  box-sizing: border-box;
}

img {
  max-width: 100%;
  height: auto;
}

/* 动画延迟类 */
.delay-100 {
  animation-delay: 0.1s;
}

.delay-200 {
  animation-delay: 0.2s;
}

.delay-300 {
  animation-delay: 0.3s;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.section-title {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: bold;
  color: #323233;
}

.section-title span {
  margin-left: 8px;
}

.more-btn {
  font-size: 12px;
}

/* 功能按钮区样式 */
.function-grid {
  --van-grid-item-content-padding: 8px;
  display: flex;
  flex-wrap: nowrap;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
  padding-bottom: 10px;
}

/* 第二排功能区样式 */
.second-row {
  margin-top: 10px;
}

:deep(.first-row .van-grid-item) {
  flex: 0 0 50%;
  max-width: 50%;
  box-sizing: border-box;
}

:deep(.second-row .van-grid-item) {
  flex: 0 0 33.33%;
  max-width: 33.33%;
  box-sizing: border-box;
}

:deep(.van-grid-item) {
  /* 保持van-grid的默认flex布局 */
  box-sizing: border-box;
  background: transparent !important; /* 确保没有白色背景 */
  /* 移除动画相关样式，因为动画在template中直接控制 */
  /* transition: all 0.3s ease; */
}

.grid-item-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  width: 100%;
  padding: 0 5px;
  box-sizing: border-box;
  position: relative; /* 为底部指示器提供定位基准 */
}

.grid-item-content:hover {
  transform: translateY(-5px);
}

.icon-wrapper {
  width: 48px;
  height: 48px;
  border-radius: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
  transition: all 0.3s ease;
}

.grid-item-content:active .icon-wrapper {
  transform: scale(0.9);
  /* 移除浮动效果 box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1); */
}

.grid-text {
  font-size: 13px;
  color: #323233;
  font-weight: 500;
  text-align: center;
}

/* 移除重复的.function-item样式 */
/* 移除未使用的.function-item样式，这些样式可能造成白色浮动图层 */

/* 知识卡片列表 */
.knowledge-list {
  display: grid;
  gap: 16px;
  width: 100%;
}

.knowledge-card {
  background: #fff;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  cursor: pointer;
  border: 1px solid rgba(102, 126, 234, 0.1);
  width: 100%;
  box-sizing: border-box;
}

.knowledge-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(102, 126, 234, 0.15);
  border-color: rgba(102, 126, 234, 0.2);
}

.card-image-wrapper {
  position: relative;
  height: 160px;
  overflow: hidden;
  width: 100%;
}

.card-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.knowledge-card:hover .card-image {
  transform: scale(1.05);
}

.card-tag {
  position: absolute;
  top: 12px;
  left: 12px;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 11px;
  font-weight: 500;
  color: #fff;
  backdrop-filter: blur(10px);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  white-space: nowrap;
}

.card-tag.health {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.card-tag.medicine {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.card-tag.acupuncture {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.card-tag.diet {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
  color: #333;
  text-shadow: none;
}

.card-tag.exercise {
  background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
  color: #333;
  text-shadow: none;
}

.card-content {
  padding: 16px;
  box-sizing: border-box;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0 0 8px 0;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-break: break-word;
}

.card-summary {
  font-size: 13px;
  color: #666;
  line-height: 1.5;
  margin: 0 0 12px 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-break: break-word;
}

.card-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: #999;
  flex-wrap: wrap;
  gap: 8px;
}

.meta-left {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.meta-right {
  display: flex;
  gap: 12px;
  align-items: center;
  flex-wrap: wrap;
}

.meta-right span {
  display: flex;
  align-items: center;
  gap: 4px;
  white-space: nowrap;
}

.author {
  color: #667eea;
  font-weight: 500;
}

/* 加载更多 */
.load-more {
  text-align: center;
  margin-top: 24px;
  padding: 16px;
  width: 100%;
}

.load-more .van-button {
  min-width: 120px;
  border-radius: 20px;
  font-weight: 500;
}

/* 悬浮操作按钮 */
.floating-actions {
  position: fixed;
  right: 16px;
  bottom: 100px;
  z-index: 99;
}

.floating-btn {
  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
  backdrop-filter: blur(10px);
  border: none;
  font-weight: 500;
}

.chat-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
  }
  50% {
    box-shadow: 0 4px 30px rgba(102, 126, 234, 0.5);
  }
  100% {
    box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
  }
}

/* 底部装饰 */
.decorative-footer {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
  text-align: center;
  padding: 16px 16px 12px; /* 减少垂直padding */
  margin-top: 16px; /* 减少margin-top */
  position: relative;
  overflow: hidden;
  width: 100%;
  box-sizing: border-box;
}

.decorative-footer::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent
  );
}

.footer-content {
  max-width: 300px;
  margin: 0 auto;
}

.footer-logo {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-bottom: 12px;
}

.footer-logo span {
  font-size: 18px;
  font-weight: 600;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.footer-slogan {
  font-size: 14px;
  margin: 0 0 8px 0;
  opacity: 0.9;
  font-weight: 500;
}

.footer-copyright {
  font-size: 12px;
  margin: 0;
  opacity: 0.7;
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate__fadeInUp {
  animation: fadeInUp 0.6s ease-out;
}

.animate__fadeInDown {
  animation: fadeInDown 0.6s ease-out;
}

/* 精品文章专区样式 */
.featured-articles-section {
  margin: 5px 5px 24px 5px;
  padding: 5px;
  width: auto;
  box-sizing: border-box;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.featured-articles-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr)); /* 响应式列 */
  gap: 16px;
  margin-top: 16px;
}

.article-card {
  background: #fff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  cursor: pointer;
  border: 1px solid rgba(255, 151, 106, 0.1);
}

.article-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(255, 151, 106, 0.15);
  border-color: rgba(255, 151, 106, 0.2);
}

.article-image {
  width: 100%;
  height: 120px; /* 固定图片高度 */
  object-fit: cover;
  transition: transform 0.5s ease;
}

.article-card:hover .article-image {
  transform: scale(1.05);
}

.article-content {
  padding: 12px;
}

.article-title {
  font-size: 15px;
  font-weight: 600;
  color: #333;
  margin: 0 0 6px 0;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-break: break-word;
}

.article-summary {
  font-size: 12px;
  color: #666;
  line-height: 1.5;
  margin: 0 0 10px 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-break: break-word;
}

.article-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 11px;
  color: #999;
}

.article-views,
.article-likes {
  display: flex;
  align-items: center;
  gap: 4px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .featured-articles-grid {
    grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
  }
}

@media (max-width: 480px) {
  .featured-articles-grid {
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  }
  .article-image {
    height: 100px;
  }
  .article-title {
    font-size: 14px;
  }
  .article-summary {
    font-size: 11px;
  }
}
