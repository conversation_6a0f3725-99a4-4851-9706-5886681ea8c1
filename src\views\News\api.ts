import { get, post, requestWithRetry } from '../../api/request';
import { HOSPITAL_NEWS_URLS, buildUrl } from '../../api/urls';


export function getHospitalNewsList(params?: any) {
  return get<HospitalNewsListResponse>(HOSPITAL_NEWS_URLS.NEWS, params);
}

export function getHospitalNewsListlWithRetry(params?: any) {
  return requestWithRetry<HospitalNewsListResponse>(() => getHospitalNewsList(params));
}



// 活动数据类型定义
export interface HospitalNewsItem {
  id: number;
  name: string;
  desc: string;
  thumbnail: string;
  viewtimes: string;
  create_time: string;
}
export interface HospitalNewsListResponse {
  results: HospitalNewsItem[];
  page: number;
  page_size: number;
  count: number;
  is_last_page: boolean;
  next: string | null;
  previous: string | null;
}