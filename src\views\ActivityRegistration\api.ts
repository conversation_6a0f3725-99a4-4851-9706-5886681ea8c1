import { get, post, requestWithRetry } from "../../api/request";
import { ACTIVITY_URLS, buildUrl } from "../../api/urls";

export function postActivityRegistration(params: ActivityRegistrationRequest) {
  return post<ActivityRegistrationResponse>(ACTIVITY_URLS.REGIST, params);
}

export function postSMSCodeSend(params?: { phone: string }) {
  return post<{ message: string } | { error: string }>(ACTIVITY_URLS.SENDSMS, params);
}

// 活动报名请求参数类型
export interface ActivityRegistrationRequest {
  activity: number;      // 活动ID
  name: string;         // 姓名
  phone: string;        // 手机号（后端要求用phone字段）
  code: string;         // 验证码（后端要求用code字段）
  age: string;          // 年龄
}

// 活动报名返回数据类型
export interface ActivityRegItem {
  id: number;
  regno: string;
  name: string;
  phone?: string;       // 后端返回phone字段
  age: string;
  create_time: string;
  qrcode: string;
}

// 成功响应类型
export interface ActivityRegistrationSuccessResponse {
  results: ActivityRegItem;
}

// 失败响应类型
export interface ActivityRegistrationErrorResponse {
  success: false;
  message: string;
  errors?: Record<string, string[]>;
}

// 统一响应类型
export type ActivityRegistrationResponse = 
  | ActivityRegistrationSuccessResponse 
  | ActivityRegistrationErrorResponse;
