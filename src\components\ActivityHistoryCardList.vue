<template>
  <div class="activity-history-card-list" ref="scrollContainerRef">
    <!-- 首次加载中 -->
    <div v-if="loading && !items.length" class="loading-container">
      <van-loading size="24px">加载中...</van-loading>
    </div>

    <!-- 空状态 -->
    <van-empty v-else-if="!items.length" :description="emptyText" />

    <!-- 列表 -->
    <van-list v-else-if="useInfiniteScroll" v-model:loading="internalLoading" :finished="finished" finished-text="没有更多了"
      @load="onLoadMore" :immediate-check="false" :scroll-container="getScrollContainer">
      <div v-for="(item, index) in items" :key="item.id" class="history-card animate__animated animate__fadeInUp"
        @click="$emit('card-click', item)">
        
        <!-- 序号和时间水平布局 -->
        <div class="activity-header">
          <div class="activity-number">
            <span class="number-circle">{{ index + 1 }}</span>
          </div>
          <div class="activity-time">
            <div class="time-label">时间：</div>
            <div class="time-content">{{ formatActivityTime(item) }}</div>
          </div>
        </div>

        <!-- 活动内容区域 -->
        <div class="activity-content">
          <!-- 活动图片 -->
          <div class="activity-image-container">
            <img :src="item.thumbnail || item.image" :alt="item.name || item.title" class="activity-image" />
            <!-- 活动状态标签 -->
            <van-tag v-if="getActivityStatus(item)" :type="getStatusTagType(item)" size="medium" class="status-tag" round>
              {{ getActivityStatus(item) }}
            </van-tag>
          </div>

          <!-- 活动文本内容 -->
          <div class="activity-text">
            <h3 class="activity-title">{{ item.name || item.title }}</h3>
            <p class="activity-description">{{ item.desc || item.description || item.summary }}</p>
            
            <!-- 活动元信息 -->
            <div class="activity-meta" v-if="hasMetaInfo(item)">
              <div class="meta-item" v-if="item.location || item.address">
                <van-icon name="location-o" />
                <span>{{ item.location || item.address }}</span>
              </div>
              <div class="meta-item" v-if="item.participants || item.current_participants">
                <van-icon name="friends-o" />
                <span>{{ item.participants || item.current_participants }}人参与</span>
              </div>
              <div class="meta-item" v-if="item.cat_display || item.category">
                <van-icon name="label-o" />
                <span>{{ item.cat_display || item.category }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </van-list>

    <!-- 非无限滚动模式 -->
    <div v-else>
      <div v-for="(item, index) in items" :key="item.id" class="history-card animate__animated animate__fadeInUp"
        @click="$emit('card-click', item)">
        
        <!-- 序号和时间水平布局 -->
        <div class="activity-header">
          <div class="activity-number">
            <span class="number-circle">{{ index + 1 }}</span>
          </div>
          <div class="activity-time">
            <div class="time-label">时间：</div>
            <div class="time-content">{{ formatActivityTime(item) }}</div>
          </div>
        </div>

        <!-- 活动内容区域 -->
        <div class="activity-content">
          <!-- 活动图片 -->
          <div class="activity-image-container">
            <img :src="item.thumbnail || item.image" :alt="item.name || item.title" class="activity-image" />
            <!-- 活动状态标签 -->
            <van-tag v-if="getActivityStatus(item)" :type="getStatusTagType(item)" size="medium" class="status-tag" round>
              {{ getActivityStatus(item) }}
            </van-tag>
          </div>

          <!-- 活动文本内容 -->
          <div class="activity-text">
            <h3 class="activity-title">{{ item.name || item.title }}</h3>
            <p class="activity-description">{{ item.desc || item.description || item.summary }}</p>
            
            <!-- 活动元信息 -->
            <div class="activity-meta" v-if="hasMetaInfo(item)">
              <div class="meta-item" v-if="item.location || item.address">
                <van-icon name="location-o" />
                <span>{{ item.location || item.address }}</span>
              </div>
              <div class="meta-item" v-if="item.participants || item.current_participants">
                <van-icon name="friends-o" />
                <span>{{ item.participants || item.current_participants }}人参与</span>
              </div>
              <div class="meta-item" v-if="item.cat_display || item.category">
                <van-icon name="label-o" />
                <span>{{ item.cat_display || item.category }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, nextTick } from 'vue';
import { formatDateTime, formatActivityTimeRange } from '../utils/dateTime';

const scrollContainerRef = ref<HTMLElement | null>(null);
const getScrollContainer = () => scrollContainerRef.value;

interface Props {
  items: any[];
  loading: boolean;
  finished?: boolean;
  emptyText?: string;
  useInfiniteScroll?: boolean;
  activityType?: 'current' | 'past' | 'upcoming' | string; // 新增活动类型prop
}

onMounted(() => {
  nextTick(() => {
    console.log('ActivityHistoryCardList 滚动容器:', scrollContainerRef.value);
  });
});

const props = withDefaults(defineProps<Props>(), {
  emptyText: '暂无活动历史',
  useInfiniteScroll: true,
  finished: false,
  activityType: 'past', // 默认为历史活动
});

const emit = defineEmits(['load-more', 'card-click']);

const internalLoading = ref(false);

watch(
  () => props.loading,
  (val) => {
    internalLoading.value = val;
  },
  { immediate: true }
);

const onLoadMore = () => {
  console.log('ActivityHistoryCardList 触发加载更多');
  emit('load-more');
};

// 格式化活动时间
const formatActivityTime = (item: any) => {
  // 优先显示开始时间，使用格式化函数
  if (item.startdate) {
    // 如果有结束时间，显示时间范围
    if (item.enddate) {
      return formatActivityTimeRange(item.startdate, item.enddate);
    }
    return formatDateTime(item.startdate);
  } else if (item.start_time) {
    if (item.end_time) {
      return formatActivityTimeRange(item.start_time, item.end_time);
    }
    return formatDateTime(item.start_time);
  } else if (item.activity_date) {
    return formatDateTime(item.activity_date);
  } else if (item.create_time) {
    return formatDateTime(item.create_time);
  }
  return '时间待定';
};

// 获取活动状态
const getActivityStatus = (item: any) => {
  const now = new Date();
  
  // 如果有明确的状态，优先使用
  if (item.status) {
    return item.status;
  }
  
  // 根据活动类型显示对应的标签
  switch (props.activityType) {
    case 'upcoming':
      return '活动预告';
    case 'current':
      return '本月活动';
    case 'past':
      return '历史活动';
    default:
      // 对于其他情况，根据时间判断活动状态
      if (item.startdate && item.enddate) {
        const startDate = new Date(item.startdate);
        const endDate = new Date(item.enddate);
        
        if (now < startDate) {
          return '未开始';
        } else if (now > endDate) {
          return '已结束';
        } else {
          return '进行中';
        }
      }
      return '已结束';
  }
};

// 获取状态标签类型
const getStatusTagType = (item: any) => {
  const status = getActivityStatus(item);
  
  switch (status) {
    case '进行中':
      return 'success';
    case '未开始':
      return 'primary';
    case '已结束':
      return 'default';
    case '已取消':
      return 'danger';
    case '活动预告':
      return 'primary'; // 蓝色标签，表示即将到来
    case '本月活动':
      return 'success'; // 绿色标签，表示当前活跃
    case '历史活动':
      return 'default'; // 灰色标签，表示已过去
    default:
      return 'default';
  }
};

// 检查是否有元信息
const hasMetaInfo = (item: any) => {
  return !!(item.location || item.address || item.participants || item.current_participants || item.cat_display || item.category);
};
</script>

<style scoped>
.activity-history-card-list {
  flex: 1;
  overflow-y: auto;
  padding: 8px 16px;
}

.history-card {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  margin-bottom: 16px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid rgba(16, 172, 132, 0.08);
  position: relative;
}

.history-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(16, 172, 132, 0.12);
  border-color: rgba(16, 172, 132, 0.2);
}

.activity-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.activity-number {
  flex-shrink: 0;
  margin-right: 20px; /* 与时间间隔20像素 */
}

.number-circle {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  background: linear-gradient(135deg, #10ac84, #00d2ff);
  color: #fff;
  border-radius: 50%;
  font-size: 12px;
  font-weight: 600;
  box-shadow: 0 2px 6px rgba(16, 172, 132, 0.3);
}

.activity-time {
  display: flex;
  align-items: center;
  flex: 1;
  padding: 8px 12px;
  background: rgba(16, 172, 132, 0.05);
  border-radius: 8px;
  border-left: 3px solid #10ac84;
}

.time-label {
  font-size: 13px;
  color: #666;
  font-weight: 500;
  margin-right: 8px;
  flex-shrink: 0;
}

.time-content {
  font-size: 14px;
  color: #333;
  font-weight: 600;
  flex: 1;
}

.activity-content {
  display: flex;
  gap: 12px;
  align-items: flex-start;
}

.activity-image-container {
  position: relative;
  flex-shrink: 0;
  width: 100px;
  height: 100px;
  border-radius: 8px;
  overflow: hidden;
}

.activity-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.history-card:hover .activity-image {
  transform: scale(1.05);
}

.status-tag {
  position: absolute;
  bottom: 4px;
  right: 4px;
  font-size: 10px !important;
  font-weight: 600 !important;
}

.activity-text {
  flex: 1;
  min-width: 0;
}

.activity-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-break: break-word;
}

.activity-description {
  font-size: 13px;
  color: #666;
  line-height: 1.5;
  margin-bottom: 12px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-break: break-word;
}

.activity-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #888;
  background: rgba(0, 0, 0, 0.04);
  padding: 4px 8px;
  border-radius: 12px;
}

.meta-item .van-icon {
  font-size: 12px;
  color: #10ac84;
}

.loading-container {
  padding: 40px 0;
  text-align: center;
  color: #666;
}

/* 响应式调整 */
@media (max-width: 480px) {
  .activity-history-card-list {
    padding: 8px 12px;
  }

  .history-card {
    padding: 12px;
  }

  .activity-content {
    gap: 8px;
  }

  .activity-image-container {
    width: 80px;
    height: 80px;
  }

  .activity-title {
    font-size: 15px;
  }

  .activity-description {
    font-size: 12px;
  }

  .time-content {
    font-size: 13px;
  }

  .meta-item {
    font-size: 11px;
    padding: 3px 6px;
  }

  .number-circle {
    width: 20px;
    height: 20px;
    font-size: 11px;
  }
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate__fadeInUp {
  animation: fadeInUp 0.5s ease-out;
}

/* 减少动画的用户偏好 */
@media (prefers-reduced-motion: reduce) {
  .history-card,
  .activity-image {
    transition: none;
  }

  .animate__fadeInUp {
    animation: none;
  }
}
</style> 