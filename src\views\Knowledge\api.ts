import { get, post, requestWithRetry } from '../../api/request';
import { KNOWLEDGE_URLS, buildUrl } from '../../api/urls';

/**
 * 获取知识文章列表
 * @param params 查询参数
 * @returns 知识文章列表数据
 */
export function getHomeDataList(params?: any) {
  return get<KnowledgeListResponse>(KNOWLEDGE_URLS.HOME_CONTENT, params);
}
export function getKnowledgeDetail(id: string | number) {
  return get<KnowledgeDetailResponse>(buildUrl(KNOWLEDGE_URLS.KNOWLWDGE, id));
}

// 知识文章数据类型定义
export interface ArticleItem {
  id: number;
  name: string;
  creater: string;
  create_time: string;
  thumbnail: string;
  desc: string;
}

export interface KnowledgeDetailResponse {
  knowledge: {
    id: number;
  name: string;
  creater: string;
  create_time: string;
  thumbnail: string;
  desc: string;
  content: string; // HTML格式的详细内容
  }
}

export interface KnowledgeListResponse{
  items: {
    culture_best_data: ArticleItem[];
    knowledge_best_data: ArticleItem[];
    case_best_data: ArticleItem[];
    culture_healthy_data: ArticleItem[];
    knowledge_healthy_data: ArticleItem[];
    case_healthy_data: ArticleItem[];
    culture_latest_data: ArticleItem[];
    knowledge_latest_data: ArticleItem[];
    case_latest_data: ArticleItem[];
  }
}