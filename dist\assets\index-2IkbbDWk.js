import{d as x,u as C,r as m,y as R,c as n,o as c,i as w,b as s,g as e,L as E,w as y,H as N,j as A,t as _,I as T,p as H,_ as D,a as V,k as F,F as G}from"./index-BE8XLQ59.js";import{G as S,a as $}from"./GlobalHeader-lL88u8sR.js";import{E as M}from"./index-B27ddgxZ.js";import{f as j,i as q}from"./dateTime-CGItJ1-U.js";import{s as z}from"./function-call-BUl5915X.js";const U={class:"activity-detail-card"},J={key:0,class:"loading-container"},K={key:1,class:"error-container"},O={key:2,class:"content"},P={class:"activity-hero"},Q=["src","alt"],W={class:"activity-theme-section"},X={class:"activity-title"},Y={class:"activity-subtitle"},Z={class:"activity-info-container"},tt={class:"info-item"},et={class:"info-icon time-icon"},ot={class:"info-content"},st={class:"info-value"},it={class:"info-item"},at={class:"info-icon location-icon"},nt={class:"info-content"},ct={class:"info-value"},rt={class:"content-sections"},lt={key:0,class:"content-section"},dt={class:"section-header"},vt={class:"section-content"},ut=["innerHTML"],_t={key:1,class:"register-section"},mt=x({__name:"ActivityDetailCard",props:{activityId:{type:[String,Number],required:!0},showActions:{type:Boolean,default:!0}},emits:["loaded","error","register"],setup(f,{expose:p,emit:u}){const r=f,l=u,h=C(),d=m(!1),i=m(""),a=m(!1),o=m({id:0,name:"",cat:"",cat_name:"",desc:"",thumbnail:"",isfree:0,startdate:"",enddate:"",views:0,location:"",content:""}),g=async()=>{if(!r.activityId){i.value="活动ID无效",l("error","活动ID无效");return}try{d.value=!0,i.value="";const t=(await q(r.activityId.toString())).activity;o.value={id:t.id,name:t.name,cat:t.cat,cat_name:t.cat_name,desc:t.desc,startdate:t.startdate,enddate:t.enddate,location:t.location,thumbnail:t.thumbnail,views:t.views,isfree:t.isfree,content:t.content||""},l("loaded",o.value)}catch(v){console.error("获取活动详情失败:",v),i.value="获取活动详情失败，请稍后再试",l("error",i.value)}finally{d.value=!1}},L=async()=>{const t=`/activity-registration/${o.value.id||r.activityId}`;try{await h.push(t),l("register",o.value)}catch(b){console.error("跳转失败:",b),window.location.hash=`#${t}`}};return R(()=>r.activityId,v=>{v&&g()},{immediate:!0}),p({refresh:()=>{g()},activity:o}),(v,t)=>{const b=E,I=N,B=M,k=T;return c(),n("div",U,[d.value?(c(),n("div",J,[s(b,{type:"spinner",color:"#4b8bf4",size:"32px"}),t[0]||(t[0]=e("p",null,"加载中...",-1))])):i.value?(c(),n("div",K,[s(B,{description:"加载失败",image:"error"},{description:y(()=>[e("p",null,_(i.value),1)]),default:y(()=>[s(I,{round:"",type:"primary",onClick:g},{default:y(()=>t[1]||(t[1]=[A("重试")])),_:1,__:[1]})]),_:1})])):(c(),n("div",O,[e("div",P,[e("img",{src:o.value.thumbnail,alt:o.value.name,class:"hero-image"},null,8,Q)]),e("div",W,[e("h1",X,_(o.value.name),1),e("div",Y,_(o.value.desc),1)]),e("div",Z,[e("div",tt,[e("div",et,[s(k,{name:"clock-o"})]),e("div",ot,[t[2]||(t[2]=e("div",{class:"info-label"},"活动时间",-1)),e("div",st,_(H(j)(o.value.startdate,o.value.enddate)),1)])]),e("div",it,[e("div",at,[s(k,{name:"location-o"})]),e("div",nt,[t[3]||(t[3]=e("div",{class:"info-label"},"活动地点",-1)),e("div",ct,_(o.value.location||"待定"),1)])])]),e("div",rt,[o.value.content?(c(),n("div",lt,[e("div",dt,[s(k,{name:"notes-o",color:"#4b8bf4"}),t[4]||(t[4]=e("h2",null,"活动内容",-1))]),e("div",vt,[e("div",{class:"rich-content",innerHTML:o.value.content},null,8,ut)])])):w("",!0),!d.value&&!i.value&&f.showActions?(c(),n("div",_t,[s(I,{class:"register-btn",type:"primary",loading:a.value,onClick:L,block:"",round:""},{default:y(()=>t[5]||(t[5]=[A(" 活动报名 ")])),_:1,__:[5]},8,["loading"])])):w("",!0)])])),w("",!0)])}}}),yt=D(mt,[["__scopeId","data-v-2a0bf19e"]]),ft=x({__name:"index",setup(f){const p=C(),u=V(),r=F(()=>Array.isArray(u.params.id)?u.params.id[0]:u.params.id),l=()=>{p.back()},h=a=>{console.log("活动详情加载完成:",a)},d=a=>{console.error("活动详情加载失败:",a),z(a)},i=a=>{console.log("报名活动:",a)};return(a,o)=>(c(),n(G,null,[s(S,{title:"活动详情",onLeftClick:l}),s(yt,{"activity-id":r.value,"show-actions":!0,onLoaded:h,onError:d,onRegister:i},null,8,["activity-id"]),s($)],64))}}),It=D(ft,[["__scopeId","data-v-c182d174"]]);export{It as default};
