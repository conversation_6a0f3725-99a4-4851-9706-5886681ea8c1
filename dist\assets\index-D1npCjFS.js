import{d as l,a as i,u as p,r as m,l as _,c as u,o as f,b as a,g as h,_ as b}from"./index-BE8XLQ59.js";import{G as v,a as x}from"./GlobalHeader-lL88u8sR.js";import{L as y}from"./LeaderDetailCard-VVs8AxZp.js";import{s as t}from"./function-call-BUl5915X.js";import"./index-CAfqjps3.js";import"./index-B27ddgxZ.js";const g={class:"case-detail-page"},L={class:"detail-content"},R=l({__name:"index",setup(k){const o=i(),r=p(),s=m("");_(()=>{const e=Array.isArray(o.params.id)?o.params.id[0]:o.params.id;e?s.value=e:(t("内容ID无效"),r.back())});const n=e=>{console.log("中医案例详情加载成功:",e)},d=e=>{console.error("中医案例详情加载失败:",e),t(e)},c=()=>{console.log("重试加载中医案例详情")};return(e,B)=>(f(),u("div",g,[a(v,{title:"中医案例详情"}),h("div",L,[a(y,{"leader-id":s.value,"api-type":"case",onLoaded:n,onError:d,onRetry:c},null,8,["leader-id"])]),a(x)]))}}),N=b(R,[["__scopeId","data-v-af45bb8e"]]);export{N as default};
