import { get, requestWithRetry } from '../../api/request';
import { DEPT_URLS, buildUrl } from '../../api/urls';

/**
 * 获取分类
 * @returns 分类数据
 */
export function getDeptCategories() {
  return get<DeptCategoriesResponse>(DEPT_URLS.CATEGORIES);
}

/**
 * 根据分类获取列表
 * @param cat 分类ID
 * @param page 页码
 * @param pageSize 每页数量
 * @returns 列表数据
 */
export function getDeptByCategory(cat: string, page: number = 1, pageSize: number = 10) {
  return get<DeptListResponse>(DEPT_URLS.DEPT, {
    cat,
    page,
    page_size: pageSize
  });
}

/**
 * 带重试功能的获取分类
 * 在网络不稳定情况下使用
 * @returns 分类数据
 */

export function getDeptCategoriesWithRetry(cat: string, page: number = 1, pageSize: number = 10) {
  return requestWithRetry<DeptListResponse>(() => getDeptByCategory(
    cat,
    page,
    pageSize
  ));
}

// 分类接口
export interface DeptCategory {
  value: string;
  label: string;
}

// 分类响应接口
export interface DeptCategoriesResponse {
  categories: DeptCategory[];
}

// 信息接口
export interface DeptItem {
  id: number;
  name: string;
  title: string;
  job: string;
  cat_display: string;
  desc: string;
  thumbnail: string;
  category: string;
  tags?: string[];
}

// 列表响应接口
export interface DeptListResponse {
  results: DeptItem[];
  page: number;
  page_size: number;
  total: number;
  is_last_page: boolean;
}