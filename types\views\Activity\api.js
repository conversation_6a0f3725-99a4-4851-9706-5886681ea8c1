import { get, requestWithRetry } from '../../api/request';
import { ACTIVITY_URLS, buildUrl } from '../../api/urls';
/**
 * 获取活动列表
 * @param params 查询参数 - 支持 param: 'current' | 'upcoming' | 'past'
 * @returns 活动列表数据
 */
export function getActivityList(params) {
    return get(ACTIVITY_URLS.ACTIVITY, params);
}
/**
 * 获取活动列表（带重试）
 * @param params 查询参数 - 支持 param: 'current' | 'upcoming' | 'past'
 * @returns 活动列表数据
 */
export function getActivityListlWithRetry(params) {
    return requestWithRetry(() => getActivityList(params));
}
/**
 * 获取本月活动列表
 * @param params 其他查询参数
 * @returns 活动列表数据
 */
export function getCurrentMonthActivities(params) {
    return getActivityListlWithRetry({ ...params, param: 'current' });
}
/**
 * 获取历史活动列表
 * @param params 其他查询参数
 * @returns 活动列表数据
 */
export function getPastActivities(params) {
    return getActivityListlWithRetry({ ...params, param: 'past' });
}
/**
 * 获取活动预告列表
 * @param params 其他查询参数
 * @returns 活动列表数据
 */
export function getUpcomingActivities(params) {
    return getActivityListlWithRetry({ ...params, param: 'upcoming' });
}
export function getActivityDetail(id) {
    return get(buildUrl(ACTIVITY_URLS.ACTIVITY, id));
}
export function getHomeActivityList(params) {
    return get(ACTIVITY_URLS.HOME_CONTENT, params);
}
