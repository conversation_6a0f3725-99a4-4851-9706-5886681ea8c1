import { ref, watch, onMounted, nextTick } from 'vue';
const scrollContainerRef = ref(null);
const getScrollContainer = () => scrollContainerRef.value;
onMounted(() => {
    nextTick(() => {
        console.log('滚动容器:', scrollContainerRef.value);
    });
});
const props = withDefaults(defineProps(), {
    emptyText: '暂无内容',
    useInfiniteScroll: true,
    finished: false,
});
const emit = defineEmits(['load-more', 'card-click']);
const internalLoading = ref(false);
watch(() => props.loading, (val) => {
    internalLoading.value = val;
}, { immediate: true });
const onLoadMore = () => {
    console.log('触发加载更多');
    emit('load-more');
};
debugger; /* PartiallyEnd: #3632/scriptSetup.vue */
const __VLS_withDefaultsArg = (function (t) { return t; })({
    emptyText: '暂无内容',
    useInfiniteScroll: true,
    finished: false,
});
const __VLS_ctx = {};
let __VLS_components;
let __VLS_directives;
/** @type {__VLS_StyleScopedClasses['news-card']} */ ;
/** @type {__VLS_StyleScopedClasses['video-duration']} */ ;
/** @type {__VLS_StyleScopedClasses['video-quality']} */ ;
/** @type {__VLS_StyleScopedClasses['video-tag']} */ ;
/** @type {__VLS_StyleScopedClasses['featured-badge']} */ ;
// CSS variable injection 
// CSS variable injection end 
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ class: "common-card-list" },
    ref: "scrollContainerRef",
});
/** @type {typeof __VLS_ctx.scrollContainerRef} */ ;
if (__VLS_ctx.loading && !__VLS_ctx.items.length) {
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
        ...{ class: "loading-container" },
    });
    const __VLS_0 = {}.VanLoading;
    /** @type {[typeof __VLS_components.VanLoading, typeof __VLS_components.vanLoading, typeof __VLS_components.VanLoading, typeof __VLS_components.vanLoading, ]} */ ;
    // @ts-ignore
    const __VLS_1 = __VLS_asFunctionalComponent(__VLS_0, new __VLS_0({
        size: "24px",
    }));
    const __VLS_2 = __VLS_1({
        size: "24px",
    }, ...__VLS_functionalComponentArgsRest(__VLS_1));
    __VLS_3.slots.default;
    var __VLS_3;
}
else if (!__VLS_ctx.items.length) {
    const __VLS_4 = {}.VanEmpty;
    /** @type {[typeof __VLS_components.VanEmpty, typeof __VLS_components.vanEmpty, ]} */ ;
    // @ts-ignore
    const __VLS_5 = __VLS_asFunctionalComponent(__VLS_4, new __VLS_4({
        description: (__VLS_ctx.emptyText),
    }));
    const __VLS_6 = __VLS_5({
        description: (__VLS_ctx.emptyText),
    }, ...__VLS_functionalComponentArgsRest(__VLS_5));
}
else if (__VLS_ctx.useInfiniteScroll) {
    const __VLS_8 = {}.VanList;
    /** @type {[typeof __VLS_components.VanList, typeof __VLS_components.vanList, typeof __VLS_components.VanList, typeof __VLS_components.vanList, ]} */ ;
    // @ts-ignore
    const __VLS_9 = __VLS_asFunctionalComponent(__VLS_8, new __VLS_8({
        ...{ 'onLoad': {} },
        loading: (__VLS_ctx.internalLoading),
        finished: (__VLS_ctx.finished),
        finishedText: "没有更多了",
        immediateCheck: (false),
        scrollContainer: (__VLS_ctx.getScrollContainer),
    }));
    const __VLS_10 = __VLS_9({
        ...{ 'onLoad': {} },
        loading: (__VLS_ctx.internalLoading),
        finished: (__VLS_ctx.finished),
        finishedText: "没有更多了",
        immediateCheck: (false),
        scrollContainer: (__VLS_ctx.getScrollContainer),
    }, ...__VLS_functionalComponentArgsRest(__VLS_9));
    let __VLS_12;
    let __VLS_13;
    let __VLS_14;
    const __VLS_15 = {
        onLoad: (__VLS_ctx.onLoadMore)
    };
    __VLS_11.slots.default;
    for (const [item] of __VLS_getVForSourceType((__VLS_ctx.items))) {
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ onClick: (...[$event]) => {
                    if (!!(__VLS_ctx.loading && !__VLS_ctx.items.length))
                        return;
                    if (!!(!__VLS_ctx.items.length))
                        return;
                    if (!(__VLS_ctx.useInfiniteScroll))
                        return;
                    __VLS_ctx.$emit('card-click', item);
                } },
            key: (item.id),
            ...{ class: "news-card animate__animated animate__fadeInUp" },
        });
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "video-thumbnail-wrapper" },
        });
        __VLS_asFunctionalElement(__VLS_intrinsicElements.img)({
            src: (item.thumbnail),
            alt: (item.name),
            ...{ class: "video-thumbnail" },
        });
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "play-button" },
        });
        const __VLS_16 = {}.VanIcon;
        /** @type {[typeof __VLS_components.VanIcon, typeof __VLS_components.vanIcon, ]} */ ;
        // @ts-ignore
        const __VLS_17 = __VLS_asFunctionalComponent(__VLS_16, new __VLS_16({
            name: "play",
            size: "24",
            color: "#fff",
        }));
        const __VLS_18 = __VLS_17({
            name: "play",
            size: "24",
            color: "#fff",
        }, ...__VLS_functionalComponentArgsRest(__VLS_17));
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "video-duration" },
        });
        (item.duration);
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "video-content" },
        });
        __VLS_asFunctionalElement(__VLS_intrinsicElements.h3, __VLS_intrinsicElements.h3)({
            ...{ class: "video-title" },
        });
        (item.name);
        __VLS_asFunctionalElement(__VLS_intrinsicElements.p, __VLS_intrinsicElements.p)({
            ...{ class: "video-summary" },
        });
        (item.desc);
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "video-meta" },
        });
        __VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({
            ...{ class: "author" },
        });
        (item.creater);
        __VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({
            ...{ class: "date" },
        });
        (item.create_time);
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "card-meta" },
        });
        if (item.cat_display) {
            __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
                ...{ class: "meta-left" },
            });
            __VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({
                ...{ class: "category" },
            });
            (item.cat_display);
        }
        if (item.tags && item.tags.length) {
            __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
                ...{ class: "meta-right" },
            });
            __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
                ...{ class: "card-tags" },
            });
            for (const [tag, index] of __VLS_getVForSourceType((item.tags))) {
                __VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({
                    key: (index),
                    ...{ class: "tag" },
                });
                (tag);
            }
        }
    }
    var __VLS_11;
}
else {
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({});
    for (const [item] of __VLS_getVForSourceType((__VLS_ctx.items))) {
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ onClick: (...[$event]) => {
                    if (!!(__VLS_ctx.loading && !__VLS_ctx.items.length))
                        return;
                    if (!!(!__VLS_ctx.items.length))
                        return;
                    if (!!(__VLS_ctx.useInfiniteScroll))
                        return;
                    __VLS_ctx.$emit('card-click', item);
                } },
            key: (item.id),
            ...{ class: "news-card animate__animated animate__fadeInUp" },
        });
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "card-image-container" },
        });
        if (item.tag) {
            const __VLS_20 = {}.VanTag;
            /** @type {[typeof __VLS_components.VanTag, typeof __VLS_components.vanTag, typeof __VLS_components.VanTag, typeof __VLS_components.vanTag, ]} */ ;
            // @ts-ignore
            const __VLS_21 = __VLS_asFunctionalComponent(__VLS_20, new __VLS_20({
                type: "danger",
                size: "medium",
                ...{ class: "news-tag" },
                round: true,
            }));
            const __VLS_22 = __VLS_21({
                type: "danger",
                size: "medium",
                ...{ class: "news-tag" },
                round: true,
            }, ...__VLS_functionalComponentArgsRest(__VLS_21));
            __VLS_23.slots.default;
            (item.tag);
            var __VLS_23;
        }
        __VLS_asFunctionalElement(__VLS_intrinsicElements.img)({
            src: (item.thumbnail),
            alt: (item.name),
            ...{ class: "news-image" },
        });
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "image-overlay" },
        });
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "card-content" },
        });
        __VLS_asFunctionalElement(__VLS_intrinsicElements.h3, __VLS_intrinsicElements.h3)({
            ...{ class: "news-title" },
        });
        (item.name);
        __VLS_asFunctionalElement(__VLS_intrinsicElements.p, __VLS_intrinsicElements.p)({
            ...{ class: "news-description" },
        });
        (item.desc);
    }
}
/** @type {__VLS_StyleScopedClasses['common-card-list']} */ ;
/** @type {__VLS_StyleScopedClasses['loading-container']} */ ;
/** @type {__VLS_StyleScopedClasses['news-card']} */ ;
/** @type {__VLS_StyleScopedClasses['animate__animated']} */ ;
/** @type {__VLS_StyleScopedClasses['animate__fadeInUp']} */ ;
/** @type {__VLS_StyleScopedClasses['video-thumbnail-wrapper']} */ ;
/** @type {__VLS_StyleScopedClasses['video-thumbnail']} */ ;
/** @type {__VLS_StyleScopedClasses['play-button']} */ ;
/** @type {__VLS_StyleScopedClasses['video-duration']} */ ;
/** @type {__VLS_StyleScopedClasses['video-content']} */ ;
/** @type {__VLS_StyleScopedClasses['video-title']} */ ;
/** @type {__VLS_StyleScopedClasses['video-summary']} */ ;
/** @type {__VLS_StyleScopedClasses['video-meta']} */ ;
/** @type {__VLS_StyleScopedClasses['author']} */ ;
/** @type {__VLS_StyleScopedClasses['date']} */ ;
/** @type {__VLS_StyleScopedClasses['card-meta']} */ ;
/** @type {__VLS_StyleScopedClasses['meta-left']} */ ;
/** @type {__VLS_StyleScopedClasses['category']} */ ;
/** @type {__VLS_StyleScopedClasses['meta-right']} */ ;
/** @type {__VLS_StyleScopedClasses['card-tags']} */ ;
/** @type {__VLS_StyleScopedClasses['tag']} */ ;
/** @type {__VLS_StyleScopedClasses['news-card']} */ ;
/** @type {__VLS_StyleScopedClasses['animate__animated']} */ ;
/** @type {__VLS_StyleScopedClasses['animate__fadeInUp']} */ ;
/** @type {__VLS_StyleScopedClasses['card-image-container']} */ ;
/** @type {__VLS_StyleScopedClasses['news-tag']} */ ;
/** @type {__VLS_StyleScopedClasses['news-image']} */ ;
/** @type {__VLS_StyleScopedClasses['image-overlay']} */ ;
/** @type {__VLS_StyleScopedClasses['card-content']} */ ;
/** @type {__VLS_StyleScopedClasses['news-title']} */ ;
/** @type {__VLS_StyleScopedClasses['news-description']} */ ;
var __VLS_dollars;
const __VLS_self = (await import('vue')).defineComponent({
    setup() {
        return {
            scrollContainerRef: scrollContainerRef,
            getScrollContainer: getScrollContainer,
            internalLoading: internalLoading,
            onLoadMore: onLoadMore,
        };
    },
    emits: {},
    __typeProps: {},
    props: {},
});
export default (await import('vue')).defineComponent({
    setup() {
        return {};
    },
    emits: {},
    __typeProps: {},
    props: {},
});
; /* PartiallyEnd: #4569/main.vue */
