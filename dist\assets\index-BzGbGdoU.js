import{d as st,r as A,l as ot,B as vt,y as at,c as f,o as d,f as x,b as l,L as nt,w as m,j as C,F as N,e as U,g as e,i as I,t as r,I as it,h as R,H as ft,J as X,_ as lt,u as pt,a as gt,N as yt,n as tt}from"./index-BE8XLQ59.js";import{B as ht}from"./index-jIY073my.js";import{E as rt}from"./index-B27ddgxZ.js";import{C as mt,a as bt,G as wt}from"./index-BJjcaqGM.js";import{G as kt,a as Ct}from"./GlobalHeader-lL88u8sR.js";import{L as $t}from"./index-ouF_E0m0.js";import{T as At}from"./index-CAfqjps3.js";import{f as q,a as Z,g as et}from"./dateTime-CGItJ1-U.js";import{g as xt}from"./api-DTO_fYNL.js";import{G as Tt}from"./GridCard-D-EgbodX.js";import{s as w}from"./function-call-BUl5915X.js";import"./index-sbxbYdRt.js";const zt={key:0,class:"loading-container"},Dt=["onClick"],It={class:"card-image-container"},Lt=["src","alt"],Rt={class:"card-content"},St={class:"activity-title"},Bt={class:"activity-description"},Nt={class:"activity-details"},Ut={class:"detail-item"},qt={class:"detail-content"},Et={class:"detail-value"},Gt={class:"detail-item"},Ft={class:"detail-content"},Mt={class:"detail-value"},Vt={class:"detail-item"},Pt={class:"detail-content"},Ht={class:"detail-value"},Wt={class:"detail-item"},Jt={class:"detail-content"},jt={class:"detail-item"},Kt={class:"detail-content"},Ot={class:"detail-item"},Qt={class:"detail-content"},Xt={class:"detail-value"},Yt={class:"detail-item"},Zt={class:"detail-content"},te={class:"detail-value"},ee={class:"detail-item"},se={class:"detail-content"},oe={class:"detail-value"},ae={class:"activity-footer"},ne={class:"footer-left"},ie={key:0,class:"activity-tags"},le={class:"footer-right"},re={key:0,class:"activity-heat"},ce={class:"activity-actions"},de={key:3},ue=["onClick"],_e={class:"card-image-container"},ve=["src","alt"],fe={class:"card-content"},pe={class:"activity-title"},ge={class:"activity-description"},ye={class:"activity-details"},he={class:"detail-item"},me={class:"detail-content"},be={class:"detail-value"},we={class:"detail-item"},ke={class:"detail-content"},Ce={class:"detail-value"},$e={class:"detail-item"},Ae={class:"detail-content"},xe={class:"detail-value"},Te={class:"detail-item"},ze={class:"detail-content"},De={class:"detail-item"},Ie={class:"detail-content"},Le={class:"detail-item"},Re={class:"detail-content"},Se={class:"detail-value"},Be={class:"detail-item"},Ne={class:"detail-content"},Ue={class:"detail-value"},qe={class:"detail-item"},Ee={class:"detail-content"},Ge={class:"detail-value"},Fe={class:"activity-footer"},Me={class:"footer-left"},Ve={key:0,class:"activity-tags"},Pe={class:"footer-right"},He={key:0,class:"activity-heat"},We={class:"activity-actions"},Je=st({__name:"ActivityCommonCardList",props:{items:{},loading:{type:Boolean},finished:{type:Boolean,default:!1},emptyText:{default:"暂无活动"},useInfiniteScroll:{type:Boolean,default:!0}},emits:["load-more","card-click","share","register"],setup(n,{emit:c}){const g=A(null),b=()=>g.value;ot(()=>{vt(()=>{console.log("Activity滚动容器:",g.value)})});const _=n,h=c,y=A(!1);at(()=>_.loading,t=>{y.value=t},{immediate:!0});const k=()=>{console.log("触发加载更多活动"),h("load-more")},p=t=>{if(t.status)return t.status;const o=new Date,z=t.registration_deadline?new Date(t.registration_deadline):null;if(z&&o>z)return"报名中";const S=t.startdate?new Date(t.startdate):null,D=t.enddate?new Date(t.enddate):null;if(S&&D){if(o<S)return"报名中";if(o>=S&&o<=D)return"进行中";if(o>D)return"报名中"}return"报名中"},B=t=>{switch(p(t)){case"进行中":return"success";case"即将开始":return"warning";case"已结束":return"default";case"报名中":return"primary";default:return"primary"}},E=t=>t.startdate&&t.enddate?q(t.startdate,t.enddate):t.start_time&&t.end_time?q(t.start_time,t.end_time):t.startdate?q(t.startdate,""):t.start_time?q(t.start_time,""):"时间待定",T=t=>t.current_participants!==void 0&&t.max_participants!==void 0?`${t.current_participants}/${t.max_participants}人`:t.participant_count!==void 0?`${t.participant_count}人已报名`:t.nums!==void 0?`${t.nums}人已报名`:t.max_participants!==void 0?`限${t.max_participants}人`:"暂无数据",G=t=>t.isfree==="免费"||t.price===0?"免费":t.price!==void 0?`¥${t.price}`:t.isfree?t.isfree:"免费",F=t=>{switch(t.activity_type||t.activity_form||t.form_type||""){case"在线直播":case"online_live":return"play-circle-o";case"线下组织":case"offline_organize":return"shop-o";case"在线答题":case"online_quiz":return"edit";case"线上活动":case"online_activity":return"desktop-o";case"混合活动":case"hybrid_activity":return"exchange";default:return"medal-o"}},M=t=>{switch(t.activity_type||t.activity_form||t.form_type||""){case"在线直播":case"online_live":return"form-live";case"线下组织":case"offline_organize":return"form-offline";case"在线答题":case"online_quiz":return"form-quiz";case"线上活动":case"online_activity":return"form-online";case"混合活动":case"hybrid_activity":return"form-hybrid";default:return"form-default"}},V=t=>{const o=t.activity_type||t.activity_form||t.form_type||"";return{online_live:"在线直播",offline_organize:"线下组织",online_quiz:"在线答题",online_activity:"线上活动",hybrid_activity:"混合活动"}[o]||o||"暂无数据"},P=t=>t&&t.name&&t.name.trim()!=="",H=t=>{if(console.log("=== ActivityCommonCardList 分享点击 ==="),console.log("点击的活动项:",t),console.log("当前组件的items数据:",_.items),!P(t)){console.warn("分享失败：活动数据不完整",{item:t,hasName:!!(t!=null&&t.name),nameValue:t==null?void 0:t.name,nameType:typeof(t==null?void 0:t.name)});return}console.log("数据验证通过，触发分享事件:",t.name),h("share",t)},W=t=>{console.log("报名活动:",t.name),h("register",t)},L=t=>(p(t),!(t.current_participants&&t.max_participants&&t.current_participants>=t.max_participants)),a=t=>{if(!L(t))return"default";const o=p(t);return o==="报名中"?"primary":o==="进行中"?"success":"primary"},i=t=>{if(!L(t))return"cross";const o=p(t);return o==="报名中"?"add-o":o==="进行中"?"play-circle-o":"add-o"},u=t=>{if(!L(t))return t.current_participants&&t.max_participants&&t.current_participants>=t.max_participants?"报名满员":"无法报名";const o=p(t);return o==="报名中"?"立即报名":o==="进行中"?"正在进行":"立即报名"},J=t=>t.location?t.location:t.address?t.address:"暂无数据",j=t=>t.dieline?Z(t.dieline):t.registration_deadline?Z(t.registration_deadline):t.deadline?Z(t.deadline):"暂无数据",K=t=>t.leader?t.leader:t.organizer?t.organizer:"暂无数据",O=t=>t.contact?t.contact:"暂无数据";return(t,o)=>{const z=nt,S=rt,D=At,v=it,Q=ft,_t=$t;return d(),f("div",{class:"activity-common-card-list",ref_key:"scrollContainerRef",ref:g},[t.loading&&!t.items.length?(d(),f("div",zt,[l(z,{size:"24px"},{default:m(()=>o[1]||(o[1]=[C("加载中...")])),_:1,__:[1]})])):t.items.length?t.useInfiniteScroll?(d(),x(_t,{key:2,loading:y.value,"onUpdate:loading":o[0]||(o[0]=s=>y.value=s),finished:t.finished,"finished-text":"没有更多了",onLoad:k,"immediate-check":!1,"scroll-container":b},{default:m(()=>[(d(!0),f(N,null,U(t.items,s=>(d(),f("div",{key:s.id,class:"activity-card animate__animated animate__fadeInUp",onClick:$=>t.$emit("card-click",s)},[e("div",It,[p(s)?(d(),x(D,{key:0,type:B(s),size:"medium",class:"activity-status-tag",round:""},{default:m(()=>[C(r(p(s)),1)]),_:2},1032,["type"])):I("",!0),s.activity_type||s.cat_name?(d(),x(D,{key:1,type:"primary",size:"medium",class:"activity-type-tag",round:""},{default:m(()=>[C(r(s.activity_type||s.cat_name||"活动"),1)]),_:2},1024)):I("",!0),e("img",{src:s.thumbnail,alt:s.name,class:"activity-image"},null,8,Lt),o[2]||(o[2]=e("div",{class:"image-overlay"},null,-1))]),e("div",Rt,[e("h3",St,r(s.name),1),e("p",Bt,r(s.desc||s.description),1),e("div",Nt,[e("div",Ut,[l(v,{name:"clock-o"}),e("div",qt,[o[3]||(o[3]=e("span",{class:"detail-label"},"活动时间",-1)),e("span",Et,r(E(s)),1)])]),e("div",Gt,[l(v,{name:"location-o"}),e("div",Ft,[o[4]||(o[4]=e("span",{class:"detail-label"},"活动地点",-1)),e("span",Mt,r(J(s)),1)])]),e("div",Vt,[l(v,{name:"friends-o"}),e("div",Pt,[o[5]||(o[5]=e("span",{class:"detail-label"},"参与人数",-1)),e("span",Ht,r(T(s)),1)])]),e("div",Wt,[l(v,{name:F(s)},null,8,["name"]),e("div",Jt,[o[6]||(o[6]=e("span",{class:"detail-label"},"活动形式",-1)),e("span",{class:R(["detail-value activity-form",M(s)])},r(V(s)),3)])]),e("div",jt,[l(v,{name:"gold-coin-o"}),e("div",Kt,[o[7]||(o[7]=e("span",{class:"detail-label"},"活动费用",-1)),e("span",{class:R(["detail-value",{"free-activity":s.isfree==="免费"||s.price===0}])},r(G(s)),3)])]),e("div",Ot,[l(v,{name:"calendar-o"}),e("div",Qt,[o[8]||(o[8]=e("span",{class:"detail-label"},"报名截止",-1)),e("span",Xt,r(j(s)),1)])]),e("div",Yt,[l(v,{name:"manager-o"}),e("div",Zt,[o[9]||(o[9]=e("span",{class:"detail-label"},"主办方",-1)),e("span",te,r(K(s)),1)])]),e("div",ee,[l(v,{name:"phone-o"}),e("div",se,[o[10]||(o[10]=e("span",{class:"detail-label"},"联系方式",-1)),e("span",oe,r(O(s)),1)])])]),e("div",ae,[e("div",ne,[s.tags&&s.tags.length?(d(),f("div",ie,[(d(!0),f(N,null,U(s.tags,($,Y)=>(d(),f("span",{key:Y,class:"activity-tag"},r($),1))),128))])):I("",!0)]),e("div",le,[s.views||s.likes?(d(),f("div",re,[l(v,{name:"fire-o"}),e("span",null,r(s.views||s.likes||0),1)])):I("",!0)])]),e("div",ce,[l(Q,{type:"default",size:"small",icon:"share-o",class:"action-button share-button",disabled:!s||!s.name||s.name.trim()==="",onClick:X($=>H(s),["stop"])},{default:m(()=>o[11]||(o[11]=[C(" 转发 ")])),_:2,__:[11]},1032,["disabled","onClick"]),l(Q,{type:a(s),size:"small",icon:i(s),class:"action-button register-button",disabled:!L(s),onClick:X($=>W(s),["stop"])},{default:m(()=>[C(r(u(s)),1)]),_:2},1032,["type","icon","disabled","onClick"])])])],8,Dt))),128))]),_:1},8,["loading","finished"])):(d(),f("div",de,[(d(!0),f(N,null,U(t.items,s=>(d(),f("div",{key:s.id,class:"activity-card animate__animated animate__fadeInUp",onClick:$=>t.$emit("card-click",s)},[e("div",_e,[p(s)?(d(),x(D,{key:0,type:B(s),size:"medium",class:"activity-status-tag",round:""},{default:m(()=>[C(r(p(s)),1)]),_:2},1032,["type"])):I("",!0),s.activity_type||s.cat_name?(d(),x(D,{key:1,type:"primary",size:"medium",class:"activity-type-tag",round:""},{default:m(()=>[C(r(s.activity_type||s.cat_name||"活动"),1)]),_:2},1024)):I("",!0),e("img",{src:s.thumbnail,alt:s.name,class:"activity-image"},null,8,ve),o[12]||(o[12]=e("div",{class:"image-overlay"},null,-1))]),e("div",fe,[e("h3",pe,r(s.name),1),e("p",ge,r(s.desc||s.description),1),e("div",ye,[e("div",he,[l(v,{name:"clock-o"}),e("div",me,[o[13]||(o[13]=e("span",{class:"detail-label"},"活动时间",-1)),e("span",be,r(E(s)),1)])]),e("div",we,[l(v,{name:"location-o"}),e("div",ke,[o[14]||(o[14]=e("span",{class:"detail-label"},"活动地点",-1)),e("span",Ce,r(J(s)),1)])]),e("div",$e,[l(v,{name:"friends-o"}),e("div",Ae,[o[15]||(o[15]=e("span",{class:"detail-label"},"参与人数",-1)),e("span",xe,r(T(s)),1)])]),e("div",Te,[l(v,{name:F(s)},null,8,["name"]),e("div",ze,[o[16]||(o[16]=e("span",{class:"detail-label"},"活动形式",-1)),e("span",{class:R(["detail-value activity-form",M(s)])},r(V(s)),3)])]),e("div",De,[l(v,{name:"gold-coin-o"}),e("div",Ie,[o[17]||(o[17]=e("span",{class:"detail-label"},"活动费用",-1)),e("span",{class:R(["detail-value",{"free-activity":s.isfree==="免费"||s.price===0}])},r(G(s)),3)])]),e("div",Le,[l(v,{name:"calendar-o"}),e("div",Re,[o[18]||(o[18]=e("span",{class:"detail-label"},"报名截止",-1)),e("span",Se,r(j(s)),1)])]),e("div",Be,[l(v,{name:"manager-o"}),e("div",Ne,[o[19]||(o[19]=e("span",{class:"detail-label"},"主办方",-1)),e("span",Ue,r(K(s)),1)])]),e("div",qe,[l(v,{name:"phone-o"}),e("div",Ee,[o[20]||(o[20]=e("span",{class:"detail-label"},"联系方式",-1)),e("span",Ge,r(O(s)),1)])])]),e("div",Fe,[e("div",Me,[s.tags&&s.tags.length?(d(),f("div",Ve,[(d(!0),f(N,null,U(s.tags,($,Y)=>(d(),f("span",{key:Y,class:"activity-tag"},r($),1))),128))])):I("",!0)]),e("div",Pe,[s.views||s.likes?(d(),f("div",He,[l(v,{name:"fire-o"}),e("span",null,r(s.views||s.likes||0),1)])):I("",!0)])]),e("div",We,[l(Q,{type:"default",size:"small",icon:"share-o",class:"action-button share-button",disabled:!P(s),onClick:X($=>H(s),["stop"])},{default:m(()=>o[21]||(o[21]=[C(" 转发 ")])),_:2,__:[21]},1032,["disabled","onClick"]),l(Q,{type:a(s),size:"small",icon:i(s),class:"action-button register-button",disabled:!L(s),onClick:X($=>W(s),["stop"])},{default:m(()=>[C(r(u(s)),1)]),_:2},1032,["type","icon","disabled","onClick"])])])],8,ue))),128))])):(d(),x(S,{key:1,description:t.emptyText},null,8,["description"]))],512)}}}),je=lt(Je,[["__scopeId","data-v-0681477c"]]);function ct(n){if(console.log("=== 生成分享文本 ==="),console.log("输入的活动数据:",n),!n)throw console.error("活动数据为空"),new Error("活动数据不能为空");if(!n.id)throw console.error("活动ID缺失"),new Error("活动ID不能为空");const g=`${window.location.origin}/#/activity-detail/${n.id}`,b=n.name&&n.name.trim()?n.name.trim():"精彩活动",_=n.desc&&n.desc.trim()?n.desc.trim():"";console.log("处理后的活动名称:",b),console.log("处理后的活动描述:",_);const h=q(n.startdate||"",n.enddate||""),y=n.location&&n.location.trim()?n.location.trim():"医院",k=n.isfree==="免费"||n.price===0?"免费":n.price?`¥${n.price}`:"费用待定";n.current_participants,n.max_participants;const p=`📢 ${b}

⏰ 时间：${h}
📍 地点：${y}
💰 费用：${k}

${_?_+`

`:""}点击链接查看详情：
${g}`;return console.log("生成的分享文本:",p),console.log("=== 分享文本生成完成 ==="),p}function dt(){return navigator.userAgent.toLowerCase().includes("micromessenger")}async function Ke(n){if(console.log("=== 复制到剪贴板 ==="),console.log("要复制的文本长度:",n.length),console.log("文本内容预览:",n.substring(0,100)+(n.length>100?"...":"")),!n||n.trim()==="")return console.error("复制失败：文本为空"),!1;try{if(navigator.clipboard&&navigator.clipboard.writeText)return console.log("使用现代剪贴板API"),await navigator.clipboard.writeText(n),console.log("现代剪贴板API复制成功"),!0;{console.log("使用传统复制方法");const c=document.createElement("textarea");c.value=n,c.style.position="fixed",c.style.left="-999999px",c.style.top="-999999px",document.body.appendChild(c),c.focus(),c.select();const g=document.execCommand("copy");return document.body.removeChild(c),console.log("传统复制方法结果:",g),g}}catch(c){return console.error("复制失败:",c),!1}}function ut(){return"share"in navigator&&navigator.share!==void 0}async function Oe(n){console.log("=== 执行分享操作 ==="),console.log("分享活动数据:",n);try{if(!n)return console.error("分享失败：活动数据为空"),{success:!1,method:"validation_failed"};if(!n.id)return console.error("分享失败：活动ID为空"),{success:!1,method:"validation_failed"};if(!n.name||n.name.trim()==="")return console.error("分享失败：活动名称为空"),{success:!1,method:"validation_failed"};const c=ct(n),b=`${window.location.origin}/#/activity-detail/${n.id}`;if(console.log("分享文本生成成功，长度:",c.length),ut())try{return console.log("使用原生分享API"),await navigator.share({title:n.name,text:c,url:b}),console.log("原生分享成功"),{success:!0,method:"native"}}catch(h){if(h.name==="AbortError")return console.log("用户取消原生分享"),{success:!1,method:"cancelled"};console.log("原生分享失败，使用复制方式:",h)}if(dt()){console.log("检测到微信环境");try{if(typeof window.wx<"u"){const h=window.wx;console.log("检测到微信JS-SDK，但需要后端配置签名")}}catch(h){console.log("微信分享尝试失败:",h)}}console.log("使用复制到剪贴板方式");const _=await Ke(c);return console.log("复制结果:",_),{success:_,method:_?"clipboard":"failed"}}catch(c){return console.error("分享过程中发生异常:",c),{success:!1,method:"error"}}}async function Qe(n){const c=ct(n),b=`${window.location.origin}/#/activity-detail/${n.id}`;if("share"in navigator)try{return await navigator.share({title:n.name,text:c,url:b}),!0}catch(_){if(_.name==="AbortError")return console.log("用户取消分享"),!1;throw console.error("分享失败:",_),_}else throw new Error("浏览器不支持原生分享")}const Xe={class:"activity-page"},Ye={class:"section-container"},Ze={class:"section-header"},ts={class:"section-title"},es=["onClick"],ss={class:"section-container"},os={class:"section-header"},as={class:"section-title"},ns={class:"section-container"},is={class:"section-header"},ls={class:"section-title"},rs={class:"activity-reviews"},cs={key:0,class:"loading-container"},ds=st({__name:"index",setup(n){const c=pt(),g=gt(),b=A(!1),_=A(!1),h=A([{icon:"calendar-o",text:"本月活动",bgColor:"#e8f4ff",iconColor:"#4b8bf4",route:"/activity-list?type=current",routeName:"ActivityList",queryType:"current"},{icon:"clock-o",text:"历史活动",bgColor:"#f0e6ff",iconColor:"#7c3aed",route:"/activity-list?type=past",routeName:"ActivityList",queryType:"past"},{icon:"underway-o",text:"活动预告",bgColor:"#e6f7ef",iconColor:"#059669",route:"/activity-list?type=upcoming",routeName:"ActivityList",queryType:"upcoming"},{icon:"newspaper-o",text:"活动新闻",bgColor:"#ffebe6",iconColor:"#ff8c69",route:"/activity-news-list",routeName:"ActivityNewsList"}]),y=A({activity_data:[]});A([]),A([]);const k=A([]),p=async()=>{try{b.value=!0;const a=await et();console.log("获取首页活动列表成功:",a),y.value.activity_data=a.items.activity_data}catch(a){console.error("获取活动列表失败:",a)}finally{b.value=!1}},B=async()=>{try{_.value=!0;const a=await xt({page_size:6});k.value=a.results.map(i=>({id:i.id,title:i.name,image:i.thumbnail,badge:"回顾",originalData:i}))}catch(a){console.error("获取活动回顾失败:",a);try{const i=await et();k.value=i.items.activity_data.slice(0,6).map(u=>({id:u.id,title:u.name,image:u.thumbnail,badge:"回顾",originalData:u}))}catch(i){console.error("获取活动回顾数据完全失败:",i)}}finally{_.value=!1}};ot(()=>{p(),B()}),yt(()=>{console.log("活动页面被激活，检查数据状态"),(!y.value.activity_data||y.value.activity_data.length===0)&&(console.log("检测到活动数据为空，重新加载"),p()),(!k.value||k.value.length===0)&&(console.log("检测到活动回顾数据为空，重新加载"),B())}),at(()=>g.name,(a,i)=>{a==="Activity"&&i&&i!=="Activity"&&(console.log(`从 ${String(i)} 页面返回到活动页面，验证数据状态`),setTimeout(()=>{(!y.value.activity_data||y.value.activity_data.length===0)&&(console.log("返回活动页面时检测到数据丢失，重新加载"),p())},100))});const E=a=>{c.push({name:"ActivityDetail",params:{id:a.id},query:{from:"ongoing"}})},T=a=>a.queryType?g.name===a.routeName&&g.query.type===a.queryType:g.name===a.routeName,G=a=>T(a)?"#fff":a.iconColor,F=a=>T(a)?{background:`linear-gradient(135deg, ${a.iconColor}, ${a.iconColor}dd)`}:{backgroundColor:a.bgColor},M=a=>{a.route&&c.push(a.route)},V=()=>{c.back()},P=()=>{c.push("/activity-news-list")},H=a=>{const i=a.originalData||a;c.push({name:"ActivityNewsDetail",params:{id:i.id}})},W=async a=>{if(console.log("=== 开始分享流程 ==="),console.log("分享活动原始数据:",a),console.log("当前页面活动数据状态:",y.value.activity_data),!a){console.error("分享失败：item为空",a),w("分享数据异常，请刷新页面后重试");return}if(!a.name||typeof a.name!="string"||a.name.trim()==="")if(console.error("分享失败：活动名称无效",{name:a.name,type:typeof a.name,item:a}),a.id&&y.value.activity_data.length>0){const i=y.value.activity_data.find(u=>u.id===a.id||u.id.toString()===a.id.toString());if(i&&i.name&&i.name.trim()!=="")console.log("从页面数据中找到完整的活动信息，使用完整数据进行分享"),a=i;else{console.error("无法找到完整的活动数据"),w("活动数据不完整，请刷新页面后重试");return}}else{w("活动数据加载中，请稍后再试");return}console.log("验证通过，开始执行分享:",a.name);try{if(ut())try{if(console.log("尝试使用原生分享API"),await Qe(a)){console.log("原生分享成功"),w("分享成功");return}}catch(u){console.log("原生分享失败，尝试其他方式:",u)}console.log("使用备用分享方案");const i=await Oe(a);if(console.log("分享结果:",i),i.success)switch(i.method){case"native":w("分享成功");break;case"clipboard":dt()?w("内容已复制，请在微信中长按粘贴分享"):w("内容已复制，请粘贴到微信等应用中分享");break;default:w("分享成功")}else{if(i.method==="cancelled"){console.log("用户取消分享");return}console.error("分享失败:",i),w("分享失败，请稍后重试")}}catch(i){console.error("分享过程中发生异常:",i),w("分享失败，请稍后重试")}console.log("=== 分享流程结束 ===")},L=a=>{w(`报名活动: ${a.name}`),c.push({name:"ActivityRegistration",params:{id:a.id}})};return(a,i)=>{const u=it,J=bt,j=wt,K=nt,O=rt,t=ht;return d(),f("div",Xe,[i[5]||(i[5]=e("meta",{name:"viewport",content:"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no"},null,-1)),l(kt,{title:"精彩活动",onLeftClick:V}),l(mt,{position:"3"}),e("div",Ye,[e("div",Ze,[e("div",ts,[l(u,{name:"apps-o",color:"#4b8bf4",size:"18"}),i[0]||(i[0]=e("span",null,"活动导航",-1))])]),l(j,{"column-num":4,border:!1,gutter:10,class:"function-grid navigation-row"},{default:m(()=>[(d(!0),f(N,null,U(h.value,(o,z)=>(d(),x(J,{key:z,class:"animate__animated animate__fadeInUp",style:tt({animationDelay:z*.05+"s"})},{default:m(()=>[e("div",{class:R(["grid-item-content",{active:T(o)}]),onClick:S=>M(o)},[e("div",{class:R(["icon-wrapper",{active:T(o)}]),style:tt(F(o))},[l(u,{name:o.icon,color:G(o),size:"24"},null,8,["name","color"])],6),e("span",{class:R(["grid-text",{active:T(o)}])},r(o.text),3)],10,es)]),_:2},1032,["style"]))),128))]),_:1})]),e("div",ss,[e("div",os,[e("div",as,[l(u,{name:"play-circle-o",color:"#10ac84",size:"18"}),i[1]||(i[1]=e("span",null,"正在报名中的活动",-1))])]),l(je,{items:y.value.activity_data,loading:b.value,finished:!0,"use-infinite-scroll":!1,onCardClick:E,onShare:W,onRegister:L},null,8,["items","loading"])]),e("div",ns,[e("div",is,[e("div",ls,[l(u,{name:"photo-o",color:"#ff6b35",size:"18"}),i[2]||(i[2]=e("span",null,"活动回顾",-1))]),e("div",{class:"section-more",onClick:P},[i[3]||(i[3]=e("span",null,"查看更多",-1)),l(u,{name:"arrow",size:"12"})])]),e("div",rs,[_.value?(d(),f("div",cs,[l(K,{size:"24px"},{default:m(()=>i[4]||(i[4]=[C("加载中...")])),_:1,__:[4]})])):k.value.length?(d(),x(Tt,{key:2,items:k.value,onCardClick:H},null,8,["items"])):(d(),x(O,{key:1,description:"暂无活动回顾"}))])]),l(t,{right:"16",bottom:"80"}),l(Ct)])}}}),Cs=lt(ds,[["__scopeId","data-v-d88a3198"]]);export{Cs as default};
