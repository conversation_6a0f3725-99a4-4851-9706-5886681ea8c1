import { get, post, requestWithRetry } from '../../api/request';
import { FAMOUS_DOCTOR_URLS, buildUrl } from '../../api/urls';

/**
 * 获取领导详情
 * @param id 领导ID
 * @returns 领导详情数据
 */
export function getDoctorDetail(id: string | number) {
  return get<DoctorDetailResponse>(buildUrl(FAMOUS_DOCTOR_URLS.DOCTOR, id));
}

/**
 * 带重试功能的获取领导详情
 * 在网络不稳定情况下使用
 * @param id 领导ID
 * @returns 领导详情数据
 */
export function getDoctorDetailWithRetry(id: string | number) {
  return requestWithRetry<DoctorDetailResponse>(() => getDoctorDetail(id));
}

// 工作经历接口
export interface Experience {
  period: string;      // 时间段，如 "2015-2020"
  position: string;    // 职位
  organization: string; // 组织/单位
}

// 联系方式接口
export interface Contact {
  email?: string;      // 电子邮箱
  phone?: string;      // 电话
  office?: string;     // 办公室位置
}

// API返回的领导详情响应接口
export interface DoctorDetailResponse {
  famousdoctor: {
    id: number;           // 领导ID
    name: string;         // 姓名
    job: string;          // 职位
    desc: string;         // 描述/简介
    thumbnail: string;    // 缩略图/头像
    cat_display: string;  // 分类显示名称
    tags: string[];       // 标签数组
    content: string;      // HTML格式的详细内容
    create_time: string;  // 创建时间
    viewtimes_display: string; // 浏览次数显示
  }
}

// 领导详情接口（组件内部使用）
export interface DoctorDetail {
  id: number;           // 领导ID
  name: string;         // 姓名
  job: string;          // 职位
  desc: string;         // 描述/简介
  thumbnail: string;    // 缩略图/头像
  category: string;     // 分类ID
  category_name?: string; // 分类名称（前端计算）
  content?: string;     // HTML格式的详细内容
  viewtimes_display?: string; // 浏览次数显示
  specialties?: string[]; // 专业特长
  experience?: Experience[]; // 工作经历
  honors?: string[];    // 荣誉奖项
  publications?: string[]; // 学术成果/出版物
  gallery?: string[];   // 相册图片
  contact?: Contact | null; // 联系方式
}