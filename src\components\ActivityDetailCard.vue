<template>
  <div class="activity-detail-card">
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <van-loading type="spinner" color="#4b8bf4" size="32px" />
      <p>加载中...</p>
    </div>

    <!-- 错误提示 -->
    <div v-else-if="error" class="error-container">
      <van-empty description="加载失败" image="error">
        <template #description>
          <p>{{ error }}</p>
        </template>
        <van-button round type="primary" @click="fetchActivityDetail">重试</van-button>
      </van-empty>
    </div>

    <!-- 活动内容 -->
    <div v-else class="content">
      <!-- 活动封面图 -->
      <div class="activity-hero">
        <img :src="activity.thumbnail" :alt="activity.name" class="hero-image" />
      </div>

      <!-- 活动主题区域 -->
      <div class="activity-theme-section">
        <h1 class="activity-title">{{ activity.name }}</h1>
        <div class="activity-subtitle">{{ activity.desc }}</div>
      </div>

      <!-- 活动时间地点信息 -->
      <div class="activity-info-container">
        <div class="info-item">
          <div class="info-icon time-icon">
            <van-icon name="clock-o" />
          </div>
          <div class="info-content">
            <div class="info-label">活动时间</div>
            <div class="info-value">{{ formatActivityTimeRange(activity.startdate, activity.enddate) }}</div>
          </div>
        </div>
        
        <div class="info-item">
          <div class="info-icon location-icon">
            <van-icon name="location-o" />
          </div>
          <div class="info-content">
            <div class="info-label">活动地点</div>
            <div class="info-value">{{ activity.location || '待定' }}</div>
          </div>
        </div>
      </div>

      <!-- 活动详细内容 -->
      <div class="content-sections">
        <!-- 活动内容 -->
        <div class="content-section" v-if="activity.content">
          <div class="section-header">
            <van-icon name="notes-o" color="#4b8bf4" />
            <h2>活动内容</h2>
          </div>
          <div class="section-content">
            <div class="rich-content" v-html="activity.content"></div>
          </div>
        </div>

        <!-- 报名按钮区域 -->
        <div class="register-section" v-if="!loading && !error && showActions">
          <van-button 
            class="register-btn" 
            type="primary"
            :loading="submitting"
            @click="handleRegister"
            block
            round
          >
            活动报名
          </van-button>
        </div>
      </div>
    </div>
    
    <!-- 调试信息 -->
    <div v-if="false" style="position: fixed; top: 50px; left: 10px; background: yellow; padding: 10px; z-index: 9999;">
      Loading: {{ loading }}<br>
      Error: {{ error }}<br>
      ShowActions: {{ showActions }}<br>
      ActivityId: {{ activityId }}<br>
      ButtonCondition: {{ !loading && !error && showActions }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, defineProps, defineEmits } from 'vue';
import { useRouter } from 'vue-router';
import { showToast } from 'vant';
import { getActivityDetail } from '../views/Activity/api';
import type { ActivityDetailItem } from '../views/Activity/api';
import { formatActivityTimeRange } from '../utils/dateTime';


// Props
const props = defineProps({
  activityId: {
    type: [String, Number],
    required: true
  },
  showActions: {
    type: Boolean,
    default: true
  }
});

// Emits
const emit = defineEmits(['loaded', 'error', 'register']);

const router = useRouter();
const loading = ref(false);
const error = ref('');
const submitting = ref(false);

// 活动数据
const activity = ref<ActivityDetailItem>({
  id: 0,
  name: '',
  cat: '',
  cat_name: '',
  desc: '',
  thumbnail: '',
  isfree: 0,
  startdate: '',
  enddate: '',
  views: 0,
  location: '',
  content: '',
});

// 获取活动详情
const fetchActivityDetail = async () => {
  if (!props.activityId) {
    error.value = '活动ID无效';
    emit('error', '活动ID无效');
    return;
  }

  try {
    loading.value = true;
    error.value = '';
    const res = await getActivityDetail(props.activityId.toString());
    const data = res.activity;

    activity.value = {
      id: data.id,
      name: data.name,
      cat: data.cat,
      cat_name: data.cat_name,
      desc: data.desc,
      startdate: data.startdate,
      enddate: data.enddate,
      location: data.location,
      thumbnail: data.thumbnail,
      views: data.views,
      isfree: data.isfree,
      content: data.content || '',
    };

    emit('loaded', activity.value);
  } catch (err) {
    console.error('获取活动详情失败:', err);
    error.value = '获取活动详情失败，请稍后再试';
    emit('error', error.value);
  } finally {
    loading.value = false;
  }
};





// 处理报名
const handleRegister = async () => {
  const activityId = activity.value.id || props.activityId;
  const targetUrl = `/activity-registration/${activityId}`;
  
  try {
    await router.push(targetUrl);
    emit('register', activity.value);
  } catch (error) {
    console.error('跳转失败:', error);
    window.location.hash = `#${targetUrl}`;
  }
};

// 监听活动ID变化
watch(() => props.activityId, (newId) => {
  if (newId) {
    fetchActivityDetail();
  }
}, { immediate: true });

// 暴露刷新方法
const refresh = () => {
  fetchActivityDetail();
};

defineExpose({
  refresh,
  activity
});
</script>

<style scoped>
.activity-detail-card {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding-bottom: 80px; /* 为页脚留出空间 */
  position: relative;
  width: 100%;
  max-width: 100vw;
  overflow-x: hidden;
  box-sizing: border-box;
}

/* 移除自定义导航栏样式，使用全局页头 */

/* 加载和错误状态 */
.loading-container, .error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  padding: 20px;
}

.loading-container p {
  margin-top: 16px;
  color: #666;
  font-size: 14px;
}

/* 主要内容区域 */
.content {
  padding-top: 20px;
}

/* 活动封面图 - 使用section-container样式 */
.activity-hero {
  position: relative;
  margin: 5px 5px 24px 5px;
  height: 240px;
  overflow: hidden;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.hero-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 16px;
}



/* 活动主题区域 - 使用section-container样式 */
.activity-theme-section {
  margin: 5px 5px 24px 5px;
  padding: 20px;
  width: auto;
  box-sizing: border-box;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  position: relative;
  z-index: 10;
}

.activity-title {
  font-size: 24px;
  font-weight: 700;
  color: #1a1a1a;
  margin: 0 0 8px 0;
  line-height: 1.3;
}

.activity-subtitle {
  font-size: 16px;
  color: #666;
  margin-bottom: 16px;
  line-height: 1.5;
}

/* 活动时间地点信息容器 - 使用section-container样式 */
.activity-info-container {
  margin: 5px 5px 24px 5px;
  padding: 20px;
  width: auto;
  box-sizing: border-box;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  padding: 4px 0;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 22px;
}

.info-icon.time-icon {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.info-icon.location-icon {
  background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
  color: white;
}

.info-content {
  flex: 1;
}

.info-label {
  font-size: 16px;
  color: #666;
  margin-bottom: 6px;
  font-weight: 500;
}

.info-value {
  font-size: 20px;
  color: #333;
  font-weight: 600;
  line-height: 1.4;
}

.content-sections {
  margin: 5px 5px 24px 5px; /* Consistent with other main sections */
}

.content-section {
  margin-bottom: 24px; /* Only bottom margin, fills parent width */
  padding: 5px;
  width: auto;
  box-sizing: border-box;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  padding: 16px 20px;
  background: #f8f9fa;
  border-bottom: 1px solid #eee;
}

.section-header h2 {
  margin: 0 0 0 8px;
  font-size: 18px;
  font-weight: 600;
  color: #333;
  text-align: left;
}

.section-content {
  padding: 2px;
}

.section-content p {
  margin: 0;
  line-height: 1.6;
  color: #666;
  font-size: 15px;
}

.rich-content {
  line-height: 1.8;
  color: #333;
}

.rich-content img {
  max-width: 100%;
  height: auto;
  border-radius: 8px;
  margin: 10px 0;
}

.rich-content p {
  margin-bottom: 12px;
}

.rich-content h1, .rich-content h2, .rich-content h3 {
  margin: 20px 0 12px 0;
  color: #333;
}

/* 报名按钮区域样式 - 使用section-container样式 */
.register-section {
  margin-bottom: 24px; /* Only bottom margin, fills parent width */
  padding: 20px;
  width: auto;
  box-sizing: border-box;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.register-btn {
  height: 48px;
  font-size: 16px;
  font-weight: 600;
  background: linear-gradient(135deg, #4b8bf4 0%, #667eea 100%);
  border: none;
  transition: all 0.3s ease;
}

.register-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(75, 139, 244, 0.4);
}

.register-btn:disabled {
  opacity: 0.6;
  transform: none;
}

/* 响应式优化 - 与活动页保持一致 */
@media (max-width: 374px) {
  .activity-title {
    font-size: 20px;
  }
  
  .activity-hero {
    height: 200px;
  }
  
  .activity-theme-section,
  .activity-info-container,
  .register-section,
  .content-section {
    padding: 12px;
  }
  
  .section-content {
    padding: 16px;
  }
}

@media (min-width: 375px) and (max-width: 413px) {
  .activity-theme-section,
  .activity-info-container,
  .register-section {
    padding: 16px;
  }
}

@media (min-width: 414px) and (max-width: 767px) {
  .activity-theme-section,
  .activity-info-container,
  .register-section {
    padding: 20px;
  }
}

/* 动画效果 - 与活动页保持一致 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.activity-hero {
  animation: fadeInUp 0.6s ease-out;
}

.activity-theme-section {
  animation: fadeInUp 0.6s ease-out 0.1s both;
}

.activity-info-container {
  animation: fadeInUp 0.6s ease-out 0.2s both;
}

.content-section {
  animation: fadeInUp 0.6s ease-out 0.3s both;
}

.register-section {
  animation: fadeInUp 0.6s ease-out 0.4s both;
}

/* 减少动画的用户偏好 */
@media (prefers-reduced-motion: reduce) {
  .activity-hero,
  .activity-theme-section,
  .activity-info-container,
  .content-section,
  .register-section {
    animation: none;
  }
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 4px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 2px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a1a1a1;
}
</style>
