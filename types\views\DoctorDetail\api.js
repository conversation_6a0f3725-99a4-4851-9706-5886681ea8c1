import { get, requestWithRetry } from '../../api/request';
import { FAMOUS_DOCTOR_URLS, buildUrl } from '../../api/urls';
/**
 * 获取领导详情
 * @param id 领导ID
 * @returns 领导详情数据
 */
export function getDoctorDetail(id) {
    return get(buildUrl(FAMOUS_DOCTOR_URLS.DOCTOR, id));
}
/**
 * 带重试功能的获取领导详情
 * 在网络不稳定情况下使用
 * @param id 领导ID
 * @returns 领导详情数据
 */
export function getDoctorDetailWithRetry(id) {
    return requestWithRetry(() => getDoctorDetail(id));
}
