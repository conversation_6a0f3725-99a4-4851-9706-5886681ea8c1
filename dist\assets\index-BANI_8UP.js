import{s as D,v as b,z as y,d as R,u as x,r as o,y as O,l as B,c as f,o as _,b as u,w as g,F as I,e as F,f as G,g as C,_ as L}from"./index-BE8XLQ59.js";import{T as S,a as z}from"./index-CmzFzWyS.js";import"./index-sbxbYdRt.js";import{G as E,a as M}from"./GlobalHeader-lL88u8sR.js";import{C as U}from"./CommonCardList-1yAjUs8b.js";import"./index-B27ddgxZ.js";import"./index-ouF_E0m0.js";import"./index-CAfqjps3.js";function V(){return b(y.CATEGORIES)}function A(r,n=1,t=6){return b(y.DOCTOR,{cat:r,page:n,page_size:t})}function N(r,n=1,t=6){return D(()=>A(r,n,t))}const W={class:"news-page category-tabs-container"},q={class:"section-container"},H={class:"news-grid"},j=R({__name:"index",setup(r){const n=x(),t=o(!1),i=o(!1),d=o([]),v=o(1),l=o(0),h=o(5),m=o([]),w=async()=>{const e=await V();console.log("获取到领导分类sdfsdfsd:",e);const s=e.categories.map(a=>({id:a.value,name:a.label}));m.value=[...s]};O(l,e=>{v.value=1,d.value=[],i.value=!1,p()});const p=async()=>{var s;if(i.value||t.value)return;t.value=!0;const e=(s=m.value[l.value])==null?void 0:s.id;console.log("当前选中标签ID:",e);try{const a=await N(e,v.value,h.value);d.value.push(...a.results),v.value+=1,i.value=a.is_last_page===!0}catch(a){console.error("加载失败:",a)}finally{t.value=!1}},T=e=>{n.push({name:"DoctorDetail",params:{id:e.id}})};return B(()=>{w(),p()}),(e,s)=>{const a=S,k=z;return _(),f("div",W,[u(E,{title:"名中医"}),u(k,{active:l.value,"onUpdate:active":s[0]||(s[0]=c=>l.value=c),class:"category-tabs",sticky:"","sticky-offset-top":0,swipeable:""},{default:g(()=>[(_(!0),f(I,null,F(m.value,c=>(_(),G(a,{key:c.id,title:c.name},{default:g(()=>[C("div",q,[C("div",H,[u(U,{items:d.value,loading:t.value,finished:i.value,"use-infinite-scroll":!0,onLoadMore:p,onCardClick:T},null,8,["items","loading","finished"])])])]),_:2},1032,["title"]))),128))]),_:1},8,["active"]),u(M)])}}}),ee=L(j,[["__scopeId","data-v-82786958"]]);export{ee as default};
