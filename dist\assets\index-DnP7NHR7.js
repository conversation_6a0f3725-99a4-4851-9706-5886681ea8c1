import{d as i,a as l,u as p,r as u,l as m,c as _,o as f,b as a,g as h,_ as b}from"./index-BE8XLQ59.js";import{G as v,a as x}from"./GlobalHeader-lL88u8sR.js";import{L as y}from"./LeaderDetailCard-VVs8AxZp.js";import{s}from"./function-call-BUl5915X.js";import"./index-CAfqjps3.js";import"./index-B27ddgxZ.js";const g={class:"education-detail-page"},L={class:"detail-content"},R=i({__name:"index",setup(k){const o=l(),r=p(),t=u("");m(()=>{const e=Array.isArray(o.params.id)?o.params.id[0]:o.params.id;e?t.value=e:(s("内容ID无效"),r.back())});const d=e=>{console.log("科教图文详情加载成功:",e)},n=e=>{console.error("科教图文详情加载失败:",e),s(e)},c=()=>{console.log("重试加载科教图文详情")};return(e,B)=>(f(),_("div",g,[a(v,{title:"科教图文详情"}),h("div",L,[a(y,{"leader-id":t.value,"api-type":"education",onLoaded:d,onError:n,onRetry:c},null,8,["leader-id"])]),a(x)]))}}),N=b(R,[["__scopeId","data-v-2905bfd2"]]);export{N as default};
