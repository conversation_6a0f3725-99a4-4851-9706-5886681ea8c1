@echo off
chcp 65001 >nul
echo.
echo ================================================
echo 中医智慧前端项目快速部署脚本
echo ================================================
echo.

:: 检查Node.js
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未安装Node.js，请先安装Node.js 18+
    pause
    exit /b 1
)

:: 检查npm
npm --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未安装npm
    pause
    exit /b 1
)

echo ✅ Node.js环境检查通过
echo.

:: 安装依赖
echo 📦 安装项目依赖...
call npm ci
if errorlevel 1 (
    echo ❌ 依赖安装失败
    pause
    exit /b 1
)

echo ✅ 依赖安装成功
echo.

:: 构建项目
echo 🔨 构建生产环境代码...
call npm run build
if errorlevel 1 (
    echo ❌ 构建失败
    pause
    exit /b 1
)

echo ✅ 构建成功
echo.

:: 检查dist目录
if not exist "dist" (
    echo ❌ 错误: 构建失败，未找到dist目录
    pause
    exit /b 1
)

echo 🎉 部署准备完成！
echo.
echo 📁 生产文件位于: dist\ 目录
echo.
echo 部署选项:
echo 1. 复制dist目录内容到您的Web服务器
echo 2. 使用Docker: docker build -t zyyfront .
echo 3. 本地预览: npm run preview
echo.
echo 💡 详细部署说明请查看 DEPLOYMENT.md 文件
echo.

pause 