import { get, requestWithRetry } from '../../api/request';
import { DEPT_URLS } from '../../api/urls';
/**
 * 获取分类
 * @returns 分类数据
 */
export function getDeptCategories() {
    return get(DEPT_URLS.CATEGORIES);
}
/**
 * 根据分类获取列表
 * @param cat 分类ID
 * @param page 页码
 * @param pageSize 每页数量
 * @returns 列表数据
 */
export function getDeptByCategory(cat, page = 1, pageSize = 10) {
    return get(DEPT_URLS.DEPT, {
        cat,
        page,
        page_size: pageSize
    });
}
/**
 * 带重试功能的获取分类
 * 在网络不稳定情况下使用
 * @returns 分类数据
 */
export function getDeptCategoriesWithRetry(cat, page = 1, pageSize = 10) {
    return requestWithRetry(() => getDeptByCategory(cat, page, pageSize));
}
