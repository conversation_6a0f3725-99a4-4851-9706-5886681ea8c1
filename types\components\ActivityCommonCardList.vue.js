import { ref, watch, onMounted, nextTick } from 'vue';
import { formatActivityTimeRange, formatDeadline } from '../utils/dateTime';
const scrollContainerRef = ref(null);
const getScrollContainer = () => scrollContainerRef.value;
onMounted(() => {
    nextTick(() => {
        console.log('Activity滚动容器:', scrollContainerRef.value);
    });
});
const props = withDefaults(defineProps(), {
    emptyText: '暂无活动',
    useInfiniteScroll: true,
    finished: false,
});
const emit = defineEmits(['load-more', 'card-click', 'share', 'register']);
const internalLoading = ref(false);
watch(() => props.loading, (val) => {
    internalLoading.value = val;
}, { immediate: true });
const onLoadMore = () => {
    console.log('触发加载更多活动');
    emit('load-more');
};
// 获取活动状态
const getActivityStatus = (item) => {
    // 如果有明确的状态字段，优先使用
    if (item.status) {
        return item.status;
    }
    // 对于"正在报名中的活动"列表，默认状态应该是报名中
    // 我们可以通过其他字段来判断状态
    const now = new Date();
    // 检查报名截止时间
    const registrationDeadline = item.registration_deadline ? new Date(item.registration_deadline) : null;
    // 如果有报名截止时间且已过期，仍然显示为报名中（因为这是报名中的活动列表）
    if (registrationDeadline && now > registrationDeadline) {
        return '报名中';
    }
    // 检查活动时间
    const startDate = item.startdate ? new Date(item.startdate) : null;
    const endDate = item.enddate ? new Date(item.enddate) : null;
    if (startDate && endDate) {
        if (now < startDate) {
            return '报名中'; // 活动还没开始，处于报名阶段
        }
        else if (now >= startDate && now <= endDate) {
            return '进行中'; // 活动正在进行
        }
        else if (now > endDate) {
            // 即使活动结束了，但如果在"正在报名中"列表里，仍然显示为报名中
            return '报名中';
        }
    }
    // 默认状态为报名中
    return '报名中';
};
// 获取状态标签类型
const getStatusTagType = (item) => {
    const status = getActivityStatus(item);
    switch (status) {
        case '进行中':
            return 'success';
        case '即将开始':
            return 'warning';
        case '已结束':
            return 'default';
        case '报名中':
            return 'primary';
        default:
            return 'primary';
    }
};
// 格式化活动时间 - 使用统一的时间格式化工具
const formatActivityTime = (item) => {
    if (item.startdate && item.enddate) {
        return formatActivityTimeRange(item.startdate, item.enddate);
    }
    else if (item.start_time && item.end_time) {
        return formatActivityTimeRange(item.start_time, item.end_time);
    }
    else if (item.startdate) {
        return formatActivityTimeRange(item.startdate, '');
    }
    else if (item.start_time) {
        return formatActivityTimeRange(item.start_time, '');
    }
    return '时间待定';
};
// 格式化参与人数
const formatParticipantCount = (item) => {
    if (item.current_participants !== undefined && item.max_participants !== undefined) {
        return `${item.current_participants}/${item.max_participants}人`;
    }
    else if (item.participant_count !== undefined) {
        return `${item.participant_count}人已报名`;
    }
    else if (item.nums !== undefined) {
        return `${item.nums}人已报名`;
    }
    else if (item.max_participants !== undefined) {
        return `限${item.max_participants}人`;
    }
    return '暂无数据';
};
// 格式化活动费用
const formatActivityFee = (item) => {
    if (item.isfree === '免费' || item.price === 0) {
        return '免费';
    }
    else if (item.price !== undefined) {
        return `¥${item.price}`;
    }
    else if (item.isfree) {
        return item.isfree;
    }
    return '免费'; // 默认静态数据
};
// 获取活动形式图标
const getActivityFormIcon = (item) => {
    const form = item.activity_type || item.activity_form || item.form_type || '';
    switch (form) {
        case '在线直播':
        case 'online_live':
            return 'play-circle-o';
        case '线下组织':
        case 'offline_organize':
            return 'shop-o';
        case '在线答题':
        case 'online_quiz':
            return 'edit';
        case '线上活动':
        case 'online_activity':
            return 'desktop-o';
        case '混合活动':
        case 'hybrid_activity':
            return 'exchange';
        default:
            return 'medal-o';
    }
};
// 获取活动形式样式类
const getActivityFormClass = (item) => {
    const form = item.activity_type || item.activity_form || item.form_type || '';
    switch (form) {
        case '在线直播':
        case 'online_live':
            return 'form-live';
        case '线下组织':
        case 'offline_organize':
            return 'form-offline';
        case '在线答题':
        case 'online_quiz':
            return 'form-quiz';
        case '线上活动':
        case 'online_activity':
            return 'form-online';
        case '混合活动':
        case 'hybrid_activity':
            return 'form-hybrid';
        default:
            return 'form-default';
    }
};
// 格式化活动形式
const formatActivityForm = (item) => {
    const form = item.activity_type || item.activity_form || item.form_type || '';
    // 如果是英文代码，转换为中文
    const formMap = {
        'online_live': '在线直播',
        'offline_organize': '线下组织',
        'online_quiz': '在线答题',
        'online_activity': '线上活动',
        'hybrid_activity': '混合活动'
    };
    return formMap[form] || form || '暂无数据';
};
// 判断活动数据是否完整
const isActivityDataComplete = (item) => {
    return item && item.name && item.name.trim() !== '';
};
// 处理转发按钮点击
const handleShare = (item) => {
    console.log('=== ActivityCommonCardList 分享点击 ===');
    console.log('点击的活动项:', item);
    console.log('当前组件的items数据:', props.items);
    // 数据验证：确保活动数据完整
    if (!isActivityDataComplete(item)) {
        console.warn('分享失败：活动数据不完整', {
            item: item,
            hasName: !!item?.name,
            nameValue: item?.name,
            nameType: typeof item?.name
        });
        return;
    }
    console.log('数据验证通过，触发分享事件:', item.name);
    emit('share', item);
};
// 处理报名按钮点击
const handleRegister = (item) => {
    console.log('报名活动:', item.name);
    emit('register', item);
};
// 判断是否可以报名
const canRegister = (item) => {
    const status = getActivityStatus(item);
    // 对于"正在报名中的活动"列表，大部分情况下都应该可以报名
    // 只有在人数已满的情况下才不能报名
    if (item.current_participants && item.max_participants &&
        item.current_participants >= item.max_participants) {
        return false;
    }
    // 其他情况都可以报名
    return true;
};
// 获取报名按钮类型
const getRegisterButtonType = (item) => {
    if (!canRegister(item)) {
        return 'default';
    }
    const status = getActivityStatus(item);
    // 对于"正在报名中的活动"列表，主要使用primary类型
    if (status === '报名中') {
        return 'primary';
    }
    else if (status === '进行中') {
        return 'success';
    }
    // 默认使用primary类型
    return 'primary';
};
// 获取报名按钮图标
const getRegisterButtonIcon = (item) => {
    if (!canRegister(item)) {
        return 'cross';
    }
    const status = getActivityStatus(item);
    // 对于"正在报名中的活动"列表，主要使用报名相关图标
    if (status === '报名中') {
        return 'add-o';
    }
    else if (status === '进行中') {
        return 'play-circle-o';
    }
    // 默认使用报名图标
    return 'add-o';
};
// 获取报名按钮文本
const getRegisterButtonText = (item) => {
    if (!canRegister(item)) {
        // 只有在报名满员的情况下才显示无法报名
        if (item.current_participants && item.max_participants &&
            item.current_participants >= item.max_participants) {
            return '报名满员';
        }
        return '无法报名';
    }
    const status = getActivityStatus(item);
    // 对于"正在报名中的活动"列表，主要显示报名相关的文本
    if (status === '报名中') {
        return '立即报名';
    }
    else if (status === '进行中') {
        return '正在进行';
    }
    // 默认显示立即报名
    return '立即报名';
};
// 格式化活动地点
const formatActivityLocation = (item) => {
    if (item.location) {
        return item.location;
    }
    else if (item.address) {
        return item.address;
    }
    return '暂无数据';
};
// 格式化报名截止时间 - 使用统一的时间格式化工具
const formatRegistrationDeadline = (item) => {
    if (item.dieline) {
        return formatDeadline(item.dieline);
    }
    else if (item.registration_deadline) {
        return formatDeadline(item.registration_deadline);
    }
    else if (item.deadline) {
        return formatDeadline(item.deadline);
    }
    return '暂无数据';
};
// 格式化活动主办方
const formatOrganizer = (item) => {
    if (item.leader) {
        return item.leader;
    }
    else if (item.organizer) {
        return item.organizer;
    }
    return '暂无数据';
};
// 格式化联系方式
const formatContact = (item) => {
    if (item.contact) {
        return item.contact;
    }
    return '暂无数据';
};
debugger; /* PartiallyEnd: #3632/scriptSetup.vue */
const __VLS_withDefaultsArg = (function (t) { return t; })({
    emptyText: '暂无活动',
    useInfiniteScroll: true,
    finished: false,
});
const __VLS_ctx = {};
let __VLS_components;
let __VLS_directives;
/** @type {__VLS_StyleScopedClasses['activity-card']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-card']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-image']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-item']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-heat']} */ ;
/** @type {__VLS_StyleScopedClasses['van-icon']} */ ;
/** @type {__VLS_StyleScopedClasses['share-button']} */ ;
/** @type {__VLS_StyleScopedClasses['register-button']} */ ;
/** @type {__VLS_StyleScopedClasses['register-button']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-image']} */ ;
/** @type {__VLS_StyleScopedClasses['card-content']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-title']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-description']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-item']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-item']} */ ;
/** @type {__VLS_StyleScopedClasses['van-icon']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-label']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-value']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-tags']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-tag']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-actions']} */ ;
/** @type {__VLS_StyleScopedClasses['action-button']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-card']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-image']} */ ;
/** @type {__VLS_StyleScopedClasses['animate__fadeInUp']} */ ;
// CSS variable injection 
// CSS variable injection end 
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ class: "activity-common-card-list" },
    ref: "scrollContainerRef",
});
/** @type {typeof __VLS_ctx.scrollContainerRef} */ ;
if (__VLS_ctx.loading && !__VLS_ctx.items.length) {
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
        ...{ class: "loading-container" },
    });
    const __VLS_0 = {}.VanLoading;
    /** @type {[typeof __VLS_components.VanLoading, typeof __VLS_components.vanLoading, typeof __VLS_components.VanLoading, typeof __VLS_components.vanLoading, ]} */ ;
    // @ts-ignore
    const __VLS_1 = __VLS_asFunctionalComponent(__VLS_0, new __VLS_0({
        size: "24px",
    }));
    const __VLS_2 = __VLS_1({
        size: "24px",
    }, ...__VLS_functionalComponentArgsRest(__VLS_1));
    __VLS_3.slots.default;
    var __VLS_3;
}
else if (!__VLS_ctx.items.length) {
    const __VLS_4 = {}.VanEmpty;
    /** @type {[typeof __VLS_components.VanEmpty, typeof __VLS_components.vanEmpty, ]} */ ;
    // @ts-ignore
    const __VLS_5 = __VLS_asFunctionalComponent(__VLS_4, new __VLS_4({
        description: (__VLS_ctx.emptyText),
    }));
    const __VLS_6 = __VLS_5({
        description: (__VLS_ctx.emptyText),
    }, ...__VLS_functionalComponentArgsRest(__VLS_5));
}
else if (__VLS_ctx.useInfiniteScroll) {
    const __VLS_8 = {}.VanList;
    /** @type {[typeof __VLS_components.VanList, typeof __VLS_components.vanList, typeof __VLS_components.VanList, typeof __VLS_components.vanList, ]} */ ;
    // @ts-ignore
    const __VLS_9 = __VLS_asFunctionalComponent(__VLS_8, new __VLS_8({
        ...{ 'onLoad': {} },
        loading: (__VLS_ctx.internalLoading),
        finished: (__VLS_ctx.finished),
        finishedText: "没有更多了",
        immediateCheck: (false),
        scrollContainer: (__VLS_ctx.getScrollContainer),
    }));
    const __VLS_10 = __VLS_9({
        ...{ 'onLoad': {} },
        loading: (__VLS_ctx.internalLoading),
        finished: (__VLS_ctx.finished),
        finishedText: "没有更多了",
        immediateCheck: (false),
        scrollContainer: (__VLS_ctx.getScrollContainer),
    }, ...__VLS_functionalComponentArgsRest(__VLS_9));
    let __VLS_12;
    let __VLS_13;
    let __VLS_14;
    const __VLS_15 = {
        onLoad: (__VLS_ctx.onLoadMore)
    };
    __VLS_11.slots.default;
    for (const [item] of __VLS_getVForSourceType((__VLS_ctx.items))) {
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ onClick: (...[$event]) => {
                    if (!!(__VLS_ctx.loading && !__VLS_ctx.items.length))
                        return;
                    if (!!(!__VLS_ctx.items.length))
                        return;
                    if (!(__VLS_ctx.useInfiniteScroll))
                        return;
                    __VLS_ctx.$emit('card-click', item);
                } },
            key: (item.id),
            ...{ class: "activity-card animate__animated animate__fadeInUp" },
        });
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "card-image-container" },
        });
        if (__VLS_ctx.getActivityStatus(item)) {
            const __VLS_16 = {}.VanTag;
            /** @type {[typeof __VLS_components.VanTag, typeof __VLS_components.vanTag, typeof __VLS_components.VanTag, typeof __VLS_components.vanTag, ]} */ ;
            // @ts-ignore
            const __VLS_17 = __VLS_asFunctionalComponent(__VLS_16, new __VLS_16({
                type: (__VLS_ctx.getStatusTagType(item)),
                size: "medium",
                ...{ class: "activity-status-tag" },
                round: true,
            }));
            const __VLS_18 = __VLS_17({
                type: (__VLS_ctx.getStatusTagType(item)),
                size: "medium",
                ...{ class: "activity-status-tag" },
                round: true,
            }, ...__VLS_functionalComponentArgsRest(__VLS_17));
            __VLS_19.slots.default;
            (__VLS_ctx.getActivityStatus(item));
            var __VLS_19;
        }
        if (item.activity_type || item.cat_name) {
            const __VLS_20 = {}.VanTag;
            /** @type {[typeof __VLS_components.VanTag, typeof __VLS_components.vanTag, typeof __VLS_components.VanTag, typeof __VLS_components.vanTag, ]} */ ;
            // @ts-ignore
            const __VLS_21 = __VLS_asFunctionalComponent(__VLS_20, new __VLS_20({
                type: "primary",
                size: "medium",
                ...{ class: "activity-type-tag" },
                round: true,
            }));
            const __VLS_22 = __VLS_21({
                type: "primary",
                size: "medium",
                ...{ class: "activity-type-tag" },
                round: true,
            }, ...__VLS_functionalComponentArgsRest(__VLS_21));
            __VLS_23.slots.default;
            (item.activity_type || item.cat_name || '活动');
            var __VLS_23;
        }
        __VLS_asFunctionalElement(__VLS_intrinsicElements.img)({
            src: (item.thumbnail),
            alt: (item.name),
            ...{ class: "activity-image" },
        });
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "image-overlay" },
        });
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "card-content" },
        });
        __VLS_asFunctionalElement(__VLS_intrinsicElements.h3, __VLS_intrinsicElements.h3)({
            ...{ class: "activity-title" },
        });
        (item.name);
        __VLS_asFunctionalElement(__VLS_intrinsicElements.p, __VLS_intrinsicElements.p)({
            ...{ class: "activity-description" },
        });
        (item.desc || item.description);
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "activity-details" },
        });
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "detail-item" },
        });
        const __VLS_24 = {}.VanIcon;
        /** @type {[typeof __VLS_components.VanIcon, typeof __VLS_components.vanIcon, ]} */ ;
        // @ts-ignore
        const __VLS_25 = __VLS_asFunctionalComponent(__VLS_24, new __VLS_24({
            name: "clock-o",
        }));
        const __VLS_26 = __VLS_25({
            name: "clock-o",
        }, ...__VLS_functionalComponentArgsRest(__VLS_25));
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "detail-content" },
        });
        __VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({
            ...{ class: "detail-label" },
        });
        __VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({
            ...{ class: "detail-value" },
        });
        (__VLS_ctx.formatActivityTime(item));
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "detail-item" },
        });
        const __VLS_28 = {}.VanIcon;
        /** @type {[typeof __VLS_components.VanIcon, typeof __VLS_components.vanIcon, ]} */ ;
        // @ts-ignore
        const __VLS_29 = __VLS_asFunctionalComponent(__VLS_28, new __VLS_28({
            name: "location-o",
        }));
        const __VLS_30 = __VLS_29({
            name: "location-o",
        }, ...__VLS_functionalComponentArgsRest(__VLS_29));
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "detail-content" },
        });
        __VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({
            ...{ class: "detail-label" },
        });
        __VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({
            ...{ class: "detail-value" },
        });
        (__VLS_ctx.formatActivityLocation(item));
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "detail-item" },
        });
        const __VLS_32 = {}.VanIcon;
        /** @type {[typeof __VLS_components.VanIcon, typeof __VLS_components.vanIcon, ]} */ ;
        // @ts-ignore
        const __VLS_33 = __VLS_asFunctionalComponent(__VLS_32, new __VLS_32({
            name: "friends-o",
        }));
        const __VLS_34 = __VLS_33({
            name: "friends-o",
        }, ...__VLS_functionalComponentArgsRest(__VLS_33));
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "detail-content" },
        });
        __VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({
            ...{ class: "detail-label" },
        });
        __VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({
            ...{ class: "detail-value" },
        });
        (__VLS_ctx.formatParticipantCount(item));
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "detail-item" },
        });
        const __VLS_36 = {}.VanIcon;
        /** @type {[typeof __VLS_components.VanIcon, typeof __VLS_components.vanIcon, ]} */ ;
        // @ts-ignore
        const __VLS_37 = __VLS_asFunctionalComponent(__VLS_36, new __VLS_36({
            name: (__VLS_ctx.getActivityFormIcon(item)),
        }));
        const __VLS_38 = __VLS_37({
            name: (__VLS_ctx.getActivityFormIcon(item)),
        }, ...__VLS_functionalComponentArgsRest(__VLS_37));
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "detail-content" },
        });
        __VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({
            ...{ class: "detail-label" },
        });
        __VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({
            ...{ class: "detail-value activity-form" },
            ...{ class: (__VLS_ctx.getActivityFormClass(item)) },
        });
        (__VLS_ctx.formatActivityForm(item));
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "detail-item" },
        });
        const __VLS_40 = {}.VanIcon;
        /** @type {[typeof __VLS_components.VanIcon, typeof __VLS_components.vanIcon, ]} */ ;
        // @ts-ignore
        const __VLS_41 = __VLS_asFunctionalComponent(__VLS_40, new __VLS_40({
            name: "gold-coin-o",
        }));
        const __VLS_42 = __VLS_41({
            name: "gold-coin-o",
        }, ...__VLS_functionalComponentArgsRest(__VLS_41));
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "detail-content" },
        });
        __VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({
            ...{ class: "detail-label" },
        });
        __VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({
            ...{ class: "detail-value" },
            ...{ class: ({ 'free-activity': item.isfree === '免费' || item.price === 0 }) },
        });
        (__VLS_ctx.formatActivityFee(item));
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "detail-item" },
        });
        const __VLS_44 = {}.VanIcon;
        /** @type {[typeof __VLS_components.VanIcon, typeof __VLS_components.vanIcon, ]} */ ;
        // @ts-ignore
        const __VLS_45 = __VLS_asFunctionalComponent(__VLS_44, new __VLS_44({
            name: "calendar-o",
        }));
        const __VLS_46 = __VLS_45({
            name: "calendar-o",
        }, ...__VLS_functionalComponentArgsRest(__VLS_45));
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "detail-content" },
        });
        __VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({
            ...{ class: "detail-label" },
        });
        __VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({
            ...{ class: "detail-value" },
        });
        (__VLS_ctx.formatRegistrationDeadline(item));
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "detail-item" },
        });
        const __VLS_48 = {}.VanIcon;
        /** @type {[typeof __VLS_components.VanIcon, typeof __VLS_components.vanIcon, ]} */ ;
        // @ts-ignore
        const __VLS_49 = __VLS_asFunctionalComponent(__VLS_48, new __VLS_48({
            name: "manager-o",
        }));
        const __VLS_50 = __VLS_49({
            name: "manager-o",
        }, ...__VLS_functionalComponentArgsRest(__VLS_49));
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "detail-content" },
        });
        __VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({
            ...{ class: "detail-label" },
        });
        __VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({
            ...{ class: "detail-value" },
        });
        (__VLS_ctx.formatOrganizer(item));
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "detail-item" },
        });
        const __VLS_52 = {}.VanIcon;
        /** @type {[typeof __VLS_components.VanIcon, typeof __VLS_components.vanIcon, ]} */ ;
        // @ts-ignore
        const __VLS_53 = __VLS_asFunctionalComponent(__VLS_52, new __VLS_52({
            name: "phone-o",
        }));
        const __VLS_54 = __VLS_53({
            name: "phone-o",
        }, ...__VLS_functionalComponentArgsRest(__VLS_53));
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "detail-content" },
        });
        __VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({
            ...{ class: "detail-label" },
        });
        __VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({
            ...{ class: "detail-value" },
        });
        (__VLS_ctx.formatContact(item));
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "activity-footer" },
        });
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "footer-left" },
        });
        if (item.tags && item.tags.length) {
            __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
                ...{ class: "activity-tags" },
            });
            for (const [tag, index] of __VLS_getVForSourceType((item.tags))) {
                __VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({
                    key: (index),
                    ...{ class: "activity-tag" },
                });
                (tag);
            }
        }
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "footer-right" },
        });
        if (item.views || item.likes) {
            __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
                ...{ class: "activity-heat" },
            });
            const __VLS_56 = {}.VanIcon;
            /** @type {[typeof __VLS_components.VanIcon, typeof __VLS_components.vanIcon, ]} */ ;
            // @ts-ignore
            const __VLS_57 = __VLS_asFunctionalComponent(__VLS_56, new __VLS_56({
                name: "fire-o",
            }));
            const __VLS_58 = __VLS_57({
                name: "fire-o",
            }, ...__VLS_functionalComponentArgsRest(__VLS_57));
            __VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({});
            (item.views || item.likes || 0);
        }
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "activity-actions" },
        });
        const __VLS_60 = {}.VanButton;
        /** @type {[typeof __VLS_components.VanButton, typeof __VLS_components.vanButton, typeof __VLS_components.VanButton, typeof __VLS_components.vanButton, ]} */ ;
        // @ts-ignore
        const __VLS_61 = __VLS_asFunctionalComponent(__VLS_60, new __VLS_60({
            ...{ 'onClick': {} },
            type: "default",
            size: "small",
            icon: "share-o",
            ...{ class: "action-button share-button" },
            disabled: (!item || !item.name || item.name.trim() === ''),
        }));
        const __VLS_62 = __VLS_61({
            ...{ 'onClick': {} },
            type: "default",
            size: "small",
            icon: "share-o",
            ...{ class: "action-button share-button" },
            disabled: (!item || !item.name || item.name.trim() === ''),
        }, ...__VLS_functionalComponentArgsRest(__VLS_61));
        let __VLS_64;
        let __VLS_65;
        let __VLS_66;
        const __VLS_67 = {
            onClick: (...[$event]) => {
                if (!!(__VLS_ctx.loading && !__VLS_ctx.items.length))
                    return;
                if (!!(!__VLS_ctx.items.length))
                    return;
                if (!(__VLS_ctx.useInfiniteScroll))
                    return;
                __VLS_ctx.handleShare(item);
            }
        };
        __VLS_63.slots.default;
        var __VLS_63;
        const __VLS_68 = {}.VanButton;
        /** @type {[typeof __VLS_components.VanButton, typeof __VLS_components.vanButton, typeof __VLS_components.VanButton, typeof __VLS_components.vanButton, ]} */ ;
        // @ts-ignore
        const __VLS_69 = __VLS_asFunctionalComponent(__VLS_68, new __VLS_68({
            ...{ 'onClick': {} },
            type: (__VLS_ctx.getRegisterButtonType(item)),
            size: "small",
            icon: (__VLS_ctx.getRegisterButtonIcon(item)),
            ...{ class: "action-button register-button" },
            disabled: (!__VLS_ctx.canRegister(item)),
        }));
        const __VLS_70 = __VLS_69({
            ...{ 'onClick': {} },
            type: (__VLS_ctx.getRegisterButtonType(item)),
            size: "small",
            icon: (__VLS_ctx.getRegisterButtonIcon(item)),
            ...{ class: "action-button register-button" },
            disabled: (!__VLS_ctx.canRegister(item)),
        }, ...__VLS_functionalComponentArgsRest(__VLS_69));
        let __VLS_72;
        let __VLS_73;
        let __VLS_74;
        const __VLS_75 = {
            onClick: (...[$event]) => {
                if (!!(__VLS_ctx.loading && !__VLS_ctx.items.length))
                    return;
                if (!!(!__VLS_ctx.items.length))
                    return;
                if (!(__VLS_ctx.useInfiniteScroll))
                    return;
                __VLS_ctx.handleRegister(item);
            }
        };
        __VLS_71.slots.default;
        (__VLS_ctx.getRegisterButtonText(item));
        var __VLS_71;
    }
    var __VLS_11;
}
else {
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({});
    for (const [item] of __VLS_getVForSourceType((__VLS_ctx.items))) {
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ onClick: (...[$event]) => {
                    if (!!(__VLS_ctx.loading && !__VLS_ctx.items.length))
                        return;
                    if (!!(!__VLS_ctx.items.length))
                        return;
                    if (!!(__VLS_ctx.useInfiniteScroll))
                        return;
                    __VLS_ctx.$emit('card-click', item);
                } },
            key: (item.id),
            ...{ class: "activity-card animate__animated animate__fadeInUp" },
        });
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "card-image-container" },
        });
        if (__VLS_ctx.getActivityStatus(item)) {
            const __VLS_76 = {}.VanTag;
            /** @type {[typeof __VLS_components.VanTag, typeof __VLS_components.vanTag, typeof __VLS_components.VanTag, typeof __VLS_components.vanTag, ]} */ ;
            // @ts-ignore
            const __VLS_77 = __VLS_asFunctionalComponent(__VLS_76, new __VLS_76({
                type: (__VLS_ctx.getStatusTagType(item)),
                size: "medium",
                ...{ class: "activity-status-tag" },
                round: true,
            }));
            const __VLS_78 = __VLS_77({
                type: (__VLS_ctx.getStatusTagType(item)),
                size: "medium",
                ...{ class: "activity-status-tag" },
                round: true,
            }, ...__VLS_functionalComponentArgsRest(__VLS_77));
            __VLS_79.slots.default;
            (__VLS_ctx.getActivityStatus(item));
            var __VLS_79;
        }
        if (item.activity_type || item.cat_name) {
            const __VLS_80 = {}.VanTag;
            /** @type {[typeof __VLS_components.VanTag, typeof __VLS_components.vanTag, typeof __VLS_components.VanTag, typeof __VLS_components.vanTag, ]} */ ;
            // @ts-ignore
            const __VLS_81 = __VLS_asFunctionalComponent(__VLS_80, new __VLS_80({
                type: "primary",
                size: "medium",
                ...{ class: "activity-type-tag" },
                round: true,
            }));
            const __VLS_82 = __VLS_81({
                type: "primary",
                size: "medium",
                ...{ class: "activity-type-tag" },
                round: true,
            }, ...__VLS_functionalComponentArgsRest(__VLS_81));
            __VLS_83.slots.default;
            (item.activity_type || item.cat_name || '活动');
            var __VLS_83;
        }
        __VLS_asFunctionalElement(__VLS_intrinsicElements.img)({
            src: (item.thumbnail),
            alt: (item.name),
            ...{ class: "activity-image" },
        });
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "image-overlay" },
        });
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "card-content" },
        });
        __VLS_asFunctionalElement(__VLS_intrinsicElements.h3, __VLS_intrinsicElements.h3)({
            ...{ class: "activity-title" },
        });
        (item.name);
        __VLS_asFunctionalElement(__VLS_intrinsicElements.p, __VLS_intrinsicElements.p)({
            ...{ class: "activity-description" },
        });
        (item.desc || item.description);
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "activity-details" },
        });
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "detail-item" },
        });
        const __VLS_84 = {}.VanIcon;
        /** @type {[typeof __VLS_components.VanIcon, typeof __VLS_components.vanIcon, ]} */ ;
        // @ts-ignore
        const __VLS_85 = __VLS_asFunctionalComponent(__VLS_84, new __VLS_84({
            name: "clock-o",
        }));
        const __VLS_86 = __VLS_85({
            name: "clock-o",
        }, ...__VLS_functionalComponentArgsRest(__VLS_85));
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "detail-content" },
        });
        __VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({
            ...{ class: "detail-label" },
        });
        __VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({
            ...{ class: "detail-value" },
        });
        (__VLS_ctx.formatActivityTime(item));
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "detail-item" },
        });
        const __VLS_88 = {}.VanIcon;
        /** @type {[typeof __VLS_components.VanIcon, typeof __VLS_components.vanIcon, ]} */ ;
        // @ts-ignore
        const __VLS_89 = __VLS_asFunctionalComponent(__VLS_88, new __VLS_88({
            name: "location-o",
        }));
        const __VLS_90 = __VLS_89({
            name: "location-o",
        }, ...__VLS_functionalComponentArgsRest(__VLS_89));
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "detail-content" },
        });
        __VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({
            ...{ class: "detail-label" },
        });
        __VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({
            ...{ class: "detail-value" },
        });
        (__VLS_ctx.formatActivityLocation(item));
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "detail-item" },
        });
        const __VLS_92 = {}.VanIcon;
        /** @type {[typeof __VLS_components.VanIcon, typeof __VLS_components.vanIcon, ]} */ ;
        // @ts-ignore
        const __VLS_93 = __VLS_asFunctionalComponent(__VLS_92, new __VLS_92({
            name: "friends-o",
        }));
        const __VLS_94 = __VLS_93({
            name: "friends-o",
        }, ...__VLS_functionalComponentArgsRest(__VLS_93));
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "detail-content" },
        });
        __VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({
            ...{ class: "detail-label" },
        });
        __VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({
            ...{ class: "detail-value" },
        });
        (__VLS_ctx.formatParticipantCount(item));
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "detail-item" },
        });
        const __VLS_96 = {}.VanIcon;
        /** @type {[typeof __VLS_components.VanIcon, typeof __VLS_components.vanIcon, ]} */ ;
        // @ts-ignore
        const __VLS_97 = __VLS_asFunctionalComponent(__VLS_96, new __VLS_96({
            name: (__VLS_ctx.getActivityFormIcon(item)),
        }));
        const __VLS_98 = __VLS_97({
            name: (__VLS_ctx.getActivityFormIcon(item)),
        }, ...__VLS_functionalComponentArgsRest(__VLS_97));
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "detail-content" },
        });
        __VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({
            ...{ class: "detail-label" },
        });
        __VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({
            ...{ class: "detail-value activity-form" },
            ...{ class: (__VLS_ctx.getActivityFormClass(item)) },
        });
        (__VLS_ctx.formatActivityForm(item));
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "detail-item" },
        });
        const __VLS_100 = {}.VanIcon;
        /** @type {[typeof __VLS_components.VanIcon, typeof __VLS_components.vanIcon, ]} */ ;
        // @ts-ignore
        const __VLS_101 = __VLS_asFunctionalComponent(__VLS_100, new __VLS_100({
            name: "gold-coin-o",
        }));
        const __VLS_102 = __VLS_101({
            name: "gold-coin-o",
        }, ...__VLS_functionalComponentArgsRest(__VLS_101));
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "detail-content" },
        });
        __VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({
            ...{ class: "detail-label" },
        });
        __VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({
            ...{ class: "detail-value" },
            ...{ class: ({ 'free-activity': item.isfree === '免费' || item.price === 0 }) },
        });
        (__VLS_ctx.formatActivityFee(item));
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "detail-item" },
        });
        const __VLS_104 = {}.VanIcon;
        /** @type {[typeof __VLS_components.VanIcon, typeof __VLS_components.vanIcon, ]} */ ;
        // @ts-ignore
        const __VLS_105 = __VLS_asFunctionalComponent(__VLS_104, new __VLS_104({
            name: "calendar-o",
        }));
        const __VLS_106 = __VLS_105({
            name: "calendar-o",
        }, ...__VLS_functionalComponentArgsRest(__VLS_105));
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "detail-content" },
        });
        __VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({
            ...{ class: "detail-label" },
        });
        __VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({
            ...{ class: "detail-value" },
        });
        (__VLS_ctx.formatRegistrationDeadline(item));
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "detail-item" },
        });
        const __VLS_108 = {}.VanIcon;
        /** @type {[typeof __VLS_components.VanIcon, typeof __VLS_components.vanIcon, ]} */ ;
        // @ts-ignore
        const __VLS_109 = __VLS_asFunctionalComponent(__VLS_108, new __VLS_108({
            name: "manager-o",
        }));
        const __VLS_110 = __VLS_109({
            name: "manager-o",
        }, ...__VLS_functionalComponentArgsRest(__VLS_109));
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "detail-content" },
        });
        __VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({
            ...{ class: "detail-label" },
        });
        __VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({
            ...{ class: "detail-value" },
        });
        (__VLS_ctx.formatOrganizer(item));
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "detail-item" },
        });
        const __VLS_112 = {}.VanIcon;
        /** @type {[typeof __VLS_components.VanIcon, typeof __VLS_components.vanIcon, ]} */ ;
        // @ts-ignore
        const __VLS_113 = __VLS_asFunctionalComponent(__VLS_112, new __VLS_112({
            name: "phone-o",
        }));
        const __VLS_114 = __VLS_113({
            name: "phone-o",
        }, ...__VLS_functionalComponentArgsRest(__VLS_113));
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "detail-content" },
        });
        __VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({
            ...{ class: "detail-label" },
        });
        __VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({
            ...{ class: "detail-value" },
        });
        (__VLS_ctx.formatContact(item));
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "activity-footer" },
        });
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "footer-left" },
        });
        if (item.tags && item.tags.length) {
            __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
                ...{ class: "activity-tags" },
            });
            for (const [tag, index] of __VLS_getVForSourceType((item.tags))) {
                __VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({
                    key: (index),
                    ...{ class: "activity-tag" },
                });
                (tag);
            }
        }
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "footer-right" },
        });
        if (item.views || item.likes) {
            __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
                ...{ class: "activity-heat" },
            });
            const __VLS_116 = {}.VanIcon;
            /** @type {[typeof __VLS_components.VanIcon, typeof __VLS_components.vanIcon, ]} */ ;
            // @ts-ignore
            const __VLS_117 = __VLS_asFunctionalComponent(__VLS_116, new __VLS_116({
                name: "fire-o",
            }));
            const __VLS_118 = __VLS_117({
                name: "fire-o",
            }, ...__VLS_functionalComponentArgsRest(__VLS_117));
            __VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({});
            (item.views || item.likes || 0);
        }
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "activity-actions" },
        });
        const __VLS_120 = {}.VanButton;
        /** @type {[typeof __VLS_components.VanButton, typeof __VLS_components.vanButton, typeof __VLS_components.VanButton, typeof __VLS_components.vanButton, ]} */ ;
        // @ts-ignore
        const __VLS_121 = __VLS_asFunctionalComponent(__VLS_120, new __VLS_120({
            ...{ 'onClick': {} },
            type: "default",
            size: "small",
            icon: "share-o",
            ...{ class: "action-button share-button" },
            disabled: (!__VLS_ctx.isActivityDataComplete(item)),
        }));
        const __VLS_122 = __VLS_121({
            ...{ 'onClick': {} },
            type: "default",
            size: "small",
            icon: "share-o",
            ...{ class: "action-button share-button" },
            disabled: (!__VLS_ctx.isActivityDataComplete(item)),
        }, ...__VLS_functionalComponentArgsRest(__VLS_121));
        let __VLS_124;
        let __VLS_125;
        let __VLS_126;
        const __VLS_127 = {
            onClick: (...[$event]) => {
                if (!!(__VLS_ctx.loading && !__VLS_ctx.items.length))
                    return;
                if (!!(!__VLS_ctx.items.length))
                    return;
                if (!!(__VLS_ctx.useInfiniteScroll))
                    return;
                __VLS_ctx.handleShare(item);
            }
        };
        __VLS_123.slots.default;
        var __VLS_123;
        const __VLS_128 = {}.VanButton;
        /** @type {[typeof __VLS_components.VanButton, typeof __VLS_components.vanButton, typeof __VLS_components.VanButton, typeof __VLS_components.vanButton, ]} */ ;
        // @ts-ignore
        const __VLS_129 = __VLS_asFunctionalComponent(__VLS_128, new __VLS_128({
            ...{ 'onClick': {} },
            type: (__VLS_ctx.getRegisterButtonType(item)),
            size: "small",
            icon: (__VLS_ctx.getRegisterButtonIcon(item)),
            ...{ class: "action-button register-button" },
            disabled: (!__VLS_ctx.canRegister(item)),
        }));
        const __VLS_130 = __VLS_129({
            ...{ 'onClick': {} },
            type: (__VLS_ctx.getRegisterButtonType(item)),
            size: "small",
            icon: (__VLS_ctx.getRegisterButtonIcon(item)),
            ...{ class: "action-button register-button" },
            disabled: (!__VLS_ctx.canRegister(item)),
        }, ...__VLS_functionalComponentArgsRest(__VLS_129));
        let __VLS_132;
        let __VLS_133;
        let __VLS_134;
        const __VLS_135 = {
            onClick: (...[$event]) => {
                if (!!(__VLS_ctx.loading && !__VLS_ctx.items.length))
                    return;
                if (!!(!__VLS_ctx.items.length))
                    return;
                if (!!(__VLS_ctx.useInfiniteScroll))
                    return;
                __VLS_ctx.handleRegister(item);
            }
        };
        __VLS_131.slots.default;
        (__VLS_ctx.getRegisterButtonText(item));
        var __VLS_131;
    }
}
/** @type {__VLS_StyleScopedClasses['activity-common-card-list']} */ ;
/** @type {__VLS_StyleScopedClasses['loading-container']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-card']} */ ;
/** @type {__VLS_StyleScopedClasses['animate__animated']} */ ;
/** @type {__VLS_StyleScopedClasses['animate__fadeInUp']} */ ;
/** @type {__VLS_StyleScopedClasses['card-image-container']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-status-tag']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-type-tag']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-image']} */ ;
/** @type {__VLS_StyleScopedClasses['image-overlay']} */ ;
/** @type {__VLS_StyleScopedClasses['card-content']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-title']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-description']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-details']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-item']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-content']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-label']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-value']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-item']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-content']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-label']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-value']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-item']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-content']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-label']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-value']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-item']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-content']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-label']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-value']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-form']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-item']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-content']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-label']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-value']} */ ;
/** @type {__VLS_StyleScopedClasses['free-activity']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-item']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-content']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-label']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-value']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-item']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-content']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-label']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-value']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-item']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-content']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-label']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-value']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-footer']} */ ;
/** @type {__VLS_StyleScopedClasses['footer-left']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-tags']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-tag']} */ ;
/** @type {__VLS_StyleScopedClasses['footer-right']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-heat']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-actions']} */ ;
/** @type {__VLS_StyleScopedClasses['action-button']} */ ;
/** @type {__VLS_StyleScopedClasses['share-button']} */ ;
/** @type {__VLS_StyleScopedClasses['action-button']} */ ;
/** @type {__VLS_StyleScopedClasses['register-button']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-card']} */ ;
/** @type {__VLS_StyleScopedClasses['animate__animated']} */ ;
/** @type {__VLS_StyleScopedClasses['animate__fadeInUp']} */ ;
/** @type {__VLS_StyleScopedClasses['card-image-container']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-status-tag']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-type-tag']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-image']} */ ;
/** @type {__VLS_StyleScopedClasses['image-overlay']} */ ;
/** @type {__VLS_StyleScopedClasses['card-content']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-title']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-description']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-details']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-item']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-content']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-label']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-value']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-item']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-content']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-label']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-value']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-item']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-content']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-label']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-value']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-item']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-content']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-label']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-value']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-form']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-item']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-content']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-label']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-value']} */ ;
/** @type {__VLS_StyleScopedClasses['free-activity']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-item']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-content']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-label']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-value']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-item']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-content']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-label']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-value']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-item']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-content']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-label']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-value']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-footer']} */ ;
/** @type {__VLS_StyleScopedClasses['footer-left']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-tags']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-tag']} */ ;
/** @type {__VLS_StyleScopedClasses['footer-right']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-heat']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-actions']} */ ;
/** @type {__VLS_StyleScopedClasses['action-button']} */ ;
/** @type {__VLS_StyleScopedClasses['share-button']} */ ;
/** @type {__VLS_StyleScopedClasses['action-button']} */ ;
/** @type {__VLS_StyleScopedClasses['register-button']} */ ;
var __VLS_dollars;
const __VLS_self = (await import('vue')).defineComponent({
    setup() {
        return {
            scrollContainerRef: scrollContainerRef,
            getScrollContainer: getScrollContainer,
            internalLoading: internalLoading,
            onLoadMore: onLoadMore,
            getActivityStatus: getActivityStatus,
            getStatusTagType: getStatusTagType,
            formatActivityTime: formatActivityTime,
            formatParticipantCount: formatParticipantCount,
            formatActivityFee: formatActivityFee,
            getActivityFormIcon: getActivityFormIcon,
            getActivityFormClass: getActivityFormClass,
            formatActivityForm: formatActivityForm,
            isActivityDataComplete: isActivityDataComplete,
            handleShare: handleShare,
            handleRegister: handleRegister,
            canRegister: canRegister,
            getRegisterButtonType: getRegisterButtonType,
            getRegisterButtonIcon: getRegisterButtonIcon,
            getRegisterButtonText: getRegisterButtonText,
            formatActivityLocation: formatActivityLocation,
            formatRegistrationDeadline: formatRegistrationDeadline,
            formatOrganizer: formatOrganizer,
            formatContact: formatContact,
        };
    },
    emits: {},
    __typeProps: {},
    props: {},
});
export default (await import('vue')).defineComponent({
    setup() {
        return {};
    },
    emits: {},
    __typeProps: {},
    props: {},
});
; /* PartiallyEnd: #4569/main.vue */
