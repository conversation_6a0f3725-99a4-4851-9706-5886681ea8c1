import { ref, onMounted, nextTick, computed } from "vue";
import { useRoute, useRouter } from "vue-router";
import { showToast } from "vant";
import GlobalHeader from '../../components/GlobalHeader.vue';
import GlobalFooter from '../../components/GlobalFooter.vue';
import { getVideoDetail } from './api'; // 请确保你有此接口
const route = useRoute();
const router = useRouter();
const loading = ref(false);
const error = ref("");
const isPlaying = ref(false);
const videoRef = ref(null);
// 提取腾讯视频链接中的 vid
const getTencentVid = (url) => {
    const match = url.match(/\/page\/([a-zA-Z0-9]+)\.html/);
    return match ? match[1] : null;
};
// 如果是腾讯视频链接，提取 vid
const tencentVid = computed(() => {
    if (videoDetail.value?.video?.includes("v.qq.com/x/page/")) {
        return getTencentVid(videoDetail.value.video);
    }
    return null;
});
const videoDetail = ref(null);
// 获取视频详情
const fetchVideoDetail = async () => {
    const id = Array.isArray(route.params.id) ? route.params.id[0] : route.params.id;
    if (!id) {
        error.value = "视频ID无效";
        return;
    }
    try {
        loading.value = true;
        const response = await getVideoDetail(id);
        const data = response.videos;
        if (!data)
            throw new Error("数据异常");
        // 调试输出：检查API返回的数据
        console.log('API返回的原始视频数据:', data);
        console.log('创作者字段:', data.creater);
        console.log('发布时间字段:', data.create_time);
        videoDetail.value = {
            id: data.id,
            name: data.name,
            duration: "35:20",
            cat_display: data.cat_display,
            desc: data.desc,
            thumbnail: data.thumbnail.trim(),
            content: data.content,
            creater: data.creater || "未知创作者",
            create_time: data.create_time || new Date().toISOString().split('T')[0],
            video: data.video, // 默认视频链接
            tags: ["养生保健", "健身运动"],
        };
        // 调试输出：检查处理后的数据
        console.log('处理后的视频详情数据:', videoDetail.value);
    }
    catch (e) {
        console.error(e);
        error.value = "获取视频失败";
    }
    finally {
        loading.value = false;
    }
};
// 格式化发布时间
const formatDate = (dateStr) => {
    if (!dateStr)
        return '';
    try {
        const date = new Date(dateStr);
        if (isNaN(date.getTime())) {
            return dateStr; // 如果无法解析，返回原始字符串
        }
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
    }
    catch (error) {
        console.warn('时间格式化失败:', error);
        return dateStr;
    }
};
// 点击播放按钮
const playVideo = () => {
    isPlaying.value = true;
    nextTick(() => {
        videoRef.value?.play().catch(() => {
            showToast("无法播放视频");
        });
    });
};
// 返回上一页
const onClickLeft = () => {
    router.back();
};
onMounted(fetchVideoDetail);
debugger; /* PartiallyEnd: #3632/scriptSetup.vue */
const __VLS_ctx = {};
let __VLS_components;
let __VLS_directives;
/** @type {__VLS_StyleScopedClasses['detail-meta']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-meta']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-meta']} */ ;
/** @type {__VLS_StyleScopedClasses['content-section']} */ ;
/** @type {__VLS_StyleScopedClasses['content-section']} */ ;
/** @type {__VLS_StyleScopedClasses['content-section']} */ ;
// CSS variable injection 
// CSS variable injection end 
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ class: "video-promotion-detail-container" },
});
/** @type {[typeof GlobalHeader, ]} */ ;
// @ts-ignore
const __VLS_0 = __VLS_asFunctionalComponent(GlobalHeader, new GlobalHeader({
    ...{ 'onLeftClick': {} },
    title: "视频详情",
}));
const __VLS_1 = __VLS_0({
    ...{ 'onLeftClick': {} },
    title: "视频详情",
}, ...__VLS_functionalComponentArgsRest(__VLS_0));
let __VLS_3;
let __VLS_4;
let __VLS_5;
const __VLS_6 = {
    onLeftClick: (__VLS_ctx.onClickLeft)
};
var __VLS_2;
if (!__VLS_ctx.loading && !__VLS_ctx.error && __VLS_ctx.videoDetail) {
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
        ...{ class: "detail-content" },
    });
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
        ...{ class: "video-player" },
    });
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
        ...{ class: "video-wrapper" },
    });
    if (__VLS_ctx.tencentVid) {
        __VLS_asFunctionalElement(__VLS_intrinsicElements.iframe, __VLS_intrinsicElements.iframe)({
            ...{ class: "tencent-video-iframe" },
            src: (`https://v.qq.com/txp/iframe/player.html?vid=${__VLS_ctx.tencentVid}`),
            frameborder: "0",
            allowfullscreen: true,
        });
    }
    else {
        if (!__VLS_ctx.isPlaying) {
            __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
                ...{ onClick: (__VLS_ctx.playVideo) },
                ...{ class: "video-placeholder" },
            });
            __VLS_asFunctionalElement(__VLS_intrinsicElements.img)({
                src: (__VLS_ctx.videoDetail.thumbnail),
                alt: (__VLS_ctx.videoDetail.name),
                ...{ class: "video-thumbnail" },
            });
            __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
                ...{ class: "play-button" },
            });
            const __VLS_7 = {}.VanIcon;
            /** @type {[typeof __VLS_components.VanIcon, typeof __VLS_components.vanIcon, ]} */ ;
            // @ts-ignore
            const __VLS_8 = __VLS_asFunctionalComponent(__VLS_7, new __VLS_7({
                name: "play",
                size: "48",
                color: "white",
            }));
            const __VLS_9 = __VLS_8({
                name: "play",
                size: "48",
                color: "white",
            }, ...__VLS_functionalComponentArgsRest(__VLS_8));
            __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
                ...{ class: "video-duration" },
            });
            (__VLS_ctx.videoDetail.duration);
        }
        else {
            __VLS_asFunctionalElement(__VLS_intrinsicElements.video, __VLS_intrinsicElements.video)({
                ...{ onEnded: (...[$event]) => {
                        if (!(!__VLS_ctx.loading && !__VLS_ctx.error && __VLS_ctx.videoDetail))
                            return;
                        if (!!(__VLS_ctx.tencentVid))
                            return;
                        if (!!(!__VLS_ctx.isPlaying))
                            return;
                        __VLS_ctx.isPlaying = false;
                    } },
                ref: "videoRef",
                ...{ class: "video-element" },
                controls: true,
                autoplay: true,
                src: (__VLS_ctx.videoDetail.video),
            });
            /** @type {typeof __VLS_ctx.videoRef} */ ;
        }
    }
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
        ...{ class: "detail-header" },
    });
    __VLS_asFunctionalElement(__VLS_intrinsicElements.h1, __VLS_intrinsicElements.h1)({
        ...{ class: "detail-title" },
    });
    (__VLS_ctx.videoDetail.name);
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
        ...{ class: "detail-meta" },
    });
    __VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({
        ...{ class: "author" },
    });
    const __VLS_11 = {}.VanIcon;
    /** @type {[typeof __VLS_components.VanIcon, typeof __VLS_components.vanIcon, ]} */ ;
    // @ts-ignore
    const __VLS_12 = __VLS_asFunctionalComponent(__VLS_11, new __VLS_11({
        name: "user-o",
        size: "12",
    }));
    const __VLS_13 = __VLS_12({
        name: "user-o",
        size: "12",
    }, ...__VLS_functionalComponentArgsRest(__VLS_12));
    (__VLS_ctx.videoDetail.creater);
    __VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({
        ...{ class: "date" },
    });
    const __VLS_15 = {}.VanIcon;
    /** @type {[typeof __VLS_components.VanIcon, typeof __VLS_components.vanIcon, ]} */ ;
    // @ts-ignore
    const __VLS_16 = __VLS_asFunctionalComponent(__VLS_15, new __VLS_15({
        name: "clock-o",
        size: "12",
    }));
    const __VLS_17 = __VLS_16({
        name: "clock-o",
        size: "12",
    }, ...__VLS_functionalComponentArgsRest(__VLS_16));
    (__VLS_ctx.formatDate(__VLS_ctx.videoDetail.create_time));
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
        ...{ class: "detail-body" },
    });
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
        ...{ class: "content-section" },
    });
    __VLS_asFunctionalElement(__VLS_intrinsicElements.h3, __VLS_intrinsicElements.h3)({});
    const __VLS_19 = {}.VanIcon;
    /** @type {[typeof __VLS_components.VanIcon, typeof __VLS_components.vanIcon, ]} */ ;
    // @ts-ignore
    const __VLS_20 = __VLS_asFunctionalComponent(__VLS_19, new __VLS_19({
        name: "info-o",
        size: "18",
        color: "#f5a623",
    }));
    const __VLS_21 = __VLS_20({
        name: "info-o",
        size: "18",
        color: "#f5a623",
    }, ...__VLS_functionalComponentArgsRest(__VLS_20));
    __VLS_asFunctionalElement(__VLS_intrinsicElements.p, __VLS_intrinsicElements.p)({});
    (__VLS_ctx.videoDetail.desc);
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
        ...{ class: "content-section" },
    });
    __VLS_asFunctionalElement(__VLS_intrinsicElements.h3, __VLS_intrinsicElements.h3)({});
    const __VLS_23 = {}.VanIcon;
    /** @type {[typeof __VLS_components.VanIcon, typeof __VLS_components.vanIcon, ]} */ ;
    // @ts-ignore
    const __VLS_24 = __VLS_asFunctionalComponent(__VLS_23, new __VLS_23({
        name: "notes-o",
        size: "18",
        color: "#722ed1",
    }));
    const __VLS_25 = __VLS_24({
        name: "notes-o",
        size: "18",
        color: "#722ed1",
    }, ...__VLS_functionalComponentArgsRest(__VLS_24));
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({});
    __VLS_asFunctionalDirective(__VLS_directives.vHtml)(null, { ...__VLS_directiveBindingRestFields, value: (__VLS_ctx.videoDetail.content) }, null, null);
}
if (__VLS_ctx.loading) {
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
        ...{ class: "loading-container" },
    });
    const __VLS_27 = {}.VanLoading;
    /** @type {[typeof __VLS_components.VanLoading, typeof __VLS_components.vanLoading, ]} */ ;
    // @ts-ignore
    const __VLS_28 = __VLS_asFunctionalComponent(__VLS_27, new __VLS_27({
        type: "spinner",
        color: "#1989fa",
    }));
    const __VLS_29 = __VLS_28({
        type: "spinner",
        color: "#1989fa",
    }, ...__VLS_functionalComponentArgsRest(__VLS_28));
    __VLS_asFunctionalElement(__VLS_intrinsicElements.p, __VLS_intrinsicElements.p)({});
}
if (!__VLS_ctx.loading && __VLS_ctx.error) {
    const __VLS_31 = {}.VanEmpty;
    /** @type {[typeof __VLS_components.VanEmpty, typeof __VLS_components.vanEmpty, ]} */ ;
    // @ts-ignore
    const __VLS_32 = __VLS_asFunctionalComponent(__VLS_31, new __VLS_31({
        description: (__VLS_ctx.error),
    }));
    const __VLS_33 = __VLS_32({
        description: (__VLS_ctx.error),
    }, ...__VLS_functionalComponentArgsRest(__VLS_32));
}
/** @type {[typeof GlobalFooter, ]} */ ;
// @ts-ignore
const __VLS_35 = __VLS_asFunctionalComponent(GlobalFooter, new GlobalFooter({}));
const __VLS_36 = __VLS_35({}, ...__VLS_functionalComponentArgsRest(__VLS_35));
/** @type {__VLS_StyleScopedClasses['video-promotion-detail-container']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-content']} */ ;
/** @type {__VLS_StyleScopedClasses['video-player']} */ ;
/** @type {__VLS_StyleScopedClasses['video-wrapper']} */ ;
/** @type {__VLS_StyleScopedClasses['tencent-video-iframe']} */ ;
/** @type {__VLS_StyleScopedClasses['video-placeholder']} */ ;
/** @type {__VLS_StyleScopedClasses['video-thumbnail']} */ ;
/** @type {__VLS_StyleScopedClasses['play-button']} */ ;
/** @type {__VLS_StyleScopedClasses['video-duration']} */ ;
/** @type {__VLS_StyleScopedClasses['video-element']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-header']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-title']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-meta']} */ ;
/** @type {__VLS_StyleScopedClasses['author']} */ ;
/** @type {__VLS_StyleScopedClasses['date']} */ ;
/** @type {__VLS_StyleScopedClasses['detail-body']} */ ;
/** @type {__VLS_StyleScopedClasses['content-section']} */ ;
/** @type {__VLS_StyleScopedClasses['content-section']} */ ;
/** @type {__VLS_StyleScopedClasses['loading-container']} */ ;
var __VLS_dollars;
const __VLS_self = (await import('vue')).defineComponent({
    setup() {
        return {
            GlobalHeader: GlobalHeader,
            GlobalFooter: GlobalFooter,
            loading: loading,
            error: error,
            isPlaying: isPlaying,
            videoRef: videoRef,
            tencentVid: tencentVid,
            videoDetail: videoDetail,
            formatDate: formatDate,
            playVideo: playVideo,
            onClickLeft: onClickLeft,
        };
    },
});
export default (await import('vue')).defineComponent({
    setup() {
        return {};
    },
});
; /* PartiallyEnd: #4569/main.vue */
