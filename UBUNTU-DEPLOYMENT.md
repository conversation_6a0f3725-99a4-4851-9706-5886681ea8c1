# 中医智慧前端项目 - 阿里云 Ubuntu20 服务器部署指南

## 🌟 部署概述

本指南专门针对阿里云 Ubuntu20 服务器，提供从零开始的完整部署方案。

## 🏗️ 服务器环境要求

- **操作系统**: Ubuntu 20.04 LTS
- **服务器配置**: 最低 1 核 2GB 内存
- **网络**: 公网 IP，开放 80/443 端口
- **权限**: sudo 管理员权限

## 🚀 快速部署 (推荐)

### 方法一：一键部署脚本

1. **上传项目文件到服务器**

```bash
# 本地打包项目
cd zyyfront
tar -czf zyyfront.tar.gz .

# 上传到服务器 (替换为您的服务器IP)
scp zyyfront.tar.gz root@YOUR_SERVER_IP:/home/
```

2. **服务器上解压并部署**

```bash
# 登录服务器
ssh root@YOUR_SERVER_IP

# 解压项目
cd /home
tar -xzf zyyfront.tar.gz
cd zyyfront

# 一键安装环境并部署
sudo bash deploy-ubuntu.sh     # 安装服务器环境
bash deploy-project.sh        # 构建和部署项目
```

## 📋 详细部署步骤

### 步骤 1: 准备服务器环境

#### 1.1 连接服务器

```bash
ssh root@YOUR_SERVER_IP
```

#### 1.2 更新系统

```bash
sudo apt update && sudo apt upgrade -y
```

#### 1.3 安装基础依赖

```bash
sudo apt install -y curl wget git build-essential
```

### 步骤 2: 安装 Node.js 环境

#### 2.1 安装 Node.js 18.x

```bash
# 添加NodeSource官方源
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -

# 安装Node.js
sudo apt install -y nodejs

# 验证安装
node --version
npm --version
```

#### 2.2 配置 npm 镜像 (可选，加速下载)

```bash
npm config set registry https://registry.npmmirror.com
```

### 步骤 3: 安装 Web 服务器

#### 3.1 安装 Nginx

```bash
sudo apt install -y nginx
```

#### 3.2 启动并启用 Nginx

```bash
sudo systemctl start nginx
sudo systemctl enable nginx
```

#### 3.3 检查 Nginx 状态

```bash
sudo systemctl status nginx
```

### 步骤 4: 配置防火墙

#### 4.1 配置 UFW 防火墙

```bash
sudo ufw allow OpenSSH
sudo ufw allow 'Nginx Full'
sudo ufw --force enable
```

#### 4.2 配置阿里云安全组

在阿里云控制台添加安全组规则：

- 入方向：TCP 80 端口 (HTTP)
- 入方向：TCP 443 端口 (HTTPS)
- 入方向：TCP 22 端口 (SSH)

### 步骤 5: 上传和部署项目

#### 5.1 上传项目文件

```bash
# 方法1: 使用scp上传
scp -r ./zyyfront root@YOUR_SERVER_IP:/var/www/

# 方法2: 使用git克隆 (如果项目在git仓库)
cd /var/www
git clone YOUR_GIT_REPO zyyfront
```

#### 5.2 构建项目

```bash
cd /var/www/zyyfront

# 安装依赖
npm ci

# 构建生产版本
npm run build
```

#### 5.3 部署到 Web 目录

```bash
# 清空默认Web目录
sudo rm -rf /var/www/html/*

# 复制构建文件
sudo cp -r dist/* /var/www/html/

# 设置权限
sudo chown -R www-data:www-data /var/www/html
sudo chmod -R 755 /var/www/html
```

### 步骤 6: 配置 Nginx

#### 6.1 创建站点配置

```bash
sudo nano /etc/nginx/sites-available/zyyfront
```

#### 6.2 添加配置内容

```nginx
server {
    listen 80;
    server_name YOUR_DOMAIN_OR_IP;
    root /var/www/html;
    index index.html;

    # 日志配置
    access_log /var/log/nginx/zyyfront_access.log;
    error_log /var/log/nginx/zyyfront_error.log;

    # 启用gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/javascript
        application/xml+rss
        application/json;

    # 静态资源缓存
    location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        access_log off;
    }

    # API代理转发
    location /api/ {
        proxy_pass https://zyy.sxaliyun.cn;
        proxy_set_header Host $proxy_host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }

    # Vue Router单页面应用配置
    location / {
        try_files $uri $uri/ /index.html;
        add_header Cache-Control "no-cache, no-store, must-revalidate";
        add_header Pragma "no-cache";
        add_header Expires "0";
    }

    # 安全头部
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";

    # 移动端优化
    add_header Vary "User-Agent";

    # 隐藏服务器版本
    server_tokens off;
}
```

#### 6.3 启用站点配置

```bash
# 创建软链接
sudo ln -s /etc/nginx/sites-available/zyyfront /etc/nginx/sites-enabled/

# 删除默认配置
sudo rm -f /etc/nginx/sites-enabled/default

# 测试配置
sudo nginx -t

# 重启Nginx
sudo systemctl reload nginx
```

### 步骤 7: 验证部署

#### 7.1 检查服务状态

```bash
sudo systemctl status nginx
```

#### 7.2 访问网站

```bash
# 获取服务器IP
curl http://checkip.amazonaws.com

# 在浏览器中访问
http://YOUR_SERVER_IP
```

## 🔒 SSL 证书配置 (可选)

### 使用 Let's Encrypt 免费证书

#### 1. 安装 Certbot

```bash
sudo apt install -y certbot python3-certbot-nginx
```

#### 2. 申请证书

```bash
sudo certbot --nginx -d YOUR_DOMAIN
```

#### 3. 配置自动续期

```bash
sudo crontab -e
# 添加以下行
0 12 * * * /usr/bin/certbot renew --quiet
```

## 📊 性能优化

### 1. 启用 HTTP/2

在 Nginx 配置中添加：

```nginx
listen 443 ssl http2;
```

### 2. 配置缓存

```bash
# 创建缓存目录
sudo mkdir -p /var/cache/nginx

# 在Nginx配置中添加缓存设置
proxy_cache_path /var/cache/nginx levels=1:2 keys_zone=my_cache:10m max_size=10g inactive=60m use_temp_path=off;
```

### 3. 优化文件句柄数

```bash
# 编辑系统限制
sudo nano /etc/security/limits.conf

# 添加以下行
* soft nofile 65535
* hard nofile 65535
```

## 🔧 维护和监控

### 1. 日志监控

```bash
# 实时查看访问日志
sudo tail -f /var/log/nginx/zyyfront_access.log

# 查看错误日志
sudo tail -f /var/log/nginx/zyyfront_error.log
```

### 2. 系统监控

```bash
# 查看系统资源使用
htop

# 查看磁盘使用
df -h

# 查看内存使用
free -h
```

### 3. 定期备份

```bash
# 创建备份脚本
sudo nano /home/<USER>

#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
tar -czf /var/backups/zyyfront_$DATE.tar.gz /var/www/html
find /var/backups -name "zyyfront_*.tar.gz" -mtime +7 -delete

# 添加执行权限
sudo chmod +x /home/<USER>

# 添加到定时任务
sudo crontab -e
0 2 * * * /home/<USER>
```

## 🛠️ 故障排除

### 常见问题解决

#### 1. 页面无法访问

```bash
# 检查Nginx状态
sudo systemctl status nginx

# 检查端口占用
sudo netstat -tlnp | grep :80

# 检查防火墙
sudo ufw status
```

#### 2. API 请求失败

```bash
# 检查Nginx配置
sudo nginx -t

# 查看错误日志
sudo tail -f /var/log/nginx/zyyfront_error.log
```

#### 3. 构建失败

```bash
# 检查Node.js版本
node --version

# 清除npm缓存
npm cache clean --force

# 重新安装依赖
rm -rf node_modules package-lock.json
npm install
```

## 📞 技术支持

### 获取帮助

- 查看 Nginx 错误日志: `/var/log/nginx/error.log`
- 查看系统日志: `sudo journalctl -u nginx`
- 检查系统资源: `htop`, `df -h`, `free -h`

### 联系方式

如遇到问题，请提供以下信息：

1. 错误日志内容
2. 服务器配置信息
3. 操作步骤详情

---

## ✅ 部署检查清单

- [ ] 服务器基础环境安装完成
- [ ] Node.js 18.x 安装成功
- [ ] Nginx 安装并运行
- [ ] 防火墙配置正确
- [ ] 项目构建无错误
- [ ] 文件部署到 Web 目录
- [ ] Nginx 配置正确
- [ ] 网站可正常访问
- [ ] API 代理工作正常
- [ ] SSL 证书配置(可选)
- [ ] 备份策略设置

**部署完成后，您的中医智慧应用将在阿里云服务器上稳定运行！** 🎉
