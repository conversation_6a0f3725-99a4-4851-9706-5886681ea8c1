# 项目简述

## 项目名称
SXZYY (中医医院相关应用)

## 项目目标
提供一个基于Vue.js的中医医院信息管理和展示平台，包含活动、科室、医生、知识、药品、中医知识、科教图文等多个模块。

## 核心功能
- 活动展示与详情
- 科室信息与详情（特色科室和重点科室）
- 医生信息与详情（名中医展示）
- 医学知识文章与分类
- 药品信息与详情（特色制剂）
- 中医知识（理论、实践、病案、本草等）
- 科教图文（医学科教内容）
- 中医文化展示
- 中医案例展示
- 视频宣传
- 用户中心
- 首页轮播、功能网格、新闻、视频、文章等展示模块

## 技术栈概览
- 前端框架: Vue.js
- 构建工具: Vite
- 语言: TypeScript (部分文件为.ts)
- API交互: 存在 `src/api` 目录，包含封装的请求逻辑。
- 路由管理: 存在 `src/router` 目录。
- 组件化: 存在 `src/components` 目录和各视图下的组件。
- UI组件库: 使用Vant组件库（从代码中可见）

## 当前工作重点
更新记忆库，以确保系统对项目有全面的理解。