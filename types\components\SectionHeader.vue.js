import { defineProps, defineEmits } from 'vue';
import { useRouter } from 'vue-router';
const props = defineProps({
    title: { type: String, required: true },
    icon: { type: String, default: 'apps-o' },
    moreLink: { type: String, default: '' }, // 可选链接地址
    showMore: { type: Boolean, default: true }, // 是否显示“更多”
});
const emit = defineEmits(['more-click']);
const router = useRouter();
const handleMoreClick = () => {
    if (props.moreLink) {
        router.push(props.moreLink);
    }
    else {
        emit('more-click');
    }
};
debugger; /* PartiallyEnd: #3632/scriptSetup.vue */
const __VLS_ctx = {};
let __VLS_components;
let __VLS_directives;
/** @type {__VLS_StyleScopedClasses['title-left']} */ ;
/** @type {__VLS_StyleScopedClasses['title-right']} */ ;
// CSS variable injection 
// CSS variable injection end 
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ class: "section-header animate__animated animate__fadeInUp" },
});
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ class: "section-title" },
});
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ class: "title-left" },
});
const __VLS_0 = {}.VanIcon;
/** @type {[typeof __VLS_components.VanIcon, typeof __VLS_components.vanIcon, ]} */ ;
// @ts-ignore
const __VLS_1 = __VLS_asFunctionalComponent(__VLS_0, new __VLS_0({
    name: (__VLS_ctx.icon),
}));
const __VLS_2 = __VLS_1({
    name: (__VLS_ctx.icon),
}, ...__VLS_functionalComponentArgsRest(__VLS_1));
__VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({});
(__VLS_ctx.title);
if (__VLS_ctx.showMore) {
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
        ...{ onClick: (__VLS_ctx.handleMoreClick) },
        ...{ class: "title-right" },
    });
    __VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({});
    const __VLS_4 = {}.VanIcon;
    /** @type {[typeof __VLS_components.VanIcon, typeof __VLS_components.vanIcon, ]} */ ;
    // @ts-ignore
    const __VLS_5 = __VLS_asFunctionalComponent(__VLS_4, new __VLS_4({
        name: "arrow",
    }));
    const __VLS_6 = __VLS_5({
        name: "arrow",
    }, ...__VLS_functionalComponentArgsRest(__VLS_5));
}
/** @type {__VLS_StyleScopedClasses['section-header']} */ ;
/** @type {__VLS_StyleScopedClasses['animate__animated']} */ ;
/** @type {__VLS_StyleScopedClasses['animate__fadeInUp']} */ ;
/** @type {__VLS_StyleScopedClasses['section-title']} */ ;
/** @type {__VLS_StyleScopedClasses['title-left']} */ ;
/** @type {__VLS_StyleScopedClasses['title-right']} */ ;
var __VLS_dollars;
const __VLS_self = (await import('vue')).defineComponent({
    setup() {
        return {
            handleMoreClick: handleMoreClick,
        };
    },
    emits: {},
    props: {
        title: { type: String, required: true },
        icon: { type: String, default: 'apps-o' },
        moreLink: { type: String, default: '' }, // 可选链接地址
        showMore: { type: Boolean, default: true }, // 是否显示“更多”
    },
});
export default (await import('vue')).defineComponent({
    setup() {
        return {};
    },
    emits: {},
    props: {
        title: { type: String, required: true },
        icon: { type: String, default: 'apps-o' },
        moreLink: { type: String, default: '' }, // 可选链接地址
        showMore: { type: Boolean, default: true }, // 是否显示“更多”
    },
});
; /* PartiallyEnd: #4569/main.vue */
