import{d as l,a as i,u as p,r as m,l as _,c as u,o as f,b as a,g as h,_ as v}from"./index-BE8XLQ59.js";import{G as b,a as x}from"./GlobalHeader-lL88u8sR.js";import{L as g}from"./LeaderDetailCard-VVs8AxZp.js";import{s as t}from"./function-call-BUl5915X.js";import"./index-CAfqjps3.js";import"./index-B27ddgxZ.js";const D={class:"main-dept-detail-page"},L={class:"detail-content"},R=l({__name:"index",setup(k){const s=i(),r=p(),o=m("");_(()=>{o.value=s.params.id,o.value||(t("科室ID无效"),r.back())});const n=e=>{console.log("重点科室详情加载成功:",e)},d=e=>{console.error("重点科室详情加载失败:",e),t(e)},c=()=>{console.log("重试加载重点科室详情")};return(e,y)=>(f(),u("div",D,[a(b,{title:"重点科室详情"}),h("div",L,[a(g,{"leader-id":o.value,"api-type":"mainDept",onLoaded:n,onError:d,onRetry:c},null,8,["leader-id"])]),a(x)]))}}),V=v(R,[["__scopeId","data-v-7ccccbd2"]]);export{V as default};
