/**
 * 微信分享工具
 * 用于配置微信JS-SDK分享功能
 */

declare global {
  interface Window {
    wx: any;
  }
}

export interface ShareConfig {
  title: string;
  desc: string;
  link: string;
  imgUrl: string;
}

/**
 * 配置微信分享
 * @param config 分享配置
 */
export function configWechatShare(config: ShareConfig): void {
  if (typeof window === 'undefined' || !window.wx) {
    console.warn('微信JS-SDK未加载');
    return;
  }

  // 配置微信JS-SDK
  window.wx.config({
    debug: false, // 开启调试模式
    appId: '', // 必填，公众号的唯一标识
    timestamp: Math.floor(Date.now() / 1000), // 必填，生成签名的时间戳
    nonceStr: Math.random().toString(36).substr(2, 15), // 必填，生成签名的随机串
    signature: '', // 必填，签名
    jsApiList: [
      'updateAppMessageShareData', // 分享给朋友
      'updateTimelineShareData', // 分享到朋友圈
      'onMenuShareAppMessage', // 旧版分享给朋友
      'onMenuShareTimeline' // 旧版分享到朋友圈
    ]
  });

  window.wx.ready(() => {
    // 新版API - 分享给朋友
    window.wx.updateAppMessageShareData({
      title: config.title,
      desc: config.desc,
      link: config.link,
      imgUrl: config.imgUrl,
      success: () => {
        console.log('分享给朋友配置成功');
      },
      fail: (error: any) => {
        console.error('分享给朋友配置失败:', error);
      }
    });

    // 新版API - 分享到朋友圈
    window.wx.updateTimelineShareData({
      title: config.title,
      link: config.link,
      imgUrl: config.imgUrl,
      success: () => {
        console.log('分享到朋友圈配置成功');
      },
      fail: (error: any) => {
        console.error('分享到朋友圈配置失败:', error);
      }
    });

    // 兼容旧版API - 分享给朋友
    window.wx.onMenuShareAppMessage({
      title: config.title,
      desc: config.desc,
      link: config.link,
      imgUrl: config.imgUrl,
      success: () => {
        console.log('旧版分享给朋友成功');
      },
      cancel: () => {
        console.log('用户取消分享给朋友');
      }
    });

    // 兼容旧版API - 分享到朋友圈
    window.wx.onMenuShareTimeline({
      title: config.title,
      link: config.link,
      imgUrl: config.imgUrl,
      success: () => {
        console.log('旧版分享到朋友圈成功');
      },
      cancel: () => {
        console.log('用户取消分享到朋友圈');
      }
    });
  });

  window.wx.error((res: any) => {
    console.error('微信JS-SDK配置失败:', res);
  });
}

/**
 * 获取当前页面的分享配置
 * @param customConfig 自定义配置
 * @returns ShareConfig
 */
export function getPageShareConfig(customConfig?: Partial<ShareConfig>): ShareConfig {
  const defaultConfig: ShareConfig = {
    title: document.title || '中医智慧 - 传承千年中医文化，守护您的健康',
    desc: getMetaContent('description') || '专业的中医健康平台，提供中医知识、养生指导、专家咨询等服务。传承千年中医文化，守护您的健康。',
    link: window.location.href.split('#')[0], // 移除hash部分
    imgUrl: getMetaContent('og:image') || 'https://gujiao.sxaliyun.cn/images/og-image.png'
  };

  return { ...defaultConfig, ...customConfig };
}

/**
 * 获取meta标签内容
 * @param name meta标签的name或property
 * @returns string
 */
function getMetaContent(name: string): string {
  const meta = document.querySelector(`meta[name="${name}"]`) || 
               document.querySelector(`meta[property="${name}"]`);
  return meta?.getAttribute('content') || '';
}

/**
 * 初始化页面分享
 * @param customConfig 自定义配置
 */
export function initPageShare(customConfig?: Partial<ShareConfig>): void {
  const shareConfig = getPageShareConfig(customConfig);
  configWechatShare(shareConfig);
}

/**
 * 更新页面分享信息
 * @param title 页面标题
 * @param description 页面描述
 * @param imageUrl 分享图片URL
 */
export function updatePageShare(title?: string, description?: string, imageUrl?: string): void {
  const customConfig: Partial<ShareConfig> = {};
  
  if (title) customConfig.title = title;
  if (description) customConfig.desc = description;
  if (imageUrl) customConfig.imgUrl = imageUrl;
  
  initPageShare(customConfig);
}
