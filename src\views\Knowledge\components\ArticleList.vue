<template>
  <div class="section-container featured-articles-section">
    <SectionHeader title="精品文章" icon="description-o" :showMore="false" />

    <CommonCardList :items="cultureData" :loading="loading" :finished="finished" :use-infinite-scroll="false"
      @card-click="onCultureClick" />

    <CommonCardList :items="knowledgeData" :loading="loading" :finished="finished" :use-infinite-scroll="false"
      @card-click="onKnowledgeClick" />
    <CommonCardList :items="caseData" :loading="loading" :finished="finished" :use-infinite-scroll="false"
      @card-click="onCaseClick" />

  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import SectionHeader from '../../../components/SectionHeader.vue'; // 引入通用标题组件
import CommonCardList from '../../../components/CommonCardList.vue';

const router = useRouter();
const loading = ref(false);
const finished = ref(false); // 是否已加载完所有数据

const props = defineProps<{
  cultureData: Array<{
    id: number
    name: string
    thumbnail: string
    desc: string
  }>
  knowledgeData: Array<{
    id: number
    name: string
    thumbnail: string
    desc: string
  }>
  caseData: Array<{
    id: number
    name: string
    thumbnail: string
    desc: string
  }>
}>()
// 文章点击事件
const onCultureClick = (item: any) => {
  // 跳转到文章详情页
  router.push({ name: 'CultureDetail', params: { id: item.id } });
};

const onKnowledgeClick = (item: any) => {
  // 跳转到文章详情页
  router.push({ name: 'TcmKnowledgeDetail', params: { id: item.id } });
};
const onCaseClick = (item: any) => {
  // 跳转到文章详情页
  router.push({ name: 'CasesDetail', params: { id: item.id } });
};
// 组件挂载时打印数据
onMounted(() => {
  console.log('cultureData:', props.cultureData)
  console.log('knowledgeData:', props.knowledgeData)
  console.log('caseData:', props.caseData)
})
</script>
<style>
@import "../../../style/common.css";
</style>
<style src="../style.css"></style>
