import { ref, computed, onMounted, defineProps } from 'vue';
import { useSystemConfig } from '../composables/useSystemConfig';
// 正确定义 props
const props = defineProps({
    title: {
        type: String,
        default: "", // 空字符串表示使用配置的默认标题
    },
    homePath: {
        type: String,
        default: "/"
    }
});
const emit = defineEmits(["left-click"]);
const initialPath = ref('');
// 使用系统配置
const { getHospitalName } = useSystemConfig();
// 计算显示的标题
const displayTitle = computed(() => {
    // 如果props.title有值，使用props.title；否则使用配置的医院名称
    return props.title || getHospitalName();
});
onMounted(() => {
    // 记录组件挂载时的路径作为首页参考
    initialPath.value = window.location.pathname;
});
// 计算是否显示返回按钮
const showBackButton = computed(() => {
    // 检查当前路径是否为首页
    const isHomePage = window.location.pathname === props.homePath ||
        window.location.pathname === initialPath.value;
    // 不在首页且有历史记录时显示返回按钮
    return !isHomePage && window.history.length > 1;
});
const handleLeftClick = () => {
    if (showBackButton.value) {
        // 只有当按钮显示时才执行返回操作
        window.history.back();
    }
    else {
        // 首页或无法返回时，触发自定义事件
        emit("left-click");
    }
};
debugger; /* PartiallyEnd: #3632/scriptSetup.vue */
const __VLS_ctx = {};
let __VLS_components;
let __VLS_directives;
/** @type {__VLS_StyleScopedClasses['global-header']} */ ;
/** @type {__VLS_StyleScopedClasses['back-button']} */ ;
/** @type {__VLS_StyleScopedClasses['back-button']} */ ;
/** @type {__VLS_StyleScopedClasses['back-button']} */ ;
/** @type {__VLS_StyleScopedClasses['page-title']} */ ;
/** @type {__VLS_StyleScopedClasses['header-content']} */ ;
/** @type {__VLS_StyleScopedClasses['page-title']} */ ;
/** @type {__VLS_StyleScopedClasses['back-button']} */ ;
/** @type {__VLS_StyleScopedClasses['back-button']} */ ;
/** @type {__VLS_StyleScopedClasses['van-icon']} */ ;
/** @type {__VLS_StyleScopedClasses['header-content']} */ ;
/** @type {__VLS_StyleScopedClasses['page-title']} */ ;
/** @type {__VLS_StyleScopedClasses['back-button']} */ ;
/** @type {__VLS_StyleScopedClasses['global-header']} */ ;
/** @type {__VLS_StyleScopedClasses['global-header']} */ ;
/** @type {__VLS_StyleScopedClasses['global-header']} */ ;
/** @type {__VLS_StyleScopedClasses['global-header']} */ ;
// CSS variable injection 
// CSS variable injection end 
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ class: "global-header" },
});
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ class: "header-content" },
});
if (__VLS_ctx.showBackButton) {
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
        ...{ onClick: (__VLS_ctx.handleLeftClick) },
        ...{ class: "back-button" },
    });
    const __VLS_0 = {}.VanIcon;
    /** @type {[typeof __VLS_components.VanIcon, typeof __VLS_components.vanIcon, ]} */ ;
    // @ts-ignore
    const __VLS_1 = __VLS_asFunctionalComponent(__VLS_0, new __VLS_0({
        name: "arrow-left",
        size: "18",
    }));
    const __VLS_2 = __VLS_1({
        name: "arrow-left",
        size: "18",
    }, ...__VLS_functionalComponentArgsRest(__VLS_1));
}
__VLS_asFunctionalElement(__VLS_intrinsicElements.h1, __VLS_intrinsicElements.h1)({
    ...{ class: "page-title" },
    ...{ class: ({ 'no-back-button': !__VLS_ctx.showBackButton }) },
});
(__VLS_ctx.displayTitle);
/** @type {__VLS_StyleScopedClasses['global-header']} */ ;
/** @type {__VLS_StyleScopedClasses['header-content']} */ ;
/** @type {__VLS_StyleScopedClasses['back-button']} */ ;
/** @type {__VLS_StyleScopedClasses['page-title']} */ ;
/** @type {__VLS_StyleScopedClasses['no-back-button']} */ ;
var __VLS_dollars;
const __VLS_self = (await import('vue')).defineComponent({
    setup() {
        return {
            displayTitle: displayTitle,
            showBackButton: showBackButton,
            handleLeftClick: handleLeftClick,
        };
    },
    emits: {},
    props: {
        title: {
            type: String,
            default: "", // 空字符串表示使用配置的默认标题
        },
        homePath: {
            type: String,
            default: "/"
        }
    },
});
export default (await import('vue')).defineComponent({
    setup() {
        return {};
    },
    emits: {},
    props: {
        title: {
            type: String,
            default: "", // 空字符串表示使用配置的默认标题
        },
        homePath: {
            type: String,
            default: "/"
        }
    },
});
; /* PartiallyEnd: #4569/main.vue */
