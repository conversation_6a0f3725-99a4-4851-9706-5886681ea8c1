import{d as T,u as V,l as A,c as r,o as c,g as e,b as s,I as F,F as g,e as f,t as n,j as H,_ as E,a as U,r as h,i as j,w,f as B,n as b,h as m,L as q}from"./index-BE8XLQ59.js";import{B as J}from"./index-jIY073my.js";import{C as O,a as P,G as Q}from"./index-BJjcaqGM.js";import{G as W,a as X}from"./GlobalHeader-lL88u8sR.js";import{A as Y,g as Z}from"./ArticleGridSection-Lg8Nwmv4.js";import{s as R}from"./function-call-BUl5915X.js";import"./index-B27ddgxZ.js";import"./index-sbxbYdRt.js";import"./GridCard-D-EgbodX.js";const ee={class:"section-container"},ae={class:"section-header"},te={class:"section-title"},oe={class:"healthy-articles"},se=["onClick"],ne={class:"card-image-wrapper"},ce=["src","alt"],le={class:"card-content"},re={class:"card-title"},ie={class:"card-summary"},de={class:"card-meta"},_e={class:"meta-left"},ue={class:"author"},he={class:"meta-right"},me={class:"date"},pe=["onClick"],ve={class:"card-image-wrapper"},ge=["src","alt"],fe={class:"card-content"},ke={class:"card-title"},ye={class:"card-summary"},Ce={class:"card-meta"},we={class:"meta-left"},be={class:"author"},De={class:"meta-right"},$e={class:"date"},He=["onClick"],xe={class:"card-image-wrapper"},Ie=["src","alt"],Ne={class:"card-content"},ze={class:"card-title"},Ge={class:"card-summary"},Be={class:"card-meta"},Re={class:"meta-left"},Te={class:"author"},Ve={class:"meta-right"},Ae={class:"date"},Fe=T({__name:"Healthy",props:{cultureHealthyData:{},knowledgeHealthyData:{},caseHealthyData:{}},setup(L){const p=V(),D=l=>{p.push({name:"CultureDetail",params:{id:l.id}})},_=l=>{p.push({name:"TcmKnowledgeDetail",params:{id:l.id}})},k=l=>{p.push({name:"CasesDetail",params:{id:l.id}})};return A(()=>{}),(l,y)=>{const u=F;return c(),r("div",ee,[e("div",ae,[e("div",te,[s(u,{name:"heart-o",color:"#ff6b35",size:"18"}),y[0]||(y[0]=e("span",null,"养生专区",-1))])]),e("div",oe,[(c(!0),r(g,null,f(l.cultureHealthyData,a=>(c(),r("div",{class:"knowledge-card",key:"culture-"+a.id,onClick:C=>D(a)},[e("div",ne,[e("img",{src:a.thumbnail,alt:a.name,class:"card-image"},null,8,ce)]),e("div",le,[e("h3",re,n(a.name),1),e("p",ie,n(a.desc),1),e("div",de,[e("div",_e,[e("span",ue,n(a.creater),1)]),e("div",he,[e("span",me,[s(u,{name:"clock-o",size:"12"}),H(" "+n(a.create_time),1)])])])])],8,se))),128)),(c(!0),r(g,null,f(l.knowledgeHealthyData,a=>(c(),r("div",{class:"knowledge-card",key:"knowledge-"+a.id,onClick:C=>_(a)},[e("div",ve,[e("img",{src:a.thumbnail,alt:a.name,class:"card-image"},null,8,ge)]),e("div",fe,[e("h3",ke,n(a.name),1),e("p",ye,n(a.desc),1),e("div",Ce,[e("div",we,[e("span",be,n(a.creater),1)]),e("div",De,[e("span",$e,[s(u,{name:"clock-o",size:"12"}),H(" "+n(a.create_time),1)])])])])],8,pe))),128)),(c(!0),r(g,null,f(l.caseHealthyData,a=>(c(),r("div",{class:"knowledge-card",key:"case-"+a.id,onClick:C=>k(a)},[e("div",xe,[e("img",{src:a.thumbnail,alt:a.name,class:"card-image"},null,8,Ie)]),e("div",Ne,[e("h3",ze,n(a.name),1),e("p",Ge,n(a.desc),1),e("div",Be,[e("div",Re,[e("span",Te,n(a.creater),1)]),e("div",Ve,[e("span",Ae,[s(u,{name:"clock-o",size:"12"}),H(" "+n(a.create_time),1)])])])])],8,He))),128))])])}}}),Le=E(Fe,[["__scopeId","data-v-a72998ff"]]),Se={class:"knowledge-container"},Ke={class:"section-container"},Me={class:"section-header"},Ee={class:"section-title"},Ue=["onClick"],je=["onClick"],qe={key:0,class:"loading-container"},aa=T({__name:"index",setup(L){const p=V(),D=U(),_=h(!1),k=h(""),l=h(!1),y=h([{icon:"photo-o",text:"科教图文",bgColor:"#fff0f0",iconColor:"#ff6b35",route:"/education-media",routeName:"EducationMedia"},{icon:"book-o",text:"中医知识",bgColor:"#e8f4ff",iconColor:"#4b8bf4",route:"/tcm-knowledge",routeName:"TcmKnowledge"}]),u=h([{icon:"gem-o",text:"中医文化",bgColor:"#f0e6ff",iconColor:"#7c3aed",route:"/culture",routeName:"Culture"},{icon:"records",text:"中医案例",bgColor:"#e6f7ef",iconColor:"#059669",route:"/cases",routeName:"Cases"},{icon:"video-o",text:"视频宣传",bgColor:"#ffebe6",iconColor:"#ff8c69",route:"/video",routeName:"Video"}]),a=h({culture_best_data:[],knowledge_best_data:[],case_best_data:[],culture_healthy_data:[],knowledge_healthy_data:[],case_healthy_data:[]}),C=async()=>{if(!_.value)try{_.value=!0,k.value="";const t=await Z({source:"knowledge"});a.value=t.items,console.log("获取到的文章列表数据:",a.value)}catch(t){console.error("获取文章列表失败:",t),k.value="获取文章列表失败，请稍后再试"}finally{_.value=!1,l.value=!1}},i=t=>D.name===t.routeName,x=t=>i(t)?"#fff":t.iconColor,I=t=>i(t)?{background:`linear-gradient(135deg, ${t.iconColor}, ${t.iconColor}dd)`}:{backgroundColor:t.bgColor},N=t=>{console.log(`点击了功能项: ${t.text}, 路由路径: ${t.route}`),t.route?p.push(t.route).catch(d=>{console.error("路由跳转失败:",d),R(`跳转到${t.text}失败，请稍后再试`)}):R(`${t.text}功能开发中`)};return A(()=>{C()}),(t,d)=>{const $=F,z=P,G=Q,S=q,K=J;return c(),r("div",Se,[d[2]||(d[2]=e("meta",{name:"viewport",content:"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no"},null,-1)),s(W,{title:"中医知识"}),s(O,{position:"2"}),e("div",Ke,[e("div",Me,[e("div",Ee,[s($,{name:"apps-o",color:"#4b8bf4",size:"18"}),d[0]||(d[0]=e("span",null,"功能导航",-1))])]),s(G,{"column-num":2,border:!1,gutter:10,class:"function-grid first-row"},{default:w(()=>[(c(!0),r(g,null,f(y.value,(o,v)=>(c(),B(z,{key:v,class:"animate__animated animate__fadeInUp",style:b({animationDelay:v*.05+"s"})},{default:w(()=>[e("div",{class:m(["grid-item-content",{active:i(o)}]),onClick:M=>N(o)},[e("div",{class:m(["icon-wrapper",{active:i(o)}]),style:b(I(o))},[s($,{name:o.icon,color:x(o),size:"24"},null,8,["name","color"])],6),e("span",{class:m(["grid-text",{active:i(o)}])},n(o.text),3)],10,Ue)]),_:2},1032,["style"]))),128))]),_:1}),s(G,{"column-num":3,border:!1,gutter:10,class:"function-grid second-row"},{default:w(()=>[(c(!0),r(g,null,f(u.value,(o,v)=>(c(),B(z,{key:v,class:"animate__animated animate__fadeInUp",style:b({animationDelay:(v+2)*.05+"s"})},{default:w(()=>[e("div",{class:m(["grid-item-content",{active:i(o)}]),onClick:M=>N(o)},[e("div",{class:m(["icon-wrapper",{active:i(o)}]),style:b(I(o))},[s($,{name:o.icon,color:x(o),size:"24"},null,8,["name","color"])],6),e("span",{class:m(["grid-text",{active:i(o)}])},n(o.text),3)],10,je)]),_:2},1032,["style"]))),128))]),_:1})]),s(Y,{title:"精品文章",icon:"description-o",showMore:!1,cultureData:a.value.culture_best_data,knowledgeData:a.value.knowledge_best_data,caseData:a.value.case_best_data},null,8,["cultureData","knowledgeData","caseData"]),s(Le,{cultureHealthyData:a.value.culture_healthy_data,knowledgeHealthyData:a.value.knowledge_healthy_data,caseHealthyData:a.value.case_healthy_data},null,8,["cultureHealthyData","knowledgeHealthyData","caseHealthyData"]),_.value?(c(),r("div",qe,[s(S,{type:"spinner",color:"#1989fa"}),d[1]||(d[1]=e("p",null,"加载中...",-1))])):j("",!0),s(K,{right:"16",bottom:"80"}),s(X)])}}});export{aa as default};
