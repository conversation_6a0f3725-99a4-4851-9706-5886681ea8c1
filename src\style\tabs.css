/* 分类页Tab样式优化 - 限制作用范围 */

/* 只对带有category-tabs类的容器生效 */
.category-tabs-container {
  /* Tab容器样式 */
}

.category-tabs-container :deep(.van-tabs__wrap) {
  background-color: #fff !important;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05) !important;
  position: sticky !important;
  top: 0 !important;
  z-index: 999 !important;
  will-change: transform !important;
  transform: translateZ(0) !important;
  width: 100% !important;
}

/* Tab导航区域 */
.category-tabs-container :deep(.van-tabs__nav) {
  background-color: #fff !important;
  padding: 2px 0 !important;
}

/* 分类标签容器 */
.category-tabs-container :deep(.category-tabs) {
  position: sticky !important;
  top: 0 !important;
  z-index: 999 !important;
  background: #fff !important;
  will-change: transform !important;
  transform: translateZ(0) !important;
}

/* Vant Tabs组件本身 */
.category-tabs-container :deep(.van-tabs) {
  position: relative !important;
  background: #fff !important;
}

/* 标签项基础样式 */
.category-tabs-container :deep(.van-tab) {
  color: #666 !important;
  font-weight: 500 !important;
  font-size: 13px !important;
  padding: 4px 8px !important;
  margin: 0 1px !important;
  border-radius: 8px !important;
  background-color: #f8f9fa !important;
  transition: all 0.2s ease !important;
  border: 1px solid transparent !important;
  position: relative !important;
  min-width: auto !important;
}

/* 标签项悬停效果 */
.category-tabs-container :deep(.van-tab:hover) {
  background-color: #e6f4ff !important;
  color: #1989fa !important;
}

/* 选中的标签项样式 - 蓝色背景 */
.category-tabs-container :deep(.van-tab--active) {
  color: #fff !important;
  font-weight: 600 !important;
  background: #1989fa !important;
  border: 1px solid #1989fa !important;
  box-shadow: 0 1px 3px rgba(25, 137, 250, 0.15) !important;
  padding: 3px 6px !important;
}



/* 隐藏默认的下划线指示器 */
.category-tabs-container :deep(.van-tabs__line) {
  display: none !important;
}

/* Tab内容区域样式 */
.category-tabs-container :deep(.van-tabs__content) {
  position: relative !important;
  z-index: 1 !important;
  background-color: #f8f9fa !important;
  min-height: calc(100vh - 200px) !important;
}

/* Tab面板样式 */
.category-tabs-container :deep(.van-tab__panel) {
  padding: 0 !important;
}

/* 响应式设计 - 小屏幕优化 */
@media (max-width: 768px) {
  .category-tabs-container :deep(.van-tab) {
    padding: 3px 6px !important;
    font-size: 12px !important;
    margin: 0 1px !important;
  }
  
  .category-tabs-container :deep(.van-tab--active) {
    padding: 2px 4px !important;
  }
  
  .category-tabs-container :deep(.van-tabs__wrap) {
    padding: 0 4px !important;
  }
}

/* 滚动条样式优化 */
.category-tabs-container :deep(.van-tabs__nav--scrollable) {
  scrollbar-width: none !important;
  -ms-overflow-style: none !important;
}

.category-tabs-container :deep(.van-tabs__nav--scrollable::-webkit-scrollbar) {
  display: none !important;
}

/* 左右渐变遮罩效果 */
.category-tabs-container :deep(.van-tabs__nav--scrollable::before),
.category-tabs-container :deep(.van-tabs__nav--scrollable::after) {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  width: 20px !important;
  height: 100% !important;
  pointer-events: none !important;
  z-index: 1 !important;
}

.category-tabs-container :deep(.van-tabs__nav--scrollable::before) {
  left: 0 !important;
  background: linear-gradient(to right, #fff, transparent) !important;
}

.category-tabs-container :deep(.van-tabs__nav--scrollable::after) {
  right: 0 !important;
  background: linear-gradient(to left, #fff, transparent) !important;
} 