import{v as c,G as l,x as H,z as F,M as G,D as K,aw as V,E as $,K as W,C as Y,A as q,d as J,u as Q,r as w,k as T,y as R,c as d,o as r,b as i,g as a,L as X,w as p,H as Z,j as b,t as g,i as y,I as ee,f as S,F as te,e as ae,h as se,_ as oe}from"./index-BE8XLQ59.js";import{T as ne}from"./index-CAfqjps3.js";import{E as re}from"./index-B27ddgxZ.js";function ie(o){return c(l(H.LEADER,o))}function ce(o){return c(l(F.DOCTOR,o))}function le(o){return c(l(G.MEDICINE,o))}function de(o){return c(l(K.DEPT,o))}function ue(o){return c(l(V.MAINDEPT,o))}function _e(o){return c(l($.EDUCATION,o))}function me(o){return c(l(<PERSON><PERSON>,o))}function pe(o){return c(l(Y.C<PERSON>UR<PERSON>,o))}function ge(o){return c(l(q.CASE,o))}const ve={class:"leader-detail-card"},he={key:0,class:"loading-container"},fe={key:1,class:"error-container"},be={class:"error-text"},ye={key:2,class:"content"},De={class:"thumbnail-section"},ke={class:"section-header"},Ie={class:"section-title"},we={class:"thumbnail-content"},Ee={class:"large-avatar"},Le=["src","alt"],Ce={class:"avatar-overlay"},Te={class:"info-section"},Re={class:"meta-container"},Se={key:0,class:"meta-item"},Ue={class:"meta-text"},Ae={class:"meta-item"},Me={class:"meta-text"},Ne={class:"tags-container"},xe={key:0,class:"description-section"},ze={class:"section-header"},Be={class:"section-content"},Oe=["innerHTML"],je={key:1,class:"richtext-section"},Pe={class:"section-header"},He={class:"section-content"},Fe=["innerHTML"],Ge={class:"back-button-section"},U="/images/default-avatar.svg",Ke=J({__name:"LeaderDetailCard",props:{leaderId:{default:""},leaderInfo:{default:void 0},showRetryButton:{type:Boolean,default:!0},apiType:{default:"leader"}},emits:["retry","loaded","error","goBack"],setup(o,{expose:A,emit:M}){const n=o,h=M,u=Q(),D=w(!1),_=w(""),s=w({id:0,name:"",job:"",desc:"",thumbnail:"",cat_display:"",tags:[],content:"",create_time:"",specialties:[],experience:[],publications:[],achievements:[],research_directions:[]}),N=T(()=>n.leaderInfo||s.value),k=async()=>{if(!n.leaderId){_.value="ID无效";return}try{D.value=!0,_.value="";let t,e;switch(n.apiType){case"doctor":t=await ce(n.leaderId),e=t.famousdoctor;break;case"medicine":t=await le(n.leaderId),e=t.specialpreparations;break;case"dept":t=await de(n.leaderId),e=t.characterdepart;break;case"mainDept":t=await ue(n.leaderId),e=t.maindept;break;case"education":t=await _e(n.leaderId),e=t.drumbeating;break;case"knowledge":t=await me(n.leaderId),e=t.knowledge;break;case"culture":t=await pe(n.leaderId),e=t.culture;break;case"case":t=await ge(n.leaderId),e=t.case;break;case"leader":default:t=await ie(n.leaderId),e=t.leader;break}if(!e)throw new Error("API返回数据格式不正确");const v=e.thumbnail?e.thumbnail.trim():"";s.value={id:e.id,name:e.name||"未知",job:e.job||"",desc:e.desc||"",thumbnail:v,cat_display:e.cat_display||e.category_name||"未分类",tags:e.tags||[],content:e.content||"",create_time:e.create_time||new Date().toISOString(),viewtimes_display:e.viewtimes_display||"0",specialties:[],experience:[],publications:[],achievements:[],research_directions:[]},console.log("API返回的原始数据:",e),console.log("create_time字段值:",e.create_time),console.log("处理后的leaderData:",s.value),h("loaded",s.value)}catch(t){console.error("获取详情失败:",t),_.value="获取详情失败，请稍后再试",h("error",_.value)}finally{D.value=!1}};R(()=>n.leaderId,t=>{t&&!n.leaderInfo&&k()},{immediate:!0}),R(()=>n.leaderInfo,t=>{t&&(s.value={...t})},{immediate:!0,deep:!0});const x=t=>{const e=t.target;e.src=U},z=t=>t.replace(/\r\n/g,"<br>").replace(/\n/g,"<br>"),B=t=>{if(!t)return"";try{const e=new Date(t);if(isNaN(e.getTime()))return t;const v=e.getFullYear(),f=String(e.getMonth()+1).padStart(2,"0"),I=String(e.getDate()).padStart(2,"0");return`${v}-${f}-${I} 08:00`}catch(e){return console.warn("时间格式化失败:",e),t}},O=t=>{const e=["tag-red","tag-orange","tag-yellow","tag-green","tag-cyan","tag-blue","tag-purple","tag-pink","tag-indigo","tag-teal"];return e[t%e.length]},j=()=>{h("retry"),n.leaderId&&k()},E=()=>{if(h("goBack"),window.history.length>1)u.go(-1);else switch(n.apiType){case"doctor":u.push("/doctor");break;case"medicine":u.push("/medicine");break;case"dept":u.push("/dept");break;case"mainDept":u.push("/main-dept");break;case"leader":default:u.push("/leader");break}};return A({refresh:k,leaderData:T(()=>N.value),goBack:E}),(t,e)=>{const v=X,f=Z,I=re,m=ee,L=ne;return r(),d("div",ve,[D.value?(r(),d("div",he,[i(v,{type:"spinner",color:"#1989fa",size:"24"}),e[0]||(e[0]=a("p",{class:"loading-text"},"加载中...",-1))])):_.value?(r(),d("div",fe,[i(I,{description:"加载失败",image:"error"},{description:p(()=>[a("p",be,g(_.value),1)]),default:p(()=>[i(f,{round:"",type:"primary",onClick:j,class:"retry-btn"},{default:p(()=>e[1]||(e[1]=[b(" 重试 ")])),_:1,__:[1]})]),_:1})])):(r(),d("div",ye,[a("div",De,[a("div",ke,[i(m,{name:"photo-o",size:"18",color:"#4b8bf4"}),a("h2",Ie,g(s.value.name),1)]),a("div",we,[a("div",Ee,[a("img",{src:s.value.thumbnail||U,alt:s.value.name,onError:x},null,40,Le),a("div",Ce,[i(m,{name:"user-o",size:"32",color:"#fff"})])])])]),a("div",Te,[a("div",Re,[s.value.create_time&&s.value.create_time.trim()?(r(),d("div",Se,[i(m,{name:"clock-o",size:"14",color:"#999"}),a("span",Ue,"发布时间："+g(B(s.value.create_time)),1)])):y("",!0),a("div",Ae,[i(m,{name:"eye-o",size:"14",color:"#999"}),a("span",Me,"浏览次数："+g(s.value.viewtimes_display||"0"),1)])]),a("div",Ne,[s.value.job&&s.value.job.trim()?(r(),S(L,{key:0,size:"large",round:"",class:"tag-item tag-blue"},{default:p(()=>[b(g(s.value.job),1)]),_:1})):y("",!0),(r(!0),d(te,null,ae(s.value.tags,(P,C)=>(r(),S(L,{key:C,size:"large",round:"",class:se(`tag-item ${O(C)}`)},{default:p(()=>[b(g(P),1)]),_:2},1032,["class"]))),128))])]),s.value.desc?(r(),d("div",xe,[a("div",ze,[i(m,{name:"info-o",size:"18",color:"#f5a623"}),e[2]||(e[2]=a("h2",{class:"section-title"},"基本信息",-1))]),a("div",Be,[a("div",{class:"desc-content",innerHTML:z(s.value.desc)},null,8,Oe)])])):y("",!0),s.value.content?(r(),d("div",je,[a("div",Pe,[i(m,{name:"notes-o",size:"18",color:"#722ed1"}),e[3]||(e[3]=a("h2",{class:"section-title"},"详细介绍",-1))]),a("div",He,[a("div",{class:"rich-content",innerHTML:s.value.content},null,8,Fe)])])):y("",!0),a("div",Ge,[i(f,{type:"primary",size:"large",round:"",block:"",icon:"arrow-left",onClick:E,class:"back-button"},{default:p(()=>e[4]||(e[4]=[b(" 返回上一级菜单 ")])),_:1,__:[4]})])]))])}}}),Ye=oe(Ke,[["__scopeId","data-v-fd69d2cb"]]);export{Ye as L};
