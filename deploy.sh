#!/bin/bash

# 中医智慧前端项目部署脚本
echo "开始部署中医智慧前端项目..."

# 检查Node.js环境
if ! command -v node &> /dev/null; then
    echo "错误: 未安装Node.js，请先安装Node.js 18+"
    exit 1
fi

# 检查npm
if ! command -v npm &> /dev/null; then
    echo "错误: 未安装npm"
    exit 1
fi

# 获取脚本所在目录
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
cd "$SCRIPT_DIR"

echo "当前目录: $(pwd)"

# 安装依赖
echo "安装项目依赖..."
npm ci

# 构建项目
echo "构建生产环境代码..."
npm run build

# 检查构建是否成功
if [ ! -d "dist" ]; then
    echo "错误: 构建失败，未找到dist目录"
    exit 1
fi

echo "构建成功! 生产文件位于 dist/ 目录"

# 询问部署方式
echo ""
echo "请选择部署方式:"
echo "1) 复制到web服务器目录"
echo "2) 使用Docker部署"
echo "3) 仅构建（已完成）"
read -p "请输入选项 (1-3): " choice

case $choice in
    1)
        read -p "请输入web服务器目录路径 (例如: /var/www/html): " web_dir
        if [ -d "$web_dir" ]; then
            echo "复制文件到 $web_dir ..."
            sudo cp -r dist/* "$web_dir/"
            echo "部署完成! 文件已复制到 $web_dir"
        else
            echo "错误: 目录 $web_dir 不存在"
            exit 1
        fi
        ;;
    2)
        if command -v docker &> /dev/null; then
            echo "使用Docker构建镜像..."
            docker build -t zyyfront:latest .
            echo "启动容器..."
            docker run -d --name zyyfront -p 80:80 --restart unless-stopped zyyfront:latest
            echo "Docker部署完成! 应用运行在 http://localhost"
        else
            echo "错误: 未安装Docker"
            exit 1
        fi
        ;;
    3)
        echo "仅构建完成，dist目录包含所有生产文件"
        ;;
    *)
        echo "无效选项"
        exit 1
        ;;
esac

echo ""
echo "部署完成! 🎉"
echo "项目特性:"
echo "- 响应式移动端设计"
echo "- API代理已配置"
echo "- 静态资源优化"
echo "- Vue Router单页面应用支持" 