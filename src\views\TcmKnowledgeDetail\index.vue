<template>
  <div class="knowledge-detail-page">
    <GlobalHeader title="中医知识详情" />
    <div class="detail-content">
      <LeaderDetailCard 
        :leader-id="knowledgeId" 
        api-type="knowledge"
        @loaded="handleLoaded"
        @error="handleError"
        @retry="handleRetry"
      />
    </div>
    <GlobalFooter />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { showToast } from 'vant';
import GlobalHeader from '../../components/GlobalHeader.vue';
import GlobalFooter from '../../components/GlobalFooter.vue';
import LeaderDetailCard from '../../components/LeaderDetailCard.vue';
import type { LeaderDetail } from '../LeaderDetail/api';

const route = useRoute();
const router = useRouter();
const knowledgeId = ref<string>('');

onMounted(() => {
  const id = Array.isArray(route.params.id) ? route.params.id[0] : route.params.id;
  if (id) {
    knowledgeId.value = id;
  } else {
    showToast('内容ID无效');
    router.back();
  }
});

const handleLoaded = (data: LeaderDetail) => {
  console.log('中医知识详情加载成功:', data);
};

const handleError = (error: string) => {
  console.error('中医知识详情加载失败:', error);
  showToast(error);
};

const handleRetry = () => {
  console.log('重试加载中医知识详情');
};
</script>

<style scoped>
.knowledge-detail-page {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f8f9fa;
}

.detail-content {
  flex: 1;
}
</style>