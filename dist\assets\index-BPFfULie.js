import{s as x,v as b,A as y,d as S,u as B,r as o,y as E,l as I,c as _,o as f,b as u,w as g,F as R,e as A,f as G,g as C,_ as L}from"./index-BE8XLQ59.js";import{T as F,a as V}from"./index-CmzFzWyS.js";import"./index-sbxbYdRt.js";import{G as z,a as D}from"./GlobalHeader-lL88u8sR.js";import{C as M}from"./CommonCardList-1yAjUs8b.js";import"./index-B27ddgxZ.js";import"./index-ouF_E0m0.js";import"./index-CAfqjps3.js";function N(){return b(y.CATEGORIES)}function U(r,n=1,t=10){return b(y.CASE,{cat:r,page:n,page_size:t})}function W(r,n=1,t=10){return x(()=>U(r,n,t))}const q={class:"news-page category-tabs-container"},H={class:"section-container"},O={class:"news-grid"},j=S({__name:"index",setup(r){const n=B(),t=o(!1),i=o(!1),d=o([]),v=o(1),l=o(0),h=o(5),m=o([]),w=async()=>{const e=await N();console.log("获取到领导分类sdfsdfsd:",e);const s=e.categories.map(a=>({id:a.value,name:a.label}));m.value=[...s]};E(l,e=>{v.value=1,d.value=[],i.value=!1,p()});const p=async()=>{var s;if(i.value||t.value)return;t.value=!0;const e=(s=m.value[l.value])==null?void 0:s.id;console.log("当前选中标签ID:",e);try{const a=await W(e,v.value,h.value);d.value.push(...a.results),v.value+=1,i.value=a.is_last_page===!0}catch(a){console.error("加载失败:",a)}finally{t.value=!1}},k=e=>{n.push({name:"CasesDetail",params:{id:e.id}})};return I(()=>{w(),p()}),(e,s)=>{const a=F,T=V;return f(),_("div",q,[u(z,{title:"中医案例"}),u(T,{active:l.value,"onUpdate:active":s[0]||(s[0]=c=>l.value=c),class:"category-tabs",sticky:"","sticky-offset-top":0,swipeable:""},{default:g(()=>[(f(!0),_(R,null,A(m.value,c=>(f(),G(a,{key:c.id,title:c.name},{default:g(()=>[C("div",H,[C("div",O,[u(M,{items:d.value,loading:t.value,finished:i.value,"use-infinite-scroll":!0,onLoadMore:p,onCardClick:k},null,8,["items","loading","finished"])])])]),_:2},1032,["title"]))),128))]),_:1},8,["active"]),u(D)])}}}),ee=L(j,[["__scopeId","data-v-97de6d70"]]);export{ee as default};
