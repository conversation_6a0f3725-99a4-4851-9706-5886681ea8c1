import{s as _,v as m,a5 as f,d as g,u as v,r as s,l as h,c as C,o as N,b as i,g as c,_ as w}from"./index-BE8XLQ59.js";import{G as L,a as y}from"./GlobalHeader-lL88u8sR.js";import{C as H}from"./CommonCardList-1yAjUs8b.js";import"./index-ouF_E0m0.js";import"./index-CAfqjps3.js";import"./index-B27ddgxZ.js";function S(a){return m(f.NEWS,a)}function k(a){return _(()=>S(a))}const R={class:"news-page"},W={class:"section-container"},b={class:"news-grid"},x=g({__name:"list",setup(a){const u=v(),t=s(!1),o=s(!1),l=s([]),n=s(1),d=s(5),r=async()=>{if(!(o.value||t.value)){t.value=!0;try{const e=await k({page:n.value,page_size:d.value});console.log("加载新闻数据:",e),console.log("is_last_page:",e.is_last_page),l.value.push(...e.results),n.value+=1,o.value=e.is_last_page===!0}catch(e){console.error("加载新闻失败:",e)}finally{t.value=!1}}},p=e=>{u.push({name:"HospitalNewsDetail",params:{id:e.id}})};return h(()=>{r()}),(e,B)=>(N(),C("div",R,[i(L,{title:"医院新闻"}),c("div",W,[c("div",b,[i(H,{items:l.value,loading:t.value,finished:o.value,"use-infinite-scroll":!0,onLoadMore:r,onCardClick:p},null,8,["items","loading","finished"])])]),i(y)]))}}),q=w(x,[["__scopeId","data-v-07051297"]]);export{q as default};
