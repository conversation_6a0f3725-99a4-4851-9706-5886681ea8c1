import { get, post, requestWithRetry } from '../../api/request';
import { VIDEO_URLS, buildUrl } from '../../api/urls';

/**
 * 获取领导详情
 * @param id 领导ID
 * @returns 领导详情数据
 */
export function getVideoDetail(id: string | number) {
  return get<VideoDetailResponse>(buildUrl(VIDEO_URLS.VIDEO, id));
}

/**
 * 带重试功能的获取领导详情
 * 在网络不稳定情况下使用
 * @param id 领导ID
 * @returns 领导详情数据
 */
export function getVideoDetailWithRetry(id: string | number) {
  return requestWithRetry<VideoDetailResponse>(() => getVideoDetail(id));
}


// API返回的领导详情响应接口
export interface VideoDetailResponse {
  videos: {
    id: number;          
    name: string;         
    duration: string;          
    desc: string;         // 描述/简介
    thumbnail: string;    // 缩略图/头像
    cat_display: string;          // 分类ID
    video: string;
    content: string;      // HTML格式的详细内容
    creater: string;      // 作者
    create_time: string; // 发布时间
    tags: string[];      // 标签列表
    is_featured: boolean; // 是否为推荐视频
    video_type: 'promotional' | 'educational' | 'documentary' | 'interview'; // 视频类型
    duration_seconds: number; // 视频时长（秒）
  }
}

// 领导详情接口（组件内部使用）
export interface VideoDetail {
  id: number;          
    name: string;         
    duration: string;          
    desc: string;         // 描述/简介
    thumbnail: string;    // 缩略图/头像
    video: string;
    cat_display: string;          // 分类ID
    content: string;      // HTML格式的详细内容
    creater: string;      // 作者
    create_time: string; // 发布时间
    tags: string[];
}