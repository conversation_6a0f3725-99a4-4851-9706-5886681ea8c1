import { get, requestWithRetry } from '../../api/request';
import { HOSPITAL_NEWS_URLS, buildUrl } from '../../api/urls';


export function getHospitalNewsDetail(id: string | number) {
  return get<HospitalNewsDetailResponse>(buildUrl(HOSPITAL_NEWS_URLS.NEWS, id));
}

export function getHospitalNewsDetailWithRetry(id: string | number) {
  return requestWithRetry<HospitalNewsDetailResponse>(() => getHospitalNewsDetail(id));
}

export interface HospitalNewsDetailResponse {
  hospital_news: {
    id: number;          
    name: string;          
    desc: string;        
    thumbnail: string;          
    content: string; 
    create_time: string;    
    viewtimes_display: string;
    cat_display?: string;  // 分类显示名称
    tags?: string[];       // 标签数组
}
}
export interface HospitalNewsDetail {
    id: number;          
    name: string;          
    desc: string;        
    thumbnail: string;          
    content: string; 
    create_time: string;    
    viewtimes_display: string;
    cat_display?: string;  // 分类显示名称
    tags?: string[];       // 标签数组
}