{"name": "h5-project", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "build:prerender": "npm run build && node scripts/prerender.js", "preview": "vite preview", "prerender": "node scripts/prerender.js"}, "dependencies": {"@vant/auto-import-resolver": "^1.3.0", "animate.css": "^4.1.1", "axios": "^1.9.0", "chokidar": "^3.5.3", "html2canvas": "^1.4.1", "pinia": "^3.0.3", "qrcode": "^1.5.4", "unplugin-auto-import": "^19.3.0", "unplugin-vue-components": "^28.7.0", "vant": "^4.9.19", "vue": "^3.5.16", "vue-qr": "^4.0.9", "vue-router": "^4.5.1"}, "devDependencies": {"@types/node": "^22.15.30", "@types/qrcode": "^1.5.5", "@vitejs/plugin-vue": "^5.2.3", "@vue/tsconfig": "^0.7.0", "typescript": "~5.8.3", "vite": "^6.3.5", "vue-tsc": "^2.2.8"}}