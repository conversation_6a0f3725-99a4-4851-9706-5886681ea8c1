import{d as g,m as v,k as o,c as f,o as h,g as t,b,I as y,t as i,_ as w,r as k,l as S,i as $,h as C}from"./index-BE8XLQ59.js";const H={class:"global-footer"},N={class:"footer-content"},Y={class:"footer-logo"},x={class:"brand-name"},B={class:"footer-slogan"},G={class:"footer-copy"},I=g({__name:"GlobalFooter",props:{brand:{type:String,default:""},slogan:{type:String,default:""},icon:{type:String,default:"medic-o"},startYear:{type:Number,default:0}},setup(d){const e=d,{getHospitalName:c,getHospitalSlogan:p,getCopyrightText:r}=v(),l=o(()=>e.brand||c()),u=o(()=>e.slogan||p()),a=o(()=>e.icon),_=o(()=>{if(e.startYear>0){const n=new Date().getFullYear(),m=e.startYear===n?`${e.startYear}`:`${e.startYear}-${n}`,s=l.value;return`© ${m} ${s} 版权所有`}else return r()});return(n,m)=>{const s=y;return h(),f("div",H,[t("div",N,[t("div",Y,[b(s,{name:a.value,size:"22",color:"#2b72f6"},null,8,["name"]),t("span",x,i(l.value),1)]),t("p",B,i(u.value),1),t("p",G,i(_.value),1)])])}}}),V=w(I,[["__scopeId","data-v-6417884f"]]),P={class:"global-header"},z={class:"header-content"},F=g({__name:"GlobalHeader",props:{title:{type:String,default:""},homePath:{type:String,default:"/"}},emits:["left-click"],setup(d,{emit:e}){const c=d,p=e,r=k(""),{getHospitalName:l}=v(),u=o(()=>c.title||l());S(()=>{r.value=window.location.pathname});const a=o(()=>!(window.location.pathname===c.homePath||window.location.pathname===r.value)&&window.history.length>1),_=()=>{a.value?window.history.back():p("left-click")};return(n,m)=>{const s=y;return h(),f("div",P,[t("div",z,[a.value?(h(),f("div",{key:0,class:"back-button",onClick:_},[b(s,{name:"arrow-left",size:"18"})])):$("",!0),t("h1",{class:C(["page-title",{"no-back-button":!a.value}])},i(u.value),3)])])}}}),D=w(F,[["__scopeId","data-v-fa784034"]]);export{D as G,V as a};
