import{d as v,u as k,a as b,r as l,k as c,y as x,c as L,o as g,g as y,b as r,_ as B}from"./index-BE8XLQ59.js";import{B as R}from"./index-jIY073my.js";import{G as C,a as w}from"./GlobalHeader-lL88u8sR.js";import{L as D}from"./LeaderDetailCard-VVs8AxZp.js";import"./index-CAfqjps3.js";import"./index-B27ddgxZ.js";const E={class:"leader-detail-container"},G=v({__name:"index",setup(A,{expose:d}){const s=k(),a=b(),o=l(),i=c(()=>Array.isArray(a.params.id)?a.params.id[0]:a.params.id),t=l(null),m=c(()=>{var e;return(e=t.value)!=null&&e.name?`${t.value.name} - 领导详情`:"领导详情"}),u=()=>{document.referrer&&document.referrer!==location.href?s.back():s.replace("/home")},p=e=>{t.value=e,console.log("领导详情加载完成:",e)},f=e=>{console.error("领导详情加载失败:",e)},_=()=>{console.log("重试加载领导详情")};return x(()=>a.params.id,e=>{e&&console.log("领导ID变化，重新加载:",e)}),d({refresh:()=>{o.value&&o.value.refresh()}}),(e,n)=>{const h=R;return g(),L("div",E,[n[0]||(n[0]=y("meta",{name:"viewport",content:"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no"},null,-1)),r(C,{title:m.value,onLeftClick:u},null,8,["title"]),r(D,{"leader-id":i.value,onLoaded:p,onError:f,onRetry:_,ref_key:"leaderCardRef",ref:o},null,8,["leader-id"]),r(h,{right:"16",bottom:"80"}),r(w)])}}}),j=B(G,[["__scopeId","data-v-06f7c788"]]);export{j as default};
