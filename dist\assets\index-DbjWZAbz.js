import{d as l,a as i,u,r as p,l as m,c as _,o as f,b as a,g as h,_ as b}from"./index-BE8XLQ59.js";import{G as v,a as x}from"./GlobalHeader-lL88u8sR.js";import{L as y}from"./LeaderDetailCard-VVs8AxZp.js";import{s}from"./function-call-BUl5915X.js";import"./index-CAfqjps3.js";import"./index-B27ddgxZ.js";const g={class:"culture-detail-page"},L={class:"detail-content"},R=l({__name:"index",setup(k){const o=i(),r=u(),t=p("");m(()=>{const e=Array.isArray(o.params.id)?o.params.id[0]:o.params.id;e?t.value=e:(s("内容ID无效"),r.back())});const n=e=>{console.log("中医文化详情加载成功:",e)},c=e=>{console.error("中医文化详情加载失败:",e),s(e)},d=()=>{console.log("重试加载中医文化详情")};return(e,B)=>(f(),_("div",g,[a(v,{title:"中医文化详情"}),h("div",L,[a(y,{"leader-id":t.value,"api-type":"culture",onLoaded:n,onError:c,onRetry:d},null,8,["leader-id"])]),a(x)]))}}),N=b(R,[["__scopeId","data-v-e9cec41b"]]);export{N as default};
