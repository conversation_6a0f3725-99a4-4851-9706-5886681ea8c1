<template>
  <div class="global-header">
    <div class="header-content">
      <!-- 只有不在首页时才显示返回按钮 -->
      <template v-if="showBackButton">
        <div class="back-button" @click="handleLeftClick">
          <van-icon name="arrow-left" size="18" />
        </div>
      </template>
      
      <!-- 标题始终居中显示 -->
      <h1 class="page-title" :class="{ 'no-back-button': !showBackButton }">{{ displayTitle }}</h1>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, defineProps } from 'vue';
import { useSystemConfig } from '../composables/useSystemConfig';

// 正确定义 props
const props = defineProps({
  title: {
    type: String,
    default: "",  // 空字符串表示使用配置的默认标题
  },
  homePath: {
    type: String,
    default: "/"
  }
});

const emit = defineEmits(["left-click"]);
const initialPath = ref('');

// 使用系统配置
const { getHospitalName } = useSystemConfig();

// 计算显示的标题
const displayTitle = computed(() => {
  // 如果props.title有值，使用props.title；否则使用配置的医院名称
  return props.title || getHospitalName();
});

onMounted(() => {
  // 记录组件挂载时的路径作为首页参考
  initialPath.value = window.location.pathname;
});

// 计算是否显示返回按钮
const showBackButton = computed(() => {
  // 检查当前路径是否为首页
  const isHomePage = window.location.pathname === props.homePath || 
                     window.location.pathname === initialPath.value;
  
  // 不在首页且有历史记录时显示返回按钮
  return !isHomePage && window.history.length > 1;
});

const handleLeftClick = () => {
  if (showBackButton.value) {
    // 只有当按钮显示时才执行返回操作
    window.history.back();
  } else {
    // 首页或无法返回时，触发自定义事件
    emit("left-click");
  }
};
</script>

<style scoped>
.global-header {
  position: sticky;
  top: 0;
  z-index: 1000;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  box-shadow: 0 2px 20px rgba(102, 126, 234, 0.15);
  width: 100%;
  transition: all 0.3s ease;
}

.global-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  pointer-events: none;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12px 20px;
  min-height: 56px;
  box-sizing: border-box;
  position: relative;
}

.page-title {
  font-size: 18px;
  font-weight: 600;
  color: #ffffff;
  margin: 0;
  text-align: center;
  flex: none;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 70%;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  letter-spacing: 0.5px;
  font-family: 'STKaiti', 'KaiTi', 'KaiTi_GB2312', 'DFKai-SB', '标楷体', 'BiauKai', 'STFangsong', 'FangSong', 'FangSong_GB2312', 'STSong', 'SimSun', serif;
  /* 毛笔字效果 */
  font-weight: 700;
  text-shadow: 
    0 1px 0 rgba(0, 0, 0, 0.3),
    0 2px 0 rgba(0, 0, 0, 0.2),
    0 3px 0 rgba(0, 0, 0, 0.1),
    0 4px 8px rgba(0, 0, 0, 0.3),
    0 0 10px rgba(255, 255, 255, 0.2);
  /* 增加笔触感 */
  filter: drop-shadow(1px 1px 2px rgba(0, 0, 0, 0.3));
  /* 字体变形，模拟毛笔书写的不规则性 */
  transform: perspective(500px) rotateX(2deg);
  letter-spacing: 1px;
}

.back-button {
  position: absolute;
  left: 16px;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.back-button:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: translateY(-1px) scale(1.05);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border-color: rgba(255, 255, 255, 0.3);
}

.back-button:active {
  transform: translateY(0) scale(0.98);
  background: rgba(255, 255, 255, 0.2);
}

.back-button .van-icon {
  color: #ffffff;
  font-size: 18px;
  font-weight: 600;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

/* 当没有返回按钮时，标题可以占用更多空间 */
.page-title.no-back-button {
  max-width: 85%;
}

/* 响应式优化 */
@media (max-width: 480px) {
  .header-content {
    padding: 10px 16px;
    min-height: 52px;
  }
  
  .page-title {
    font-size: 16px;
    max-width: 65%;
    /* 移动端毛笔字效果调整 */
    text-shadow: 
      0 1px 0 rgba(0, 0, 0, 0.3),
      0 2px 0 rgba(0, 0, 0, 0.2),
      0 3px 6px rgba(0, 0, 0, 0.3),
      0 0 8px rgba(255, 255, 255, 0.2);
    transform: perspective(400px) rotateX(1.5deg);
  }
  
  .back-button {
    width: 32px;
    height: 32px;
    left: 12px;
  }
  
  .back-button .van-icon {
    font-size: 16px;
  }
}

@media (max-width: 320px) {
  .header-content {
    padding: 8px 12px;
    min-height: 48px;
  }
  
  .page-title {
    font-size: 15px;
    max-width: 60%;
    /* 小屏幕毛笔字效果调整 */
    text-shadow: 
      0 1px 0 rgba(0, 0, 0, 0.3),
      0 2px 4px rgba(0, 0, 0, 0.3),
      0 0 6px rgba(255, 255, 255, 0.2);
    transform: perspective(300px) rotateX(1deg);
    letter-spacing: 0.8px;
  }
  
  .back-button {
    width: 30px;
    height: 30px;
    left: 10px;
  }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .global-header {
    background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.3);
  }
  
  .global-header::before {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.02) 100%);
  }
}

/* 滚动时的动画效果 */
.global-header.scrolled {
  box-shadow: 0 4px 25px rgba(102, 126, 234, 0.2);
}

/* 加载动画 */
@keyframes headerSlideIn {
  from {
    transform: translateY(-100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.global-header {
  animation: headerSlideIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}
</style>
