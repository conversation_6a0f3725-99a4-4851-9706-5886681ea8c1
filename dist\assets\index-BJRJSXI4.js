import{d as l,a as i,u as p,r as _,l as u,c as m,o as f,b as a,g as h,_ as v}from"./index-BE8XLQ59.js";import{G as x,a as b}from"./GlobalHeader-lL88u8sR.js";import{L as g}from"./LeaderDetailCard-VVs8AxZp.js";import{s as t}from"./function-call-BUl5915X.js";import"./index-CAfqjps3.js";import"./index-B27ddgxZ.js";const L={class:"doctor-detail-page"},R={class:"detail-content"},k=l({__name:"index",setup(y){const s=i(),r=p(),e=_("");u(()=>{e.value=s.params.id,e.value||(t("医生ID无效"),r.back())});const d=o=>{console.log("名中医详情加载成功:",o)},n=o=>{console.error("名中医详情加载失败:",o),t(o)},c=()=>{console.log("重试加载名中医详情")};return(o,B)=>(f(),m("div",L,[a(x,{title:"名中医详情"}),h("div",R,[a(g,{"leader-id":e.value,"api-type":"doctor",onLoaded:d,onError:n,onRetry:c},null,8,["leader-id"])]),a(b)]))}}),V=v(k,[["__scopeId","data-v-f892e08d"]]);export{V as default};
