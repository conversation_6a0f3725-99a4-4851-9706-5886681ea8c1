import axios from 'axios';
import { showToast, showDialog } from 'vant';
import 'vant/es/toast/style'; // ✅ 必须引入 Toast 样式
import 'vant/es/dialog/style'; // ✅ 必须引入 Dialog 样式
// 从环境变量获取配置
const baseURL = import.meta.env.VITE_API_BASE_URL || '';
const timeout = parseInt(import.meta.env.VITE_API_TIMEOUT) || 10000;
const currentEnv = import.meta.env.MODE || 'development';
// 创建axios实例
const request = axios.create({
    baseURL,
    timeout, // 使用环境变量配置的超时时间
    headers: {
        'Content-Type': 'application/json'
    }
});
// 输出当前环境和baseURL，用于调试
console.log('当前环境:', currentEnv);
console.log('API baseURL:', baseURL);
console.log('API timeout:', timeout);
// 错误消息映射
const ERROR_MESSAGES = {
    'Network Error': '网络连接失败，请检查您的网络',
    'timeout of 10000ms exceeded': '请求超时，请稍后再试',
    '404': '请求的资源不存在',
    '500': '服务器错误，请稍后再试',
    '403': '没有权限访问该资源',
    '401': '登录已过期，请重新登录'
};
// 默认错误消息
const DEFAULT_ERROR_MESSAGE = '请求失败，请稍后再试';
// 请求拦截器
request.interceptors.request.use((config) => {
    // 在发送请求之前做些什么，例如添加token
    const token = localStorage.getItem('token');
    if (token) {
        config.headers['Authorization'] = `Bearer ${token}`;
    }
    return config;
}, (error) => {
    // 对请求错误做些什么
    console.error('请求拦截器错误:', error);
    return Promise.reject(error);
});
// 响应拦截器
request.interceptors.response.use((response) => {
    const res = response.data;
    // 检查HTTP状态码是否成功
    if (response.status >= 200 && response.status < 300) {
        // 直接返回响应数据，支持新的分页格式和旧的code格式
        return res;
    }
    // HTTP状态码不成功时的处理
    const errorMessage = res.message || DEFAULT_ERROR_MESSAGE;
    // 只在开发环境弹toast，生产环境只输出console.warn
    if (import.meta.env.MODE === 'development') {
        showToast({
            message: errorMessage,
            type: 'fail',
            duration: 3000
        });
    }
    else {
        console.warn('API错误:', errorMessage);
    }
    return Promise.reject(new Error(errorMessage));
}, (error) => {
    console.error('响应错误:', error);
    // 获取错误信息
    let errorMessage = DEFAULT_ERROR_MESSAGE;
    if (error.message) {
        // 检查是否有预定义的错误消息
        errorMessage = ERROR_MESSAGES[error.message] || error.message;
    }
    if (error.response) {
        // 服务器返回了错误状态码
        const statusCode = error.response.status.toString();
        errorMessage = ERROR_MESSAGES[statusCode] ||
            (error.response.data && error.response.data.message) ||
            errorMessage;
        // 特殊状态码处理
        if (error.response.status === 401) {
            // 登录过期，清除token并跳转到登录页
            localStorage.removeItem('token');
            showDialog({
                title: '登录已过期',
                message: '您的登录已过期，请重新登录',
                confirmButtonText: '确定',
                closeOnClickOverlay: false
            }).then(() => {
                window.location.href = '/login';
            });
            return Promise.reject(error);
        }
    }
    // 只在开发环境弹toast，生产环境只输出console.warn
    if (import.meta.env.MODE === 'development') {
        showToast({
            message: errorMessage,
            type: 'fail',
            duration: 3000
        });
    }
    else {
        console.warn('API错误:', errorMessage);
    }
    return Promise.reject(error);
});
// 封装GET请求
export function get(url, params, config) {
    return request.get(url, { params, ...config });
}
// 封装POST请求
export function post(url, data, config) {
    return request.post(url, data, config);
}
// 封装PUT请求
export function put(url, data, config) {
    return request.put(url, data, config);
}
// 封装DELETE请求
export function del(url, config) {
    return request.delete(url, config);
}
// 带重试功能的请求
export async function requestWithRetry(requestFn, retries = 3, delay = 1000) {
    try {
        return await requestFn();
    }
    catch (error) {
        if (retries <= 0) {
            throw error;
        }
        // 网络错误才进行重试
        if (axios.isAxiosError(error) && !error.response) {
            await new Promise(resolve => setTimeout(resolve, delay));
            return requestWithRetry(requestFn, retries - 1, delay * 2);
        }
        throw error;
    }
}
export default request;
