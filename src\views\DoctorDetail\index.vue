<template>
  <div class="doctor-detail-page">
    <GlobalHeader title="名中医详情" />
    <div class="detail-content">
      <LeaderDetailCard 
        :leader-id="doctorId" 
        api-type="doctor"
        @loaded="handleLoaded"
        @error="handleError"
        @retry="handleRetry"
      />
    </div>
    <GlobalFooter />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { showToast } from 'vant';
import GlobalHeader from '../../components/GlobalHeader.vue';
import GlobalFooter from '../../components/GlobalFooter.vue';
import LeaderDetailCard from '../../components/LeaderDetailCard.vue';
import type { LeaderDetail } from '../LeaderDetail/api';

const route = useRoute();
const router = useRouter();
const doctorId = ref<string>('');

onMounted(() => {
  doctorId.value = route.params.id as string;
  if (!doctorId.value) {
    showToast('医生ID无效');
    router.back();
  }
});

const handleLoaded = (data: LeaderDetail) => {
  console.log('名中医详情加载成功:', data);
};

const handleError = (error: string) => {
  console.error('名中医详情加载失败:', error);
  showToast(error);
};

const handleRetry = () => {
  console.log('重试加载名中医详情');
};
</script>

<style scoped>
.doctor-detail-page {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f8f9fa;
}

.detail-content {
  flex: 1;
  overflow-y: auto;
}

</style>