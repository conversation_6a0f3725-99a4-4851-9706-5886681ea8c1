import{d as N,r as k,l as S,B as V,y as $,c as t,o as e,f as d,b as _,L as E,w as i,j as u,F as g,e as y,g as a,i as o,t as n,I as U,_ as F}from"./index-BE8XLQ59.js";import{L as M}from"./index-ouF_E0m0.js";import{T as R}from"./index-CAfqjps3.js";import{E as D}from"./index-B27ddgxZ.js";const q={key:0,class:"loading-container"},x=["onClick"],A={class:"card-image-container"},G=["src","alt"],H={class:"card-content"},J={class:"news-title"},K={class:"news-description"},O={class:"activity-meta"},P={key:0,class:"meta-item"},Q={key:1,class:"meta-item"},W={class:"card-meta"},X={key:0,class:"meta-left"},Y={class:"category"},Z={key:1,class:"meta-left"},ss={class:"category"},es={key:2,class:"meta-right"},as={class:"card-tags"},ts={key:3},ns=["onClick"],os={class:"card-image-container"},cs=["src","alt"],ls={class:"card-content"},ds={class:"news-title"},is={class:"news-description"},rs={class:"activity-meta"},_s={key:0,class:"meta-item"},us={key:1,class:"meta-item"},ms={class:"card-meta"},ps={key:0,class:"meta-left"},gs={class:"category"},ys={key:1,class:"meta-left"},hs={class:"category"},vs={key:2,class:"meta-right"},fs={class:"card-tags"},ks=N({__name:"CommonCardList",props:{items:{},loading:{type:Boolean},finished:{type:Boolean,default:!1},emptyText:{default:"暂无内容"},useInfiniteScroll:{type:Boolean,default:!0}},emits:["load-more","card-click"],setup(w,{emit:C}){const h=k(null),L=()=>h.value;S(()=>{V(()=>{console.log("滚动容器:",h.value)})});const B=w,b=C,v=k(!1);$(()=>B.loading,c=>{v.value=c},{immediate:!0});const I=()=>{console.log("触发加载更多"),b("load-more")};return(c,l)=>{const T=E,j=D,m=R,p=U,z=M;return e(),t("div",{class:"common-card-list",ref_key:"scrollContainerRef",ref:h},[c.loading&&!c.items.length?(e(),t("div",q,[_(T,{size:"24px"},{default:i(()=>l[1]||(l[1]=[u("加载中...")])),_:1,__:[1]})])):c.items.length?c.useInfiniteScroll?(e(),d(z,{key:2,loading:v.value,"onUpdate:loading":l[0]||(l[0]=s=>v.value=s),finished:c.finished,"finished-text":"没有更多了",onLoad:I,"immediate-check":!1,"scroll-container":L},{default:i(()=>[(e(!0),t(g,null,y(c.items,s=>(e(),t("div",{key:s.id,class:"news-card animate__animated animate__fadeInUp",onClick:r=>c.$emit("card-click",s)},[a("div",A,[s.job?(e(),d(m,{key:0,type:"success",size:"medium",class:"news-tag",round:""},{default:i(()=>[u(n(s.job),1)]),_:2},1024)):o("",!0),s.cat_name?(e(),d(m,{key:1,type:"success",size:"medium",class:"news-tag",round:""},{default:i(()=>[u(n(s.cat_name),1)]),_:2},1024)):o("",!0),a("img",{src:s.thumbnail,alt:s.name,class:"news-image"},null,8,G),l[2]||(l[2]=a("div",{class:"image-overlay"},null,-1))]),a("div",H,[a("h3",J,n(s.name),1),a("p",K,n(s.desc),1)]),a("div",O,[s.startdate?(e(),t("div",P,[_(p,{name:"calendar-o"}),a("span",null,n(s.startdate)+" ~ "+n(s.enddate),1)])):o("",!0),s.location?(e(),t("div",Q,[_(p,{name:"location-o"}),a("span",null,n(s.location),1)])):o("",!0)]),a("div",W,[s.cat_display?(e(),t("div",X,[a("span",Y,n(s.cat_display),1)])):o("",!0),s.isfree?(e(),t("div",Z,[a("span",ss,n(s.isfree),1)])):o("",!0),s.tags&&s.tags.length?(e(),t("div",es,[a("div",as,[(e(!0),t(g,null,y(s.tags,(r,f)=>(e(),t("span",{key:f,class:"tag"},n(r),1))),128))])])):o("",!0)])],8,x))),128))]),_:1},8,["loading","finished"])):(e(),t("div",ts,[(e(!0),t(g,null,y(c.items,s=>(e(),t("div",{key:s.id,class:"news-card animate__animated animate__fadeInUp",onClick:r=>c.$emit("card-click",s)},[a("div",os,[s.job?(e(),d(m,{key:0,type:"success",size:"medium",class:"news-tag",round:""},{default:i(()=>[u(n(s.job),1)]),_:2},1024)):o("",!0),s.cat_name?(e(),d(m,{key:1,type:"success",size:"medium",class:"news-tag",round:""},{default:i(()=>[u(n(s.cat_name),1)]),_:2},1024)):o("",!0),a("img",{src:s.thumbnail,alt:s.name,class:"news-image"},null,8,cs),l[3]||(l[3]=a("div",{class:"image-overlay"},null,-1))]),a("div",ls,[a("h3",ds,n(s.name),1),a("p",is,n(s.desc),1)]),a("div",rs,[s.startdate?(e(),t("div",_s,[_(p,{name:"calendar-o"}),a("span",null,n(s.startdate)+" ~ "+n(s.enddate),1)])):o("",!0),s.location?(e(),t("div",us,[_(p,{name:"location-o"}),a("span",null,n(s.location),1)])):o("",!0)]),a("div",ms,[s.cat_display?(e(),t("div",ps,[a("span",gs,n(s.cat_display),1)])):o("",!0),s.isfree?(e(),t("div",ys,[a("span",hs,n(s.isfree),1)])):o("",!0),s.tags&&s.tags.length?(e(),t("div",vs,[a("div",fs,[(e(!0),t(g,null,y(s.tags,(r,f)=>(e(),t("span",{key:f,class:"tag"},n(r),1))),128))])])):o("",!0)])],8,ns))),128))])):(e(),d(j,{key:1,description:c.emptyText},null,8,["description"]))],512)}}}),bs=F(ks,[["__scopeId","data-v-e9a83d2d"]]);export{bs as C};
