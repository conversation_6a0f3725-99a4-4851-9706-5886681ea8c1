import { get, requestWithRetry } from '../../api/request';
import { FAMOUS_DOCTOR_URLS, buildUrl } from '../../api/urls';

/**
 * 获取领导分类
 * @returns 领导分类数据
 */
export function getDoctorCategories() {
  return get<DoctorCategoriesResponse>(FAMOUS_DOCTOR_URLS.CATEGORIES);
}

/**
 * 根据分类获取领导列表
 * @param cat 分类ID
 * @param page 页码
 * @param pageSize 每页数量
 * @returns 领导列表数据
 */
export function getDoctorsByCategory(cat: string, page: number = 1, pageSize: number = 6) {
  return get<DoctorListResponse>(FAMOUS_DOCTOR_URLS.DOCTOR, {
    cat,
    page,
    page_size: pageSize
  });
}

/**
 * 带重试功能的获取领导分类
 * 在网络不稳定情况下使用
 * @returns 领导分类数据
 */

export function getDoctorCategoriesWithRetry(cat: string, page: number = 1, pageSize: number = 6) {
  return requestWithRetry<DoctorListResponse>(() => getDoctorsByCategory(
    cat,
    page,
    pageSize
  ));
}

// 领导分类接口
export interface DoctorCategory {
  value: string;
  label: string;
}

// 领导分类响应接口
export interface DoctorCategoriesResponse {
  categories: DoctorCategory[];
}

// 领导信息接口
export interface DoctorItem {
  id: number;
  name: string;
  title: string;
  job: string;
  cat_display: string;
  desc: string;
  thumbnail: string;
  category: string;
  tags?: string[];
}

// 领导列表响应接口
export interface DoctorListResponse {
  results: DoctorItem[];
  page: number;
  page_size: number;
  total: number;
  is_last_page: boolean;
}