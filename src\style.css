/* 全局样式 - 移动端优化 */
:root {
  /* 中医药主题色彩 */
  --primary-color: #667eea;
  --secondary-color: #764ba2;
  --accent-color: #26de81;
  --warning-color: #ff6b6b;
  --text-primary: #333333;
  --text-secondary: #666666;
  --text-light: #999999;
  --background-light: #f8f9fa;
  --background-white: #ffffff;
  --border-color: #e9ecef;
  --shadow-light: rgba(0, 0, 0, 0.1);
  --shadow-medium: rgba(0, 0, 0, 0.15);
  --header-height: 56px;
  /* 字体设置 */
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  line-height: 1.6;
  font-weight: 400;
  
  /* 移动端优化 */
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
  text-size-adjust: 100%;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

/* 重置样式 */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

*::before,
*::after {
  box-sizing: border-box;
}

/* 基础元素 */
html {
  height: 100%;
  overflow-x: hidden;
}

/* Stagewise相关组件完全隐藏 */
#stagewise-toolbar-container,
#stagewise-toolbar-portal-root,
stagewise-companion-anchor,
[id*="stagewise"],
[class*="stagewise"] {
  display: none !important; /* 完全隐藏所有stagewise相关元素 */
  visibility: hidden !important;
  opacity: 0 !important;
  pointer-events: none !important;
}

body {
  margin: 0;
  padding: 0;
  min-height: 100vh;
  background-color: var(--background-light);
  color: var(--text-primary);
  font-size: 14px;
  overflow-x: hidden;
  -webkit-overflow-scrolling: touch;
}

/* 移动端触摸优化 */
body {
  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* 允许输入框选择文本 */
input, textarea, [contenteditable] {
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
  user-select: text;
}

/* 应用容器 */
#app {
  width: 100%;
  min-height: 100vh;
  margin: 0;
  padding: 0;
  background-color: var(--background-light);
}

/* 链接样式 */
a {
  color: var(--primary-color);
  text-decoration: none;
  transition: color 0.3s ease;
}

a:hover {
  color: var(--secondary-color);
}

a:active {
  opacity: 0.7;
}

/* 按钮样式 */
button {
  border: none;
  border-radius: 8px;
  padding: 12px 20px;
  font-size: 14px;
  font-weight: 500;
  font-family: inherit;
  cursor: pointer;
  transition: all 0.3s ease;
  outline: none;
  -webkit-appearance: none;
  appearance: none;
  background-color: var(--primary-color);
  color: white;
  min-height: 44px; /* 移动端最小触摸区域 */
}

button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px var(--shadow-medium);
}

button:active {
  transform: translateY(0);
}

button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* 输入框样式 */
input, textarea {
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 12px 16px;
  font-size: 14px;
  font-family: inherit;
  background-color: var(--background-white);
  color: var(--text-primary);
  transition: border-color 0.3s ease, box-shadow 0.3s ease;
  outline: none;
  -webkit-appearance: none;
  appearance: none;
  min-height: 44px; /* 移动端最小触摸区域 */
}

input:focus, textarea:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* 标题样式 */
h1, h2, h3, h4, h5, h6 {
  color: var(--text-primary);
  font-weight: 600;
  line-height: 1.4;
  margin: 0;
}

h1 {
  font-size: 28px;
}

h2 {
  font-size: 24px;
}

h3 {
  font-size: 20px;
}

h4 {
  font-size: 18px;
}

h5 {
  font-size: 16px;
}

h6 {
  font-size: 14px;
}

/* 段落样式 */
p {
  margin: 0;
  line-height: 1.6;
  color: var(--text-secondary);
}

/* 列表样式 */
ul, ol {
  margin: 0;
  padding: 0;
  list-style: none;
}

/* 图片样式 */
img {
  max-width: 100%;
  height: auto;
  display: block;
}

/* 卡片样式 */
.card {
  background-color: var(--background-white);
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 2px 8px var(--shadow-light);
  transition: box-shadow 0.3s ease, transform 0.3s ease;
}

.card:hover {
  box-shadow: 0 4px 16px var(--shadow-medium);
  transform: translateY(-2px);
}

/* 容器样式 */
.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 16px;
}

/* 网格布局 */
.grid {
  display: grid;
  gap: 16px;
}

.grid-2 {
  grid-template-columns: repeat(2, 1fr);
}

.grid-3 {
  grid-template-columns: repeat(3, 1fr);
}

.grid-4 {
  grid-template-columns: repeat(4, 1fr);
}

/* 弹性布局 */
.flex {
  display: flex;
}

.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

.flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

/* 间距工具类 */
.mt-1 { margin-top: 8px; }
.mt-2 { margin-top: 16px; }
.mt-3 { margin-top: 24px; }
.mt-4 { margin-top: 32px; }

.mb-1 { margin-bottom: 8px; }
.mb-2 { margin-bottom: 16px; }
.mb-3 { margin-bottom: 24px; }
.mb-4 { margin-bottom: 32px; }

.p-1 { padding: 8px; }
.p-2 { padding: 16px; }
.p-3 { padding: 24px; }
.p-4 { padding: 32px; }

/* 文本工具类 */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.text-primary { color: var(--text-primary); }
.text-secondary { color: var(--text-secondary); }
.text-light { color: var(--text-light); }

.font-bold { font-weight: 600; }
.font-medium { font-weight: 500; }
.font-normal { font-weight: 400; }

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 4px;
  height: 4px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 2px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--text-light);
}

/* 响应式设计 */
@media (max-width: 768px) {
  :root {
    font-size: 14px;
  }
  
  .container {
    padding: 0 12px;
  }
  
  .grid-3 {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .grid-4 {
    grid-template-columns: repeat(2, 1fr);
  }
  
  h1 {
    font-size: 24px;
  }
  
  h2 {
    font-size: 20px;
  }
  
  h3 {
    font-size: 18px;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 8px;
  }
  
  .grid-2,
  .grid-3,
  .grid-4 {
    grid-template-columns: 1fr;
  }
  
  button {
    padding: 10px 16px;
    font-size: 14px;
  }
  
  input, textarea {
    padding: 10px 12px;
    font-size: 14px;
  }
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn 0.6s ease-out;
}

.slide-in-up {
  animation: slideInUp 0.6s ease-out;
}

/* 加载状态 */
.loading {
  position: relative;
  overflow: hidden;
}

.loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

/* 安全区域适配 */
@supports (padding: max(0px)) {
  .safe-area-inset-top {
    padding-top: max(20px, env(safe-area-inset-top));
  }
  
  .safe-area-inset-bottom {
    padding-bottom: max(20px, env(safe-area-inset-bottom));
  }
}
