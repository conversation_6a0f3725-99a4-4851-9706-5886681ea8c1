import { computed } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { showToast } from 'vant';
import 'vant/es/toast/style';
import GlobalHeader from '../../components/GlobalHeader.vue';
import GlobalFooter from '@/components/GlobalFooter.vue';
import ActivityDetailCard from '../../components/ActivityDetailCard.vue';
const router = useRouter();
const route = useRoute();
// 获取活动ID
const activityId = computed(() => {
    return Array.isArray(route.params.id) ? route.params.id[0] : route.params.id;
});
const goBack = () => {
    router.back();
};
// 处理活动数据加载完成
const handleActivityLoaded = (activity) => {
    console.log('活动详情加载完成:', activity);
    // 可以在这里处理加载完成后的逻辑，比如更新页面标题等
};
// 处理活动加载错误
const handleActivityError = (error) => {
    console.error('活动详情加载失败:', error);
    showToast(error);
};
// 处理报名事件
const handleActivityRegister = (activity) => {
    console.log('报名活动:', activity);
    // 可以在这里添加报名统计等逻辑
};
debugger; /* PartiallyEnd: #3632/scriptSetup.vue */
const __VLS_ctx = {};
let __VLS_components;
let __VLS_directives;
// CSS variable injection 
// CSS variable injection end 
/** @type {[typeof GlobalHeader, ]} */ ;
// @ts-ignore
const __VLS_0 = __VLS_asFunctionalComponent(GlobalHeader, new GlobalHeader({
    ...{ 'onLeftClick': {} },
    title: "活动详情",
}));
const __VLS_1 = __VLS_0({
    ...{ 'onLeftClick': {} },
    title: "活动详情",
}, ...__VLS_functionalComponentArgsRest(__VLS_0));
let __VLS_3;
let __VLS_4;
let __VLS_5;
const __VLS_6 = {
    onLeftClick: (__VLS_ctx.goBack)
};
var __VLS_2;
/** @type {[typeof ActivityDetailCard, ]} */ ;
// @ts-ignore
const __VLS_7 = __VLS_asFunctionalComponent(ActivityDetailCard, new ActivityDetailCard({
    ...{ 'onLoaded': {} },
    ...{ 'onError': {} },
    ...{ 'onRegister': {} },
    activityId: (__VLS_ctx.activityId),
    showActions: (true),
}));
const __VLS_8 = __VLS_7({
    ...{ 'onLoaded': {} },
    ...{ 'onError': {} },
    ...{ 'onRegister': {} },
    activityId: (__VLS_ctx.activityId),
    showActions: (true),
}, ...__VLS_functionalComponentArgsRest(__VLS_7));
let __VLS_10;
let __VLS_11;
let __VLS_12;
const __VLS_13 = {
    onLoaded: (__VLS_ctx.handleActivityLoaded)
};
const __VLS_14 = {
    onError: (__VLS_ctx.handleActivityError)
};
const __VLS_15 = {
    onRegister: (__VLS_ctx.handleActivityRegister)
};
var __VLS_9;
/** @type {[typeof GlobalFooter, ]} */ ;
// @ts-ignore
const __VLS_16 = __VLS_asFunctionalComponent(GlobalFooter, new GlobalFooter({}));
const __VLS_17 = __VLS_16({}, ...__VLS_functionalComponentArgsRest(__VLS_16));
var __VLS_dollars;
const __VLS_self = (await import('vue')).defineComponent({
    setup() {
        return {
            GlobalHeader: GlobalHeader,
            GlobalFooter: GlobalFooter,
            ActivityDetailCard: ActivityDetailCard,
            activityId: activityId,
            goBack: goBack,
            handleActivityLoaded: handleActivityLoaded,
            handleActivityError: handleActivityError,
            handleActivityRegister: handleActivityRegister,
        };
    },
});
export default (await import('vue')).defineComponent({
    setup() {
        return {};
    },
});
; /* PartiallyEnd: #4569/main.vue */
