import { ref, watch, computed } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import GlobalHeader from '../../components/GlobalHeader.vue';
import GlobalFooter from '../../components/GlobalFooter.vue';
import LeaderDetailCard from '../../components/LeaderDetailCard.vue';
const router = useRouter();
const route = useRoute();
const leaderCardRef = ref();
// 领导ID
const leaderId = computed(() => {
    return Array.isArray(route.params.id) ? route.params.id[0] : route.params.id;
});
// 领导数据
const leaderData = ref(null);
// 动态页面标题
const pageTitle = computed(() => {
    if (leaderData.value?.name) {
        return `${leaderData.value.name} - 领导详情`;
    }
    return '领导详情';
});
// 返回上一页
const handleBack = () => {
    if (document.referrer && document.referrer !== location.href) {
        router.back();
    }
    else {
        router.replace('/home');
    }
};
// 处理领导数据加载完成
const handleLeaderLoaded = (data) => {
    leaderData.value = data;
    console.log('领导详情加载完成:', data);
};
// 处理加载错误
const handleLeaderError = (error) => {
    console.error('领导详情加载失败:', error);
};
// 处理重试
const handleRetry = () => {
    console.log('重试加载领导详情');
};
// 监听路由参数变化
watch(() => route.params.id, (newId) => {
    if (newId) {
        console.log('领导ID变化，重新加载:', newId);
        // 组件会自动响应leaderId的变化
    }
});
// 暴露刷新方法
const refresh = () => {
    if (leaderCardRef.value) {
        leaderCardRef.value.refresh();
    }
};
const __VLS_exposed = {
    refresh
};
defineExpose(__VLS_exposed);
debugger; /* PartiallyEnd: #3632/scriptSetup.vue */
const __VLS_ctx = {};
let __VLS_components;
let __VLS_directives;
// CSS variable injection 
// CSS variable injection end 
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ class: "leader-detail-container" },
});
__VLS_asFunctionalElement(__VLS_intrinsicElements.meta)({
    name: "viewport",
    content: "width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no",
});
/** @type {[typeof GlobalHeader, ]} */ ;
// @ts-ignore
const __VLS_0 = __VLS_asFunctionalComponent(GlobalHeader, new GlobalHeader({
    ...{ 'onLeftClick': {} },
    title: (__VLS_ctx.pageTitle),
}));
const __VLS_1 = __VLS_0({
    ...{ 'onLeftClick': {} },
    title: (__VLS_ctx.pageTitle),
}, ...__VLS_functionalComponentArgsRest(__VLS_0));
let __VLS_3;
let __VLS_4;
let __VLS_5;
const __VLS_6 = {
    onLeftClick: (__VLS_ctx.handleBack)
};
var __VLS_2;
/** @type {[typeof LeaderDetailCard, ]} */ ;
// @ts-ignore
const __VLS_7 = __VLS_asFunctionalComponent(LeaderDetailCard, new LeaderDetailCard({
    ...{ 'onLoaded': {} },
    ...{ 'onError': {} },
    ...{ 'onRetry': {} },
    leaderId: (__VLS_ctx.leaderId),
    ref: "leaderCardRef",
}));
const __VLS_8 = __VLS_7({
    ...{ 'onLoaded': {} },
    ...{ 'onError': {} },
    ...{ 'onRetry': {} },
    leaderId: (__VLS_ctx.leaderId),
    ref: "leaderCardRef",
}, ...__VLS_functionalComponentArgsRest(__VLS_7));
let __VLS_10;
let __VLS_11;
let __VLS_12;
const __VLS_13 = {
    onLoaded: (__VLS_ctx.handleLeaderLoaded)
};
const __VLS_14 = {
    onError: (__VLS_ctx.handleLeaderError)
};
const __VLS_15 = {
    onRetry: (__VLS_ctx.handleRetry)
};
/** @type {typeof __VLS_ctx.leaderCardRef} */ ;
var __VLS_16 = {};
var __VLS_9;
const __VLS_18 = {}.VanBackTop;
/** @type {[typeof __VLS_components.VanBackTop, typeof __VLS_components.vanBackTop, ]} */ ;
// @ts-ignore
const __VLS_19 = __VLS_asFunctionalComponent(__VLS_18, new __VLS_18({
    right: "16",
    bottom: "80",
}));
const __VLS_20 = __VLS_19({
    right: "16",
    bottom: "80",
}, ...__VLS_functionalComponentArgsRest(__VLS_19));
/** @type {[typeof GlobalFooter, ]} */ ;
// @ts-ignore
const __VLS_22 = __VLS_asFunctionalComponent(GlobalFooter, new GlobalFooter({}));
const __VLS_23 = __VLS_22({}, ...__VLS_functionalComponentArgsRest(__VLS_22));
/** @type {__VLS_StyleScopedClasses['leader-detail-container']} */ ;
// @ts-ignore
var __VLS_17 = __VLS_16;
var __VLS_dollars;
const __VLS_self = (await import('vue')).defineComponent({
    setup() {
        return {
            GlobalHeader: GlobalHeader,
            GlobalFooter: GlobalFooter,
            LeaderDetailCard: LeaderDetailCard,
            leaderCardRef: leaderCardRef,
            leaderId: leaderId,
            pageTitle: pageTitle,
            handleBack: handleBack,
            handleLeaderLoaded: handleLeaderLoaded,
            handleLeaderError: handleLeaderError,
            handleRetry: handleRetry,
        };
    },
});
export default (await import('vue')).defineComponent({
    setup() {
        return {
            ...__VLS_exposed,
        };
    },
});
; /* PartiallyEnd: #4569/main.vue */
