<template>
  <div class="section-container">
    <SectionHeader title="常用功能" :showMore="false" />

    <!-- 第一排：2个功能项 -->
    <van-grid :column-num="2" :border="false" :gutter="10" class="function-grid first-row">
      <van-grid-item v-for="(item, index) in firstRowItems" :key="index" class="animate__animated animate__fadeInUp"
        :style="{ animationDelay: index * 0.05 + 's' }">
        <div class="grid-item-content" :class="{ active: isActiveRoute(item) }" @click="onFunctionClick(item)">
          <div class="icon-wrapper" :class="{ active: isActiveRoute(item) }" :style="getIconStyle(item)">
            <van-icon :name="item.icon" :color="getIconColor(item)" size="24" />
          </div>
          <span class="grid-text" :class="{ active: isActiveRoute(item) }">{{ item.text }}</span>
        </div>
      </van-grid-item>
    </van-grid>

    <!-- 第二排：3个功能项 -->
    <van-grid :column-num="3" :border="false" :gutter="10" class="function-grid second-row">
      <van-grid-item v-for="(item, index) in secondRowItems" :key="index" class="animate__animated animate__fadeInUp"
        :style="{ animationDelay: (index + 2) * 0.05 + 's' }">
        <div class="grid-item-content" :class="{ active: isActiveRoute(item) }" @click="onFunctionClick(item)">
          <div class="icon-wrapper" :class="{ active: isActiveRoute(item) }" :style="getIconStyle(item)">
            <van-icon :name="item.icon" :color="getIconColor(item)" size="24" />
          </div>
          <span class="grid-text" :class="{ active: isActiveRoute(item) }">{{ item.text }}</span>
        </div>
      </van-grid-item>
    </van-grid>


  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { showToast } from 'vant';
import { useRouter, useRoute } from 'vue-router';
import SectionHeader from '../../../components/SectionHeader.vue'; // 引入通用标题组件

const router = useRouter();
const route = useRoute();

// 第一排功能按钮数据（2个）
const firstRowItems = ref([
  { 
    icon: 'manager-o', 
    text: '领导信息', 
    bgColor: '#e8f4ff', 
    iconColor: '#1989fa',
    routeName: 'Leader'
  },
  { 
    icon: 'friends-o', 
    text: '名中医信息', 
    bgColor: '#e8f7ff', 
    iconColor: '#07c160',
    routeName: 'Doctor'
  }
]);

// 第二排功能按钮数据（3个）
const secondRowItems = ref([
  { 
    icon: 'gift-o', 
    text: '特色制剂', 
    bgColor: '#fef2e8', 
    iconColor: '#ff976a',
    routeName: 'Medicine'
  },
  { 
    icon: 'cluster-o', 
    text: '特色科室', 
    bgColor: '#f5f5f5', 
    iconColor: '#ee0a24',
    routeName: 'Dept'
  },
  { 
    icon: 'star-o', 
    text: '重点科室', 
    bgColor: '#f0f9eb', 
    iconColor: '#8bc34a',
    routeName: 'MainDept'
  }
]);



// 判断是否是当前激活的路由
const isActiveRoute = (item: any) => {
  return route.name === item.routeName;
};

// 获取图标颜色
const getIconColor = (item: any) => {
  return isActiveRoute(item) ? '#fff' : item.iconColor;
};

// 获取图标样式
const getIconStyle = (item: any) => {
  if (isActiveRoute(item)) {
    return {
      background: `linear-gradient(135deg, ${item.iconColor}, ${item.iconColor}dd)`
    };
  }
  return {
    backgroundColor: item.bgColor
  };
};

// 功能按钮点击事件
const onFunctionClick = (item: any) => {
  console.log(`点击了${item.text}`);

  // 根据功能项跳转到对应的分类页面
  switch (item.text) {
    case '领导信息':
      router.push({ name: 'Leader', params: { type: 'leader' } });
      break;
    case '名中医信息':
      router.push({ name: 'Doctor', params: { type: 'doctor' } });
      break;
    case '特色制剂':
      router.push({ name: 'Medicine', params: { type: 'medicine' } });
      break;
    case '特色科室':
      router.push({ name: 'Dept', params: { type: 'department' } });
      break;
    case '重点科室':
      router.push({ name: 'MainDept', params: { type: 'main-department' } });
      break;
    default:
      showToast(`点击了${item.text}`);
  }
};
</script>

<style scoped>
/* 区块通用样式 */
.section-container {
  padding: 5px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.section-title {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: bold;
  color: #323233;
}

.section-title span {
  margin-left: 8px;
}

.more-btn {
  font-size: 12px;
}

/* 功能按钮区样式 */
.function-grid {
  --van-grid-item-content-padding: 8px;
  display: flex;
  flex-wrap: nowrap;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
  padding-bottom: 10px;
}

/* 第二排功能区样式 */
.second-row {
  margin-top: 10px;
}

/* 第三排功能区样式 */
.third-row {
  margin-top: 10px;
}

/* 测试按钮特殊样式 */
.test-item {
  background: linear-gradient(135deg, rgba(235, 47, 150, 0.05), rgba(235, 47, 150, 0.1));
  border-radius: 12px;
  padding: 12px;
  border: 1px dashed rgba(235, 47, 150, 0.3);
}

.test-item:hover {
  background: linear-gradient(135deg, rgba(235, 47, 150, 0.1), rgba(235, 47, 150, 0.15));
  border-color: rgba(235, 47, 150, 0.5);
}

:deep(.first-row .van-grid-item) {
  flex: 0 0 50%;
  max-width: 50%;
  box-sizing: border-box;
}

:deep(.second-row .van-grid-item) {
  flex: 0 0 33.33%;
  max-width: 33.33%;
  box-sizing: border-box;
}

:deep(.third-row .van-grid-item) {
  flex: 0 0 100%;
  max-width: 100%;
  box-sizing: border-box;
}

.grid-item-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  width: 100%;
  padding: 0 5px;
  box-sizing: border-box;
  position: relative; /* 为底部指示器提供定位基准 */
}

.grid-item-content:hover {
  transform: translateY(-5px);
}

.icon-wrapper {
  width: 48px;
  height: 48px;
  border-radius: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
  transition: all 0.3s ease;
}

/* 选中状态的图标容器 */
.icon-wrapper.active {
  border-radius: 24px !important; /* 圆形 */
  box-shadow: 0 2px 8px rgba(75, 139, 244, 0.3);
  transform: scale(1.05);
}

.grid-item-content:active .icon-wrapper {
  transform: scale(0.9);
}

/* 选中状态的图标 */
.icon-wrapper.active .van-icon {
  color: #fff !important;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.2));
}

.grid-text {
  font-size: 13px;
  color: #323233;
  font-weight: 500;
  transition: all 0.3s ease;
}

/* 选中状态的文字 */
.grid-text.active {
  color: #4b8bf4 !important;
  font-weight: 600;
}

/* 选中状态的底部指示器 */
.grid-item-content.active::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 50%;
  transform: translateX(-50%);
  width: 4px;
  height: 4px;
  background: #4b8bf4;
  border-radius: 50%;
  animation: dotFadeIn 0.3s ease;
}

@keyframes dotFadeIn {
  0% {
    opacity: 0;
    transform: translateX(-50%) scale(0);
  }
  100% {
    opacity: 1;
    transform: translateX(-50%) scale(1);
  }
}
</style>
