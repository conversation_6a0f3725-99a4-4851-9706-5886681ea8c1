import{d as c,a as i,u as p,r as _,l as u,c as m,o as f,b as a,g as h,_ as v}from"./index-BE8XLQ59.js";import{G as x,a as b}from"./GlobalHeader-lL88u8sR.js";import{L as g}from"./LeaderDetailCard-VVs8AxZp.js";import{s as t}from"./function-call-BUl5915X.js";import"./index-CAfqjps3.js";import"./index-B27ddgxZ.js";const L={class:"dept-detail-page"},R={class:"detail-content"},k=c({__name:"index",setup(y){const s=i(),r=p(),o=_("");u(()=>{o.value=s.params.id,o.value||(t("科室ID无效"),r.back())});const d=e=>{console.log("特色科室详情加载成功:",e)},n=e=>{console.error("特色科室详情加载失败:",e),t(e)},l=()=>{console.log("重试加载特色科室详情")};return(e,B)=>(f(),m("div",L,[a(x,{title:"特色科室详情"}),h("div",R,[a(g,{"leader-id":o.value,"api-type":"dept",onLoaded:d,onError:n,onRetry:l},null,8,["leader-id"])]),a(b)]))}}),V=v(k,[["__scopeId","data-v-6e1f470d"]]);export{V as default};
