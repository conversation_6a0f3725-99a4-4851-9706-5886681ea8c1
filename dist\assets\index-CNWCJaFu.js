import{s as x,v as y,x as C,d as E,u as R,r as o,y as T,l as B,c as _,o as p,b as u,w as g,F as I,e as D,f as G,g as b,_ as A}from"./index-BE8XLQ59.js";import{T as F,a as S}from"./index-CmzFzWyS.js";import"./index-sbxbYdRt.js";import{G as V,a as z}from"./GlobalHeader-lL88u8sR.js";import{C as M}from"./CommonCardList-1yAjUs8b.js";import"./index-B27ddgxZ.js";import"./index-ouF_E0m0.js";import"./index-CAfqjps3.js";function N(){return y(C.CATEGORIES)}function U(r,n=1,t=6){return y(C.LEADER,{cat:r,page:n,page_size:t})}function W(r,n=1,t=6){return x(()=>U(r,n,t))}const q={class:"news-page category-tabs-container"},H={class:"section-container"},O={class:"news-grid"},j=E({__name:"index",setup(r){const n=R(),t=o(!1),i=o(!1),d=o([]),v=o(1),l=o(0),h=o(5),f=o([]),L=async()=>{const e=await N();console.log("获取到领导分类sdfsdfsd:",e);const s=e.categories.map(a=>({id:a.value,name:a.label}));f.value=[...s]};T(l,e=>{v.value=1,d.value=[],i.value=!1,m()});const m=async()=>{var s;if(i.value||t.value)return;t.value=!0;const e=(s=f.value[l.value])==null?void 0:s.id;console.log("当前选中标签ID:",e);try{const a=await W(e,v.value,h.value);d.value.push(...a.results),v.value+=1,i.value=a.is_last_page===!0}catch(a){console.error("加载失败:",a)}finally{t.value=!1}},w=e=>{console.log("点击领导卡片，跳转到详情页:",e.id),n.push({name:"LeaderDetail",params:{id:e.id}})};return B(()=>{L(),m()}),(e,s)=>{const a=F,k=S;return p(),_("div",q,[u(V,{title:"领导信息"}),u(k,{active:l.value,"onUpdate:active":s[0]||(s[0]=c=>l.value=c),class:"category-tabs",sticky:"","sticky-offset-top":0,swipeable:""},{default:g(()=>[(p(!0),_(I,null,D(f.value,c=>(p(),G(a,{key:c.id,title:c.name},{default:g(()=>[b("div",H,[b("div",O,[u(M,{items:d.value,loading:t.value,finished:i.value,"use-infinite-scroll":!0,onLoadMore:m,onCardClick:w},null,8,["items","loading","finished"])])])]),_:2},1032,["title"]))),128))]),_:1},8,["active"]),u(z)])}}}),ee=A(j,[["__scopeId","data-v-cf5bb802"]]);export{ee as default};
