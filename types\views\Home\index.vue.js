import { onMounted, onUnmounted, ref } from "vue";
import 'vant/es/toast/style'; // ✅ 必须引入 Toast 样式
import Carousel from "./components/Carousel.vue";
import FunctionGrid from "./components/FunctionGrid.vue";
import HospitalNews from "./components/HospitalNews.vue";
import HotVideos from "./components/HotVideos.vue";
import VideoPopup from "./components/VideoPopup.vue";
import ContactUs from "./components/ContactUs.vue";
import ArticleGridSection from "../../components/ArticleGridSection.vue";
import { getHomeDataList } from "../Knowledge/api";
// 响应式数据
const loading = ref(false);
const error = ref("");
const refreshing = ref(false);
const homeData = ref({
    culture_latest_data: [],
    knowledge_latest_data: [],
    case_latest_data: [],
});
// 获取文章列表
const fetchArticles = async () => {
    if (loading.value)
        return;
    try {
        loading.value = true;
        error.value = "";
        const res = await getHomeDataList({ source: 'index' });
        homeData.value = res.items;
        console.log("获取到的文章列表数据:", homeData.value);
    }
    catch (err) {
        console.error("获取文章列表失败:", err);
        error.value = "获取文章列表失败，请稍后再试";
    }
    finally {
        loading.value = false;
        refreshing.value = false;
    }
};
// 监听滚动事件，实现渐入动画
const handleScroll = () => {
    const elements = document.querySelectorAll(".animate__animated:not(.animate__fadeIn):not(.animate__fadeInUp)");
    elements.forEach((el) => {
        const rect = el.getBoundingClientRect();
        const windowHeight = window.innerHeight || document.documentElement.clientHeight;
        if (rect.top <= windowHeight * 0.8) {
            el.classList.add("animate__fadeInUp");
        }
    });
};
onMounted(() => {
    window.addEventListener("scroll", handleScroll);
    // 初始触发一次，处理首屏元素
    handleScroll();
    fetchArticles();
});
onUnmounted(() => {
    window.removeEventListener("scroll", handleScroll);
});
debugger; /* PartiallyEnd: #3632/scriptSetup.vue */
const __VLS_ctx = {};
let __VLS_components;
let __VLS_directives;
// CSS variable injection 
// CSS variable injection end 
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ class: "home-container" },
});
const __VLS_0 = {}.GlobalHeader;
/** @type {[typeof __VLS_components.GlobalHeader, ]} */ ;
// @ts-ignore
const __VLS_1 = __VLS_asFunctionalComponent(__VLS_0, new __VLS_0({}));
const __VLS_2 = __VLS_1({}, ...__VLS_functionalComponentArgsRest(__VLS_1));
/** @type {[typeof Carousel, ]} */ ;
// @ts-ignore
const __VLS_4 = __VLS_asFunctionalComponent(Carousel, new Carousel({
    position: "1",
}));
const __VLS_5 = __VLS_4({
    position: "1",
}, ...__VLS_functionalComponentArgsRest(__VLS_4));
/** @type {[typeof FunctionGrid, ]} */ ;
// @ts-ignore
const __VLS_7 = __VLS_asFunctionalComponent(FunctionGrid, new FunctionGrid({}));
const __VLS_8 = __VLS_7({}, ...__VLS_functionalComponentArgsRest(__VLS_7));
/** @type {[typeof HospitalNews, ]} */ ;
// @ts-ignore
const __VLS_10 = __VLS_asFunctionalComponent(HospitalNews, new HospitalNews({}));
const __VLS_11 = __VLS_10({}, ...__VLS_functionalComponentArgsRest(__VLS_10));
/** @type {[typeof ArticleGridSection, ]} */ ;
// @ts-ignore
const __VLS_13 = __VLS_asFunctionalComponent(ArticleGridSection, new ArticleGridSection({
    title: "最新文章",
    icon: "notes-o",
    showMore: (true),
    cultureData: (__VLS_ctx.homeData.culture_latest_data),
    knowledgeData: (__VLS_ctx.homeData.knowledge_latest_data),
    caseData: (__VLS_ctx.homeData.case_latest_data),
}));
const __VLS_14 = __VLS_13({
    title: "最新文章",
    icon: "notes-o",
    showMore: (true),
    cultureData: (__VLS_ctx.homeData.culture_latest_data),
    knowledgeData: (__VLS_ctx.homeData.knowledge_latest_data),
    caseData: (__VLS_ctx.homeData.case_latest_data),
}, ...__VLS_functionalComponentArgsRest(__VLS_13));
/** @type {[typeof HotVideos, ]} */ ;
// @ts-ignore
const __VLS_16 = __VLS_asFunctionalComponent(HotVideos, new HotVideos({}));
const __VLS_17 = __VLS_16({}, ...__VLS_functionalComponentArgsRest(__VLS_16));
/** @type {[typeof ContactUs, ]} */ ;
// @ts-ignore
const __VLS_19 = __VLS_asFunctionalComponent(ContactUs, new ContactUs({}));
const __VLS_20 = __VLS_19({}, ...__VLS_functionalComponentArgsRest(__VLS_19));
const __VLS_22 = {}.GlobalFooter;
/** @type {[typeof __VLS_components.GlobalFooter, ]} */ ;
// @ts-ignore
const __VLS_23 = __VLS_asFunctionalComponent(__VLS_22, new __VLS_22({}));
const __VLS_24 = __VLS_23({}, ...__VLS_functionalComponentArgsRest(__VLS_23));
const __VLS_26 = {}.VanBackTop;
/** @type {[typeof __VLS_components.VanBackTop, typeof __VLS_components.vanBackTop, ]} */ ;
// @ts-ignore
const __VLS_27 = __VLS_asFunctionalComponent(__VLS_26, new __VLS_26({
    right: "16",
    bottom: "80",
}));
const __VLS_28 = __VLS_27({
    right: "16",
    bottom: "80",
}, ...__VLS_functionalComponentArgsRest(__VLS_27));
/** @type {[typeof VideoPopup, ]} */ ;
// @ts-ignore
const __VLS_30 = __VLS_asFunctionalComponent(VideoPopup, new VideoPopup({}));
const __VLS_31 = __VLS_30({}, ...__VLS_functionalComponentArgsRest(__VLS_30));
/** @type {__VLS_StyleScopedClasses['home-container']} */ ;
var __VLS_dollars;
const __VLS_self = (await import('vue')).defineComponent({
    setup() {
        return {
            Carousel: Carousel,
            FunctionGrid: FunctionGrid,
            HospitalNews: HospitalNews,
            HotVideos: HotVideos,
            VideoPopup: VideoPopup,
            ContactUs: ContactUs,
            ArticleGridSection: ArticleGridSection,
            homeData: homeData,
        };
    },
});
export default (await import('vue')).defineComponent({
    setup() {
        return {};
    },
});
; /* PartiallyEnd: #4569/main.vue */
