import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import SectionHeader from '../../../components/SectionHeader.vue'; // 引入通用标题组件
import CommonCardList from '../../../components/CommonCardList.vue';
const router = useRouter();
const loading = ref(false);
const finished = ref(false); // 是否已加载完所有数据
const props = defineProps();
// 文章点击事件
const onCultureClick = (item) => {
    // 跳转到文章详情页
    router.push({ name: 'CultureDetail', params: { id: item.id } });
};
const onKnowledgeClick = (item) => {
    // 跳转到文章详情页
    router.push({ name: 'TcmKnowledgeDetail', params: { id: item.id } });
};
const onCaseClick = (item) => {
    // 跳转到文章详情页
    router.push({ name: 'CasesDetail', params: { id: item.id } });
};
// 组件挂载时打印数据
onMounted(() => {
    console.log('cultureData:', props.cultureData);
    console.log('knowledgeData:', props.knowledgeData);
    console.log('caseData:', props.caseData);
});
debugger; /* PartiallyEnd: #3632/scriptSetup.vue */
const __VLS_ctx = {};
let __VLS_components;
let __VLS_directives;
// CSS variable injection 
// CSS variable injection end 
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ class: "section-container featured-articles-section" },
});
/** @type {[typeof SectionHeader, ]} */ ;
// @ts-ignore
const __VLS_0 = __VLS_asFunctionalComponent(SectionHeader, new SectionHeader({
    title: "精品文章",
    icon: "description-o",
    showMore: (false),
}));
const __VLS_1 = __VLS_0({
    title: "精品文章",
    icon: "description-o",
    showMore: (false),
}, ...__VLS_functionalComponentArgsRest(__VLS_0));
/** @type {[typeof CommonCardList, ]} */ ;
// @ts-ignore
const __VLS_3 = __VLS_asFunctionalComponent(CommonCardList, new CommonCardList({
    ...{ 'onCardClick': {} },
    items: (__VLS_ctx.cultureData),
    loading: (__VLS_ctx.loading),
    finished: (__VLS_ctx.finished),
    useInfiniteScroll: (false),
}));
const __VLS_4 = __VLS_3({
    ...{ 'onCardClick': {} },
    items: (__VLS_ctx.cultureData),
    loading: (__VLS_ctx.loading),
    finished: (__VLS_ctx.finished),
    useInfiniteScroll: (false),
}, ...__VLS_functionalComponentArgsRest(__VLS_3));
let __VLS_6;
let __VLS_7;
let __VLS_8;
const __VLS_9 = {
    onCardClick: (__VLS_ctx.onCultureClick)
};
var __VLS_5;
/** @type {[typeof CommonCardList, ]} */ ;
// @ts-ignore
const __VLS_10 = __VLS_asFunctionalComponent(CommonCardList, new CommonCardList({
    ...{ 'onCardClick': {} },
    items: (__VLS_ctx.knowledgeData),
    loading: (__VLS_ctx.loading),
    finished: (__VLS_ctx.finished),
    useInfiniteScroll: (false),
}));
const __VLS_11 = __VLS_10({
    ...{ 'onCardClick': {} },
    items: (__VLS_ctx.knowledgeData),
    loading: (__VLS_ctx.loading),
    finished: (__VLS_ctx.finished),
    useInfiniteScroll: (false),
}, ...__VLS_functionalComponentArgsRest(__VLS_10));
let __VLS_13;
let __VLS_14;
let __VLS_15;
const __VLS_16 = {
    onCardClick: (__VLS_ctx.onKnowledgeClick)
};
var __VLS_12;
/** @type {[typeof CommonCardList, ]} */ ;
// @ts-ignore
const __VLS_17 = __VLS_asFunctionalComponent(CommonCardList, new CommonCardList({
    ...{ 'onCardClick': {} },
    items: (__VLS_ctx.caseData),
    loading: (__VLS_ctx.loading),
    finished: (__VLS_ctx.finished),
    useInfiniteScroll: (false),
}));
const __VLS_18 = __VLS_17({
    ...{ 'onCardClick': {} },
    items: (__VLS_ctx.caseData),
    loading: (__VLS_ctx.loading),
    finished: (__VLS_ctx.finished),
    useInfiniteScroll: (false),
}, ...__VLS_functionalComponentArgsRest(__VLS_17));
let __VLS_20;
let __VLS_21;
let __VLS_22;
const __VLS_23 = {
    onCardClick: (__VLS_ctx.onCaseClick)
};
var __VLS_19;
/** @type {__VLS_StyleScopedClasses['section-container']} */ ;
/** @type {__VLS_StyleScopedClasses['featured-articles-section']} */ ;
var __VLS_dollars;
const __VLS_self = (await import('vue')).defineComponent({
    setup() {
        return {
            SectionHeader: SectionHeader,
            CommonCardList: CommonCardList,
            loading: loading,
            finished: finished,
            onCultureClick: onCultureClick,
            onKnowledgeClick: onKnowledgeClick,
            onCaseClick: onCaseClick,
        };
    },
    __typeProps: {},
});
export default (await import('vue')).defineComponent({
    setup() {
        return {};
    },
    __typeProps: {},
});
; /* PartiallyEnd: #4569/main.vue */
