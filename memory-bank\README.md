# Memory Bank: TCM H5 Application

This Memory Bank contains comprehensive documentation for the Traditional Chinese Medicine (TCM) Culture Promotion H5 Application. The documentation is designed to provide complete context for development work, ensuring continuity across sessions and clear understanding of the project architecture.

## Memory Bank Structure

### Core Documentation Files

1. **`projectbrief.md`** - Foundation Document

   - Project overview and scope
   - Core requirements and goals
   - Technical foundation
   - Success criteria and constraints

2. **`productContext.md`** - Product Strategy

   - Problem statement and solution vision
   - Target user personas and value propositions
   - User experience goals and product principles
   - Success metrics and business context

3. **`techContext.md`** - Technical Implementation

   - Complete technology stack overview
   - Development environment setup
   - Architecture patterns and API integration
   - Performance optimization and deployment

4. **`systemPatterns.md`** - Architectural Design

   - System design patterns and implementation
   - Component architecture and relationships
   - Navigation, data loading, and styling patterns
   - Security and performance considerations

5. **`activeContext.md`** - Current Focus

   - Current work session context
   - Recent discoveries and changes
   - Active development areas and next steps
   - Challenges and considerations

6. **`progress.md`** - Development Status
   - What's working and what needs to be built
   - Current status by module
   - Known issues and technical debt
   - Quality metrics and priorities

## Project Quick Reference

### Application Overview

- **Type**: Mobile-first H5 Web Application
- **Purpose**: Traditional Chinese Medicine culture promotion and education
- **Technology**: Vue 3 + TypeScript + Vant UI + Vite
- **Architecture**: Single Page Application with modular component design

### Key Features

- **Content Modules**: Knowledge base, expert network, activity management, case studies
- **Navigation**: Bottom tab bar with Home, Knowledge, Activity sections
- **Components**: Shared component library for consistent UI
- **API Integration**: Centralized HTTP client with production API
- **Performance**: Route-based code splitting and mobile optimization

### Development Status

- **Phase**: Mature Development (85-90% complete)
- **Core Features**: Fully implemented and functional
- **Production**: Ready for deployment with optimization opportunities
- **Next Steps**: Performance enhancement, accessibility, and advanced features

## Usage Guidelines

### For Development Sessions

1. **Start Each Session**: Read `activeContext.md` for current focus
2. **Understand Architecture**: Review `systemPatterns.md` for implementation patterns
3. **Check Progress**: Consult `progress.md` for current status and priorities
4. **Technical Details**: Reference `techContext.md` for technical specifications

### For New Team Members

1. **Begin with**: `projectbrief.md` for project understanding
2. **Product Context**: Read `productContext.md` for business context
3. **Technical Setup**: Follow `techContext.md` for environment setup
4. **Architecture Review**: Study `systemPatterns.md` for code patterns

### For Project Updates

- Update `activeContext.md` when focus changes
- Modify `progress.md` when features are completed
- Enhance `systemPatterns.md` when new patterns are discovered
- Revise other documents when requirements or architecture evolve

## Key Project Insights

### Strengths

- Well-structured modular architecture
- Comprehensive component library
- Mobile-optimized user experience
- Production-ready API integration
- Strong TypeScript implementation

### Areas for Enhancement

- Search functionality across content
- Offline capability with service workers
- Enhanced accessibility features
- Performance monitoring and analytics
- Advanced user interaction features

### Critical Patterns

- Component composition with shared libraries
- Centralized API management with error handling
- Mobile-first responsive design
- Hash-based routing for deployment flexibility
- Infinite scroll with pagination support

## Development Environment

### Prerequisites

- Node.js 18+ (LTS recommended)
- npm package manager
- Modern browser with ES6+ support

### Quick Start

```bash
npm install          # Install dependencies
npm run dev         # Start development server
npm run build       # Build for production
```

### API Configuration

- **Development**: Proxy to `https://zyy.sxaliyun.cn`
- **Production**: Direct API integration
- **Environment**: Automatic switching based on build mode

This Memory Bank provides a complete foundation for understanding and working with the TCM H5 Application. Each document builds upon the others to create a comprehensive picture of the project's purpose, architecture, and current state.
