<template>
  <div class="section-header animate__animated animate__fadeInUp">
    <div class="section-title">
      <div class="title-left">
        <van-icon :name="icon" />
        <span>{{ title }}</span>
      </div>
      <div
        v-if="showMore"
        class="title-right"
        @click="handleMoreClick"
      >
        <span>更多</span>
        <van-icon name="arrow" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps, defineEmits } from 'vue';
import { useRouter } from 'vue-router';

const props = defineProps({
  title: { type: String, required: true },
  icon: { type: String, default: 'apps-o' },
  moreLink: { type: String, default: '' }, // 可选链接地址
  showMore: { type: Boolean, default: true }, // 是否显示“更多”
});

const emit = defineEmits(['more-click']);
const router = useRouter();

const handleMoreClick = () => {
  if (props.moreLink) {
    router.push(props.moreLink);
  } else {
    emit('more-click');
  }
};
</script>

<style scoped>
.section-header {
  padding: 12px 16px;
  background-color: #fff;
  border-bottom: 1px solid #f0f0f0;
}

.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.title-left {
  display: flex;
  align-items: center;
  font-size: 15px;
  font-weight: 600;
  color: #333;
  gap: 6px;
}

.title-left .van-icon {
  color: #4b8bf4;
  font-size: 18px;
}

.title-right {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #999;
  cursor: pointer;
  gap: 4px;
  transition: color 0.2s;
}

.title-right:hover {
  color: #4b8bf4;
}
</style>
