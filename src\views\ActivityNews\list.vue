<template>
  <div class="news-page">
    <GlobalHeader title="活动新闻" />
    <div class="section-container">
      <div class="news-grid">
        <!-- 加载状态 -->
        <div v-if="loading && items.length === 0" class="loading-container">
          <van-loading type="spinner" color="#4b8bf4" size="32px" />
          <p>加载中...</p>
        </div>
        
        <!-- 空状态 -->
        <van-empty v-else-if="!loading && items.length === 0" description="暂无活动新闻" />
        
        <!-- GridCard显示新闻列表 -->
        <div v-else class="grid-container">
          <GridCard :items="gridItems" @card-click="handleGridCardClick" />
          
          <!-- 加载更多 -->
          <div v-if="!finished" class="load-more-container">
            <van-button 
              :loading="loading" 
              @click="loadItems" 
              type="primary" 
              size="small"
              round
            >
              {{ loading ? '加载中...' : '加载更多' }}
            </van-button>
          </div>
          
          <!-- 没有更多数据提示 -->
          <div v-else-if="items.length > 0" class="no-more-tip">
            <van-divider>没有更多数据了</van-divider>
          </div>
        </div>
      </div>
    </div>
    <GlobalFooter />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import GlobalHeader from '../../components/GlobalHeader.vue';
import GlobalFooter from '../../components/GlobalFooter.vue';
import GridCard from '../../components/GridCard.vue';
import { getActivityNewsListlWithRetry } from './api'; // 假设支持分页参数
import type { ActivityNewsItem } from './api'; // 假设有定义的类型

const router = useRouter();
const loading = ref(false);
const finished = ref(false);
const items = ref<ActivityNewsItem[]>([]);
const page = ref(1);
const pageSize = ref(5); // 每次加载5条数据

// 将活动新闻数据转换为GridCard需要的格式
const gridItems = computed(() => {
  return items.value.map((item: ActivityNewsItem) => ({
    id: item.id,
    title: item.name,
    image: item.thumbnail,
    badge: '新闻',
    subtitle: item.desc,
    originalData: item
  }));
});


const loadItems = async () => {
  if (finished.value || loading.value) return;

  loading.value = true;

  try {
    const res = await getActivityNewsListlWithRetry({
      page: page.value,
      page_size: pageSize.value,
    }); 
    console.log('加载新闻数据:', res);
    console.log('is_last_page:', res.is_last_page);

    items.value.push(...res.results);
       page.value += 1;

          finished.value = res.is_last_page === true;

  } catch (error) {
    console.error('加载新闻失败:', error);
  } finally {
    loading.value = false;
  }
};

const handleCardClick = (news: any) => {
  router.push({ name: 'ActivityNewsDetail', params: { id: news.id } });
};

// GridCard点击处理
const handleGridCardClick = (item: any) => {
  const newsData = item.originalData || item;
  router.push({ name: 'ActivityNewsDetail', params: { id: newsData.id } });
};
onMounted(() => {
  loadItems();
});
</script>

<style scoped>
.news-page {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.section-container {
  flex: 1;
  padding: 16px;
  display: flex;
  flex-direction: column;
}

.news-grid {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: #666;
}

.loading-container p {
  margin-top: 12px;
  font-size: 14px;
}

.grid-container {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.load-more-container {
  display: flex;
  justify-content: center;
  padding: 20px;
  margin-top: 16px;
}

.no-more-tip {
  padding: 20px;
  text-align: center;
}

.no-more-tip :deep(.van-divider__content) {
  color: #999;
  font-size: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .section-container {
    padding: 12px;
  }
  
  .load-more-container {
    padding: 16px;
  }
}
</style>
