# LeaderDetailCard 组件

一个可复用的领导详情卡片组件，用于展示领导的详细信息。

## 功能特性

- 📸 **照片展示**：大尺寸领导照片，支持悬停效果
- 🏷️ **多标签显示**：姓名、职务、分类等信息以标签形式展示
- 📝 **基本信息**：支持 HTML 格式的描述信息
- 📄 **富文本内容**：完整的 HTML 富文本内容展示
- 🔄 **加载状态**：内置加载和错误状态处理
- 📱 **响应式设计**：适配不同屏幕尺寸
- 🎨 **现代化 UI**：渐变背景、阴影效果、动画交互

## 使用方式

### 方式一：通过领导 ID 获取数据

```vue
<template>
  <LeaderDetailCard
    :leader-id="6"
    @loaded="handleLoaded"
    @error="handleError"
    @retry="handleRetry"
  />
</template>

<script setup>
import LeaderDetailCard from "@/components/LeaderDetailCard.vue";

const handleLoaded = (data) => {
  console.log("领导数据加载完成:", data);
};

const handleError = (error) => {
  console.error("加载失败:", error);
};

const handleRetry = () => {
  console.log("用户点击重试");
};
</script>
```

### 方式二：直接传入领导数据

```vue
<template>
  <LeaderDetailCard :leader-info="leaderData" />
</template>

<script setup>
import LeaderDetailCard from "@/components/LeaderDetailCard.vue";

const leaderData = {
  id: 6,
  name: "赵代举",
  job: "院长",
  desc: "艾麦尔·麦麦提，男性，维吾尔族...",
  thumbnail: "https://zyy.sxaliyun.cn/media/base/Leader/202505/22.jpg",
  cat_display: "院领导",
  tags: [],
  content: "<p>详细的富文本内容...</p>",
};
</script>
```

## Props

| 参数            | 类型               | 默认值      | 说明                         |
| --------------- | ------------------ | ----------- | ---------------------------- |
| leaderId        | `string \| number` | `''`        | 领导 ID，用于从 API 获取数据 |
| leaderInfo      | `LeaderDetail`     | `undefined` | 直接传入的领导信息对象       |
| showRetryButton | `boolean`          | `true`      | 是否显示重试按钮             |

## Events

| 事件名 | 参数                   | 说明               |
| ------ | ---------------------- | ------------------ |
| loaded | `(data: LeaderDetail)` | 数据加载完成时触发 |
| error  | `(error: string)`      | 加载失败时触发     |
| retry  | `()`                   | 用户点击重试时触发 |

## 方法

通过 `ref` 可以调用组件的方法：

```vue
<template>
  <LeaderDetailCard ref="cardRef" :leader-id="6" />
  <button @click="refresh">刷新</button>
</template>

<script setup>
import { ref } from "vue";

const cardRef = ref();

const refresh = () => {
  cardRef.value.refresh();
};
</script>
```

### 可用方法

| 方法名     | 参数 | 返回值                      | 说明             |
| ---------- | ---- | --------------------------- | ---------------- |
| refresh    | `()` | `Promise<void>`             | 刷新数据         |
| leaderData | -    | `ComputedRef<LeaderDetail>` | 获取当前领导数据 |

## 数据结构

```typescript
interface LeaderDetail {
  id: number;
  name: string;
  job: string;
  desc: string;
  thumbnail: string;
  cat_display: string;
  tags?: string[];
  content?: string;
  // ... 其他字段
}
```

## 样式定制

组件使用了 scoped 样式，如需定制样式，可以通过以下方式：

### 1. CSS 变量（推荐）

```css
.leader-detail-card {
  --primary-color: #667eea;
  --success-color: #4caf50;
  --warning-color: #ffa500;
  --border-radius: 16px;
}
```

### 2. 深度选择器

```vue
<style>
.my-leader-card :deep(.large-avatar) {
  height: 400px;
}

.my-leader-card :deep(.info-tag) {
  font-size: 18px;
}
</style>
```

## 注意事项

1. **数据优先级**：如果同时传入 `leaderId` 和 `leaderInfo`，优先使用 `leaderInfo`
2. **API 依赖**：使用 `leaderId` 时需要确保 API 接口可用
3. **图片处理**：组件内置了图片加载失败的处理机制
4. **响应式**：组件会自动响应 props 的变化

## 使用场景

- 领导详情页面
- 领导信息卡片
- 人员展示组件
- 个人资料页面
- 团队成员展示

## 依赖

- Vue 3
- Vant UI
- TypeScript（可选）
