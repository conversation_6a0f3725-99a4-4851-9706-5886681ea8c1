<template>
  <div class="home-container">

    <GlobalHeader />

    <Carousel position="1" />

    <FunctionGrid />

    <HospitalNews />

    <ArticleGridSection title="最新文章" icon="notes-o" :showMore="true" :cultureData="homeData.culture_latest_data"
      :knowledgeData="homeData.knowledge_latest_data" :caseData="homeData.case_latest_data" />

    <HotVideos />

    <ContactUs />

    <!-- 使用通用装饰性页脚 -->
    <GlobalFooter />

    <!-- 返回顶部按钮 -->
    <van-back-top right="16" bottom="80" />

    <VideoPopup />
  </div>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted, ref } from "vue";
import { showToast } from "vant";
import 'vant/es/toast/style'; // ✅ 必须引入 Toast 样式

import Carousel from "./components/Carousel.vue";
import FunctionGrid from "./components/FunctionGrid.vue";
import HospitalNews from "./components/HospitalNews.vue";
import HotVideos from "./components/HotVideos.vue";
import VideoPopup from "./components/VideoPopup.vue";
import ContactUs from "./components/ContactUs.vue";
import ArticleGridSection from "../../components/ArticleGridSection.vue";
import { getHomeDataList } from "../Knowledge/api"

// 响应式数据
const loading = ref(false);
const error = ref("");
const refreshing = ref(false);

const homeData = ref<{
  culture_latest_data: any[];
  knowledge_latest_data: any[];
  case_latest_data: any[];
}>({
  culture_latest_data: [],
  knowledge_latest_data: [],
  case_latest_data: [],
});

// 获取文章列表
const fetchArticles = async () => {
  if (loading.value) return;

  try {
    loading.value = true;
    error.value = "";

    const res = await getHomeDataList({ source: 'index' });
    homeData.value = res.items;
    console.log("获取到的文章列表数据:", homeData.value);
  } catch (err) {
    console.error("获取文章列表失败:", err);
    error.value = "获取文章列表失败，请稍后再试";
  } finally {
    loading.value = false;
    refreshing.value = false;
  }
};

// 监听滚动事件，实现渐入动画
const handleScroll = () => {
  const elements = document.querySelectorAll(
    ".animate__animated:not(.animate__fadeIn):not(.animate__fadeInUp)"
  );
  elements.forEach((el) => {
    const rect = el.getBoundingClientRect();
    const windowHeight =
      window.innerHeight || document.documentElement.clientHeight;

    if (rect.top <= windowHeight * 0.8) {
      el.classList.add("animate__fadeInUp");
    }
  });
};
onMounted(() => {
  window.addEventListener("scroll", handleScroll);
  // 初始触发一次，处理首屏元素
  handleScroll();
  fetchArticles()
});

onUnmounted(() => {
  window.removeEventListener("scroll", handleScroll);
});
</script>

<style scoped>
@import "../../style/common.css";
/* @import "./style.css"; */

.home-container {
  padding-bottom: 80px; /* 为页脚留出空间，参考知识页实现 */
  position: relative;
  width: 100%;
  max-width: 100vw;
  overflow-x: hidden;
  box-sizing: border-box;
}

@keyframes gradientBG {
  0% {
    background-position: 0% 50%;
  }

  50% {
    background-position: 100% 50%;
  }

  100% {
    background-position: 0% 50%;
  }
}
</style>
