import { ref, onMounted, onActivated, watch } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { showToast } from 'vant';
import 'vant/es/toast/style'; // ✅ 必须引入 Toast 样式，解决白色弹窗问题
import GlobalHeader from '../../components/GlobalHeader.vue'; // 导入通用页头组件
import GlobalFooter from '../../components/GlobalFooter.vue'; // 导入通用页脚组件
import Carousel from "../Home/components/Carousel.vue"; // 导入轮播图组件
import ActivityCommonCardList from '../../components/ActivityCommonCardList.vue';
import { getHomeActivityList } from './api';
import { getActivityNewsListlWithRetry } from '../ActivityNews/api';
import GridCard from '../../components/GridCard.vue';
import { shareActivity, shareWithSystemMenu, canUseNativeShare, isWeChatEnvironment } from '../../utils/share'; // 导入分享工具
const router = useRouter();
const route = useRoute();
const loading = ref(false);
const reviewLoading = ref(false);
// 功能导航数据（4个按钮一行排列）
const navigationItems = ref([
    {
        icon: "calendar-o",
        text: "本月活动",
        bgColor: "#e8f4ff",
        iconColor: "#4b8bf4",
        route: "/activity-list?type=current",
        routeName: "ActivityList",
        queryType: "current"
    },
    {
        icon: "clock-o",
        text: "历史活动",
        bgColor: "#f0e6ff",
        iconColor: "#7c3aed",
        route: "/activity-list?type=past",
        routeName: "ActivityList",
        queryType: "past"
    },
    {
        icon: "underway-o",
        text: "活动预告",
        bgColor: "#e6f7ef",
        iconColor: "#059669",
        route: "/activity-list?type=upcoming",
        routeName: "ActivityList",
        queryType: "upcoming"
    },
    {
        icon: "newspaper-o",
        text: "活动新闻",
        bgColor: "#ffebe6",
        iconColor: "#ff8c69",
        route: "/activity-news-list",
        routeName: "ActivityNewsList"
    },
]);
const homeData = ref({
    activity_data: [],
});
const activityReviews = ref([]);
const activityGridItems = ref([]);
const activityReviewGridItems = ref([]);
// 加载活动数据
const loadActivities = async () => {
    try {
        loading.value = true;
        const res = await getHomeActivityList();
        console.log('获取首页活动列表成功:', res);
        homeData.value.activity_data = res.items.activity_data;
    }
    catch (error) {
        console.error('获取活动列表失败:', error);
    }
    finally {
        loading.value = false;
    }
};
// 加载活动回顾数据
const loadActivityReviews = async () => {
    try {
        reviewLoading.value = true;
        // 调用活动新闻API获取活动回顾数据
        const res = await getActivityNewsListlWithRetry({ page_size: 6 });
        // 将活动新闻数据转换为GridCard需要的格式
        activityReviewGridItems.value = res.results.map((item) => ({
            id: item.id,
            title: item.name,
            image: item.thumbnail,
            badge: '回顾',
            originalData: item
        }));
    }
    catch (error) {
        console.error('获取活动回顾失败:', error);
        // 如果活动新闻API失败，回退到使用活动数据
        try {
            const fallbackRes = await getHomeActivityList();
            activityReviewGridItems.value = fallbackRes.items.activity_data.slice(0, 6).map((item) => ({
                id: item.id,
                title: item.name,
                image: item.thumbnail,
                badge: '回顾',
                originalData: item
            }));
        }
        catch (fallbackError) {
            console.error('获取活动回顾数据完全失败:', fallbackError);
        }
    }
    finally {
        reviewLoading.value = false;
    }
};
// 初始化加载
onMounted(() => {
    loadActivities();
    loadActivityReviews();
});
// 页面激活时重新验证数据
onActivated(() => {
    console.log('活动页面被激活，检查数据状态');
    // 如果数据为空，重新加载
    if (!homeData.value.activity_data || homeData.value.activity_data.length === 0) {
        console.log('检测到活动数据为空，重新加载');
        loadActivities();
    }
    if (!activityReviewGridItems.value || activityReviewGridItems.value.length === 0) {
        console.log('检测到活动回顾数据为空，重新加载');
        loadActivityReviews();
    }
});
// 监听路由变化，确保从其他页面返回时数据正确
watch(() => route.name, (newName, oldName) => {
    if (newName === 'Activity' && oldName && oldName !== 'Activity') {
        console.log(`从 ${String(oldName)} 页面返回到活动页面，验证数据状态`);
        // 延迟一小段时间再检查，确保页面完全加载
        setTimeout(() => {
            if (!homeData.value.activity_data || homeData.value.activity_data.length === 0) {
                console.log('返回活动页面时检测到数据丢失，重新加载');
                loadActivities();
            }
        }, 100);
    }
});
// 处理活动点击
const handleCardClick = (news) => {
    router.push({
        name: 'ActivityDetail',
        params: { id: news.id },
        query: { from: 'ongoing' } // 标记来源为正在进行中
    });
};
// 判断是否是当前激活的路由
const isActiveRoute = (item) => {
    if (item.queryType) {
        // 对于有查询参数的路由，需要同时匹配路由名和查询参数
        return route.name === item.routeName && route.query.type === item.queryType;
    }
    // 对于没有查询参数的路由，只匹配路由名
    return route.name === item.routeName;
};
// 获取图标颜色
const getIconColor = (item) => {
    return isActiveRoute(item) ? '#fff' : item.iconColor;
};
// 获取图标样式
const getIconStyle = (item) => {
    if (isActiveRoute(item)) {
        return {
            background: `linear-gradient(135deg, ${item.iconColor}, ${item.iconColor}dd)`
        };
    }
    return {
        backgroundColor: item.bgColor
    };
};
// 处理功能导航点击
const onFunctionClick = (item) => {
    if (item.route) {
        router.push(item.route);
    }
};
// 页头按钮事件处理
const handleBack = () => {
    router.back();
};
// 查看更多活动回顾
const handleViewMoreReviews = () => {
    router.push('/activity-news-list');
};
const handleActivityGridClick = (item) => {
    // 从GridCard的originalData中获取原始活动数据
    const activityData = item.originalData || item;
    router.push({ name: 'ActivityDetail', params: { id: activityData.id } });
};
const handleReviewGridClick = (item) => {
    // 从GridCard的originalData中获取原始活动数据
    const activityData = item.originalData || item;
    // 活动回顾应该跳转到活动新闻详情页
    router.push({ name: 'ActivityNewsDetail', params: { id: activityData.id } });
};
// 处理分享按钮点击
const handleShare = async (item) => {
    console.log('=== 开始分享流程 ===');
    console.log('分享活动原始数据:', item);
    console.log('当前页面活动数据状态:', homeData.value.activity_data);
    // 第一层验证：检查传入的item是否有效
    if (!item) {
        console.error('分享失败：item为空', item);
        showToast('分享数据异常，请刷新页面后重试');
        return;
    }
    // 第二层验证：检查活动名称
    if (!item.name || typeof item.name !== 'string' || item.name.trim() === '') {
        console.error('分享失败：活动名称无效', {
            name: item.name,
            type: typeof item.name,
            item: item
        });
        // 尝试从当前页面数据中查找对应的活动
        if (item.id && homeData.value.activity_data.length > 0) {
            const foundActivity = homeData.value.activity_data.find(activity => activity.id === item.id || activity.id.toString() === item.id.toString());
            if (foundActivity && foundActivity.name && foundActivity.name.trim() !== '') {
                console.log('从页面数据中找到完整的活动信息，使用完整数据进行分享');
                item = foundActivity;
            }
            else {
                console.error('无法找到完整的活动数据');
                showToast('活动数据不完整，请刷新页面后重试');
                return;
            }
        }
        else {
            showToast('活动数据加载中，请稍后再试');
            return;
        }
    }
    console.log('验证通过，开始执行分享:', item.name);
    try {
        // 首先尝试使用系统原生分享菜单（可以直接调起微信等应用）
        if (canUseNativeShare()) {
            try {
                console.log('尝试使用原生分享API');
                const success = await shareWithSystemMenu(item);
                if (success) {
                    console.log('原生分享成功');
                    showToast('分享成功');
                    return;
                }
            }
            catch (error) {
                console.log('原生分享失败，尝试其他方式:', error);
            }
        }
        // 如果原生分享不可用或失败，使用备用方案
        console.log('使用备用分享方案');
        const result = await shareActivity(item);
        console.log('分享结果:', result);
        if (result.success) {
            switch (result.method) {
                case 'native':
                    showToast('分享成功');
                    break;
                case 'clipboard':
                    if (isWeChatEnvironment()) {
                        showToast('内容已复制，请在微信中长按粘贴分享');
                    }
                    else {
                        showToast('内容已复制，请粘贴到微信等应用中分享');
                    }
                    break;
                default:
                    showToast('分享成功');
            }
        }
        else {
            if (result.method === 'cancelled') {
                // 用户取消分享，不显示错误提示
                console.log('用户取消分享');
                return;
            }
            console.error('分享失败:', result);
            showToast('分享失败，请稍后重试');
        }
    }
    catch (error) {
        console.error('分享过程中发生异常:', error);
        showToast('分享失败，请稍后重试');
    }
    console.log('=== 分享流程结束 ===');
};
// 处理报名按钮点击
const handleRegister = (item) => {
    showToast(`报名活动: ${item.name}`);
    // 这里可以添加实际的报名逻辑
    // 比如跳转到报名页面、调用报名API等
    router.push({ name: 'ActivityRegistration', params: { id: item.id } });
};
debugger; /* PartiallyEnd: #3632/scriptSetup.vue */
const __VLS_ctx = {};
let __VLS_components;
let __VLS_directives;
/** @type {__VLS_StyleScopedClasses['grid-item-content']} */ ;
/** @type {__VLS_StyleScopedClasses['grid-item-content']} */ ;
/** @type {__VLS_StyleScopedClasses['icon-wrapper']} */ ;
/** @type {__VLS_StyleScopedClasses['icon-wrapper']} */ ;
/** @type {__VLS_StyleScopedClasses['icon-wrapper']} */ ;
/** @type {__VLS_StyleScopedClasses['active']} */ ;
/** @type {__VLS_StyleScopedClasses['grid-text']} */ ;
/** @type {__VLS_StyleScopedClasses['active']} */ ;
/** @type {__VLS_StyleScopedClasses['grid-item-content']} */ ;
/** @type {__VLS_StyleScopedClasses['active']} */ ;
/** @type {__VLS_StyleScopedClasses['grid-text']} */ ;
/** @type {__VLS_StyleScopedClasses['grid-item-content']} */ ;
/** @type {__VLS_StyleScopedClasses['icon-wrapper']} */ ;
/** @type {__VLS_StyleScopedClasses['grid-text']} */ ;
/** @type {__VLS_StyleScopedClasses['grid-item-content']} */ ;
/** @type {__VLS_StyleScopedClasses['icon-wrapper']} */ ;
/** @type {__VLS_StyleScopedClasses['grid-text']} */ ;
/** @type {__VLS_StyleScopedClasses['grid-item-content']} */ ;
/** @type {__VLS_StyleScopedClasses['icon-wrapper']} */ ;
/** @type {__VLS_StyleScopedClasses['grid-item-content']} */ ;
/** @type {__VLS_StyleScopedClasses['icon-wrapper']} */ ;
/** @type {__VLS_StyleScopedClasses['animate__fadeInUp']} */ ;
/** @type {__VLS_StyleScopedClasses['section-more']} */ ;
/** @type {__VLS_StyleScopedClasses['section-more']} */ ;
/** @type {__VLS_StyleScopedClasses['van-icon']} */ ;
/** @type {__VLS_StyleScopedClasses['section-more']} */ ;
/** @type {__VLS_StyleScopedClasses['van-icon']} */ ;
// CSS variable injection 
// CSS variable injection end 
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ class: "activity-page" },
});
__VLS_asFunctionalElement(__VLS_intrinsicElements.meta)({
    name: "viewport",
    content: "width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no",
});
/** @type {[typeof GlobalHeader, ]} */ ;
// @ts-ignore
const __VLS_0 = __VLS_asFunctionalComponent(GlobalHeader, new GlobalHeader({
    ...{ 'onLeftClick': {} },
    title: "精彩活动",
}));
const __VLS_1 = __VLS_0({
    ...{ 'onLeftClick': {} },
    title: "精彩活动",
}, ...__VLS_functionalComponentArgsRest(__VLS_0));
let __VLS_3;
let __VLS_4;
let __VLS_5;
const __VLS_6 = {
    onLeftClick: (__VLS_ctx.handleBack)
};
var __VLS_2;
/** @type {[typeof Carousel, ]} */ ;
// @ts-ignore
const __VLS_7 = __VLS_asFunctionalComponent(Carousel, new Carousel({
    position: "3",
}));
const __VLS_8 = __VLS_7({
    position: "3",
}, ...__VLS_functionalComponentArgsRest(__VLS_7));
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ class: "section-container" },
});
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ class: "section-header" },
});
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ class: "section-title" },
});
const __VLS_10 = {}.VanIcon;
/** @type {[typeof __VLS_components.VanIcon, typeof __VLS_components.vanIcon, ]} */ ;
// @ts-ignore
const __VLS_11 = __VLS_asFunctionalComponent(__VLS_10, new __VLS_10({
    name: "apps-o",
    color: "#4b8bf4",
    size: "18",
}));
const __VLS_12 = __VLS_11({
    name: "apps-o",
    color: "#4b8bf4",
    size: "18",
}, ...__VLS_functionalComponentArgsRest(__VLS_11));
__VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({});
const __VLS_14 = {}.VanGrid;
/** @type {[typeof __VLS_components.VanGrid, typeof __VLS_components.vanGrid, typeof __VLS_components.VanGrid, typeof __VLS_components.vanGrid, ]} */ ;
// @ts-ignore
const __VLS_15 = __VLS_asFunctionalComponent(__VLS_14, new __VLS_14({
    columnNum: (4),
    border: (false),
    gutter: (10),
    ...{ class: "function-grid navigation-row" },
}));
const __VLS_16 = __VLS_15({
    columnNum: (4),
    border: (false),
    gutter: (10),
    ...{ class: "function-grid navigation-row" },
}, ...__VLS_functionalComponentArgsRest(__VLS_15));
__VLS_17.slots.default;
for (const [item, index] of __VLS_getVForSourceType((__VLS_ctx.navigationItems))) {
    const __VLS_18 = {}.VanGridItem;
    /** @type {[typeof __VLS_components.VanGridItem, typeof __VLS_components.vanGridItem, typeof __VLS_components.VanGridItem, typeof __VLS_components.vanGridItem, ]} */ ;
    // @ts-ignore
    const __VLS_19 = __VLS_asFunctionalComponent(__VLS_18, new __VLS_18({
        key: (index),
        ...{ class: "animate__animated animate__fadeInUp" },
        ...{ style: ({ animationDelay: index * 0.05 + 's' }) },
    }));
    const __VLS_20 = __VLS_19({
        key: (index),
        ...{ class: "animate__animated animate__fadeInUp" },
        ...{ style: ({ animationDelay: index * 0.05 + 's' }) },
    }, ...__VLS_functionalComponentArgsRest(__VLS_19));
    __VLS_21.slots.default;
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
        ...{ onClick: (...[$event]) => {
                __VLS_ctx.onFunctionClick(item);
            } },
        ...{ class: "grid-item-content" },
        ...{ class: ({ active: __VLS_ctx.isActiveRoute(item) }) },
    });
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
        ...{ class: "icon-wrapper" },
        ...{ class: ({ active: __VLS_ctx.isActiveRoute(item) }) },
        ...{ style: (__VLS_ctx.getIconStyle(item)) },
    });
    const __VLS_22 = {}.VanIcon;
    /** @type {[typeof __VLS_components.VanIcon, typeof __VLS_components.vanIcon, ]} */ ;
    // @ts-ignore
    const __VLS_23 = __VLS_asFunctionalComponent(__VLS_22, new __VLS_22({
        name: (item.icon),
        color: (__VLS_ctx.getIconColor(item)),
        size: "24",
    }));
    const __VLS_24 = __VLS_23({
        name: (item.icon),
        color: (__VLS_ctx.getIconColor(item)),
        size: "24",
    }, ...__VLS_functionalComponentArgsRest(__VLS_23));
    __VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({
        ...{ class: "grid-text" },
        ...{ class: ({ active: __VLS_ctx.isActiveRoute(item) }) },
    });
    (item.text);
    var __VLS_21;
}
var __VLS_17;
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ class: "section-container" },
});
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ class: "section-header" },
});
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ class: "section-title" },
});
const __VLS_26 = {}.VanIcon;
/** @type {[typeof __VLS_components.VanIcon, typeof __VLS_components.vanIcon, ]} */ ;
// @ts-ignore
const __VLS_27 = __VLS_asFunctionalComponent(__VLS_26, new __VLS_26({
    name: "play-circle-o",
    color: "#10ac84",
    size: "18",
}));
const __VLS_28 = __VLS_27({
    name: "play-circle-o",
    color: "#10ac84",
    size: "18",
}, ...__VLS_functionalComponentArgsRest(__VLS_27));
__VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({});
/** @type {[typeof ActivityCommonCardList, ]} */ ;
// @ts-ignore
const __VLS_30 = __VLS_asFunctionalComponent(ActivityCommonCardList, new ActivityCommonCardList({
    ...{ 'onCardClick': {} },
    ...{ 'onShare': {} },
    ...{ 'onRegister': {} },
    items: (__VLS_ctx.homeData.activity_data),
    loading: (__VLS_ctx.loading),
    finished: (true),
    useInfiniteScroll: (false),
}));
const __VLS_31 = __VLS_30({
    ...{ 'onCardClick': {} },
    ...{ 'onShare': {} },
    ...{ 'onRegister': {} },
    items: (__VLS_ctx.homeData.activity_data),
    loading: (__VLS_ctx.loading),
    finished: (true),
    useInfiniteScroll: (false),
}, ...__VLS_functionalComponentArgsRest(__VLS_30));
let __VLS_33;
let __VLS_34;
let __VLS_35;
const __VLS_36 = {
    onCardClick: (__VLS_ctx.handleCardClick)
};
const __VLS_37 = {
    onShare: (__VLS_ctx.handleShare)
};
const __VLS_38 = {
    onRegister: (__VLS_ctx.handleRegister)
};
var __VLS_32;
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ class: "section-container" },
});
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ class: "section-header" },
});
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ class: "section-title" },
});
const __VLS_39 = {}.VanIcon;
/** @type {[typeof __VLS_components.VanIcon, typeof __VLS_components.vanIcon, ]} */ ;
// @ts-ignore
const __VLS_40 = __VLS_asFunctionalComponent(__VLS_39, new __VLS_39({
    name: "photo-o",
    color: "#ff6b35",
    size: "18",
}));
const __VLS_41 = __VLS_40({
    name: "photo-o",
    color: "#ff6b35",
    size: "18",
}, ...__VLS_functionalComponentArgsRest(__VLS_40));
__VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({});
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ onClick: (__VLS_ctx.handleViewMoreReviews) },
    ...{ class: "section-more" },
});
__VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({});
const __VLS_43 = {}.VanIcon;
/** @type {[typeof __VLS_components.VanIcon, typeof __VLS_components.vanIcon, ]} */ ;
// @ts-ignore
const __VLS_44 = __VLS_asFunctionalComponent(__VLS_43, new __VLS_43({
    name: "arrow",
    size: "12",
}));
const __VLS_45 = __VLS_44({
    name: "arrow",
    size: "12",
}, ...__VLS_functionalComponentArgsRest(__VLS_44));
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ class: "activity-reviews" },
});
if (__VLS_ctx.reviewLoading) {
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
        ...{ class: "loading-container" },
    });
    const __VLS_47 = {}.VanLoading;
    /** @type {[typeof __VLS_components.VanLoading, typeof __VLS_components.vanLoading, typeof __VLS_components.VanLoading, typeof __VLS_components.vanLoading, ]} */ ;
    // @ts-ignore
    const __VLS_48 = __VLS_asFunctionalComponent(__VLS_47, new __VLS_47({
        size: "24px",
    }));
    const __VLS_49 = __VLS_48({
        size: "24px",
    }, ...__VLS_functionalComponentArgsRest(__VLS_48));
    __VLS_50.slots.default;
    var __VLS_50;
}
else if (!__VLS_ctx.activityReviewGridItems.length) {
    const __VLS_51 = {}.VanEmpty;
    /** @type {[typeof __VLS_components.VanEmpty, typeof __VLS_components.vanEmpty, ]} */ ;
    // @ts-ignore
    const __VLS_52 = __VLS_asFunctionalComponent(__VLS_51, new __VLS_51({
        description: "暂无活动回顾",
    }));
    const __VLS_53 = __VLS_52({
        description: "暂无活动回顾",
    }, ...__VLS_functionalComponentArgsRest(__VLS_52));
}
else {
    /** @type {[typeof GridCard, ]} */ ;
    // @ts-ignore
    const __VLS_55 = __VLS_asFunctionalComponent(GridCard, new GridCard({
        ...{ 'onCardClick': {} },
        items: (__VLS_ctx.activityReviewGridItems),
    }));
    const __VLS_56 = __VLS_55({
        ...{ 'onCardClick': {} },
        items: (__VLS_ctx.activityReviewGridItems),
    }, ...__VLS_functionalComponentArgsRest(__VLS_55));
    let __VLS_58;
    let __VLS_59;
    let __VLS_60;
    const __VLS_61 = {
        onCardClick: (__VLS_ctx.handleReviewGridClick)
    };
    var __VLS_57;
}
const __VLS_62 = {}.VanBackTop;
/** @type {[typeof __VLS_components.VanBackTop, typeof __VLS_components.vanBackTop, ]} */ ;
// @ts-ignore
const __VLS_63 = __VLS_asFunctionalComponent(__VLS_62, new __VLS_62({
    right: "16",
    bottom: "80",
}));
const __VLS_64 = __VLS_63({
    right: "16",
    bottom: "80",
}, ...__VLS_functionalComponentArgsRest(__VLS_63));
/** @type {[typeof GlobalFooter, ]} */ ;
// @ts-ignore
const __VLS_66 = __VLS_asFunctionalComponent(GlobalFooter, new GlobalFooter({}));
const __VLS_67 = __VLS_66({}, ...__VLS_functionalComponentArgsRest(__VLS_66));
/** @type {__VLS_StyleScopedClasses['activity-page']} */ ;
/** @type {__VLS_StyleScopedClasses['section-container']} */ ;
/** @type {__VLS_StyleScopedClasses['section-header']} */ ;
/** @type {__VLS_StyleScopedClasses['section-title']} */ ;
/** @type {__VLS_StyleScopedClasses['function-grid']} */ ;
/** @type {__VLS_StyleScopedClasses['navigation-row']} */ ;
/** @type {__VLS_StyleScopedClasses['animate__animated']} */ ;
/** @type {__VLS_StyleScopedClasses['animate__fadeInUp']} */ ;
/** @type {__VLS_StyleScopedClasses['grid-item-content']} */ ;
/** @type {__VLS_StyleScopedClasses['active']} */ ;
/** @type {__VLS_StyleScopedClasses['icon-wrapper']} */ ;
/** @type {__VLS_StyleScopedClasses['active']} */ ;
/** @type {__VLS_StyleScopedClasses['grid-text']} */ ;
/** @type {__VLS_StyleScopedClasses['active']} */ ;
/** @type {__VLS_StyleScopedClasses['section-container']} */ ;
/** @type {__VLS_StyleScopedClasses['section-header']} */ ;
/** @type {__VLS_StyleScopedClasses['section-title']} */ ;
/** @type {__VLS_StyleScopedClasses['section-container']} */ ;
/** @type {__VLS_StyleScopedClasses['section-header']} */ ;
/** @type {__VLS_StyleScopedClasses['section-title']} */ ;
/** @type {__VLS_StyleScopedClasses['section-more']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-reviews']} */ ;
/** @type {__VLS_StyleScopedClasses['loading-container']} */ ;
var __VLS_dollars;
const __VLS_self = (await import('vue')).defineComponent({
    setup() {
        return {
            GlobalHeader: GlobalHeader,
            GlobalFooter: GlobalFooter,
            Carousel: Carousel,
            ActivityCommonCardList: ActivityCommonCardList,
            GridCard: GridCard,
            loading: loading,
            reviewLoading: reviewLoading,
            navigationItems: navigationItems,
            homeData: homeData,
            activityReviewGridItems: activityReviewGridItems,
            handleCardClick: handleCardClick,
            isActiveRoute: isActiveRoute,
            getIconColor: getIconColor,
            getIconStyle: getIconStyle,
            onFunctionClick: onFunctionClick,
            handleBack: handleBack,
            handleViewMoreReviews: handleViewMoreReviews,
            handleReviewGridClick: handleReviewGridClick,
            handleShare: handleShare,
            handleRegister: handleRegister,
        };
    },
});
export default (await import('vue')).defineComponent({
    setup() {
        return {};
    },
});
; /* PartiallyEnd: #4569/main.vue */
