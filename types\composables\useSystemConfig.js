/**
 * 系统配置组合函数
 * 使用组合式API管理全局配置状态，包含缓存和降级处理
 */
import { ref, computed, readonly } from 'vue';
import { getAllSystemConfig } from '../api/system';
import { CACHE_KEYS, CACHE_DURATION } from '../types/system';
// 默认配置（降级备选）
const DEFAULT_HOSPITAL_INFO = {
    id: 1,
    hospital_name: '古交市中医医院',
    hospital_slogan: '传承千年 · 智慧养生',
    copyright_text: '版权所有',
    copyright_holder: '侯马市中医医院',
    establishment_year: '2020',
    show_auto_year: true
};
const DEFAULT_CONTACT_INFO = {
    id: 1,
    phone: '0351-5216112（公）',
    email: '<EMAIL>',
    address: '太原市古交市火山片区仲景路12号',
    business_hours: '周一至周五 8:00-18:00'
};
const DEFAULT_SEO_CONFIG = {
    id: 1,
    site_title: '中医智慧 - 传承千年中医文化，守护您的健康',
    site_description: '专业的中医健康平台，提供中医知识、养生指导、专家咨询等服务。传承千年中医文化，守护您的健康。',
    site_keywords: '中医,中药,养生,健康,传统医学,中医文化',
    site_author: '中医智慧团队',
    site_name: '中医智慧',
    app_title: '中医智慧',
    nav_title: '中医智慧'
};
// 全局状态
const hospitalInfo = ref(null);
const contactInfo = ref(null);
const seoConfig = ref(null);
const isLoaded = ref(false);
const isLoading = ref(false);
const hasApiData = ref(false); // 标记是否已获取到API数据
/**
 * 缓存工具函数
 */
function setCache(key, data) {
    try {
        const cacheData = {
            data,
            timestamp: Date.now()
        };
        localStorage.setItem(key, JSON.stringify(cacheData));
    }
    catch (error) {
        console.warn('缓存数据失败:', error);
    }
}
function getCache(key) {
    try {
        const cached = localStorage.getItem(key);
        if (!cached)
            return null;
        const cacheData = JSON.parse(cached);
        const now = Date.now();
        // 检查缓存是否过期
        if (now - cacheData.timestamp > CACHE_DURATION) {
            localStorage.removeItem(key);
            return null;
        }
        return cacheData.data;
    }
    catch (error) {
        console.warn('读取缓存失败:', error);
        return null;
    }
}
/**
 * 从缓存加载配置
 */
function loadFromCache() {
    const cachedHospitalInfo = getCache(CACHE_KEYS.HOSPITAL_INFO);
    const cachedContactInfo = getCache(CACHE_KEYS.CONTACT_INFO);
    const cachedSeoConfig = getCache(CACHE_KEYS.SEO_CONFIG);
    if (cachedHospitalInfo) {
        hospitalInfo.value = cachedHospitalInfo;
        hasApiData.value = true;
    }
    if (cachedContactInfo) {
        contactInfo.value = cachedContactInfo;
        hasApiData.value = true;
    }
    if (cachedSeoConfig) {
        seoConfig.value = cachedSeoConfig;
        hasApiData.value = true;
    }
    // 如果有任何缓存数据，标记为已加载
    if (cachedHospitalInfo || cachedContactInfo || cachedSeoConfig) {
        isLoaded.value = true;
    }
}
/**
 * 加载系统配置
 */
async function loadSystemConfig() {
    if (isLoading.value)
        return;
    try {
        isLoading.value = true;
        // 先从缓存加载
        loadFromCache();
        console.log('开始加载系统配置...');
        // 并行请求所有配置
        const config = await getAllSystemConfig();
        console.log('系统配置API响应:', config);
        // 更新状态和缓存
        if (config.hospitalInfo) {
            hospitalInfo.value = config.hospitalInfo;
            setCache(CACHE_KEYS.HOSPITAL_INFO, config.hospitalInfo);
            hasApiData.value = true;
            console.log('医院信息已更新:', config.hospitalInfo);
        }
        if (config.contactInfo) {
            contactInfo.value = config.contactInfo;
            setCache(CACHE_KEYS.CONTACT_INFO, config.contactInfo);
            hasApiData.value = true;
            console.log('联系信息已更新:', config.contactInfo);
        }
        if (config.seoConfig) {
            seoConfig.value = config.seoConfig;
            setCache(CACHE_KEYS.SEO_CONFIG, config.seoConfig);
            hasApiData.value = true;
            console.log('SEO配置已更新:', config.seoConfig);
        }
        isLoaded.value = true;
        console.log('系统配置加载完成');
    }
    catch (error) {
        console.warn('加载系统配置失败，使用默认配置:', error);
        // 静默降级，不影响用户体验
    }
    finally {
        isLoading.value = false;
    }
}
/**
 * 计算属性 - 提供降级处理的配置
 */
const effectiveHospitalInfo = computed(() => {
    return hospitalInfo.value || DEFAULT_HOSPITAL_INFO;
});
const effectiveContactInfo = computed(() => {
    return contactInfo.value || DEFAULT_CONTACT_INFO;
});
const effectiveSeoConfig = computed(() => {
    return seoConfig.value || DEFAULT_SEO_CONFIG;
});
// 计算版权年份范围
const copyrightYearRange = computed(() => {
    const info = effectiveHospitalInfo.value;
    if (!info.show_auto_year) {
        // 如果不自动计算，使用建院年份
        return info.establishment_year;
    }
    const currentYear = new Date().getFullYear();
    const establishmentYear = parseInt(info.establishment_year, 10);
    // 如果建院年份无效，使用当前年份
    if (isNaN(establishmentYear)) {
        return `${currentYear}`;
    }
    return establishmentYear === currentYear
        ? `${establishmentYear}`
        : `${establishmentYear}-${currentYear}`;
});
// 立即开始加载配置（应用启动时）
if (!isLoaded.value && !isLoading.value) {
    loadSystemConfig();
}
/**
 * 系统配置组合函数
 */
export function useSystemConfig() {
    // 如果还未加载过，立即加载
    if (!isLoaded.value && !isLoading.value) {
        loadSystemConfig();
    }
    return {
        // 原始状态（可能为null）
        hospitalInfo: readonly(hospitalInfo),
        contactInfo: readonly(contactInfo),
        seoConfig: readonly(seoConfig),
        // 带降级处理的有效配置
        effectiveHospitalInfo: readonly(effectiveHospitalInfo),
        effectiveContactInfo: readonly(effectiveContactInfo),
        effectiveSeoConfig: readonly(effectiveSeoConfig),
        // 辅助计算属性
        copyrightYearRange: readonly(copyrightYearRange),
        // 状态
        isLoaded: readonly(isLoaded),
        isLoading: readonly(isLoading),
        hasApiData: readonly(hasApiData),
        // 方法
        loadSystemConfig,
        // 便捷访问方法
        getHospitalName: () => effectiveHospitalInfo.value.hospital_name,
        getHospitalSlogan: () => effectiveHospitalInfo.value.hospital_slogan,
        getCopyrightText: () => `© ${copyrightYearRange.value} ${effectiveHospitalInfo.value.copyright_holder} ${effectiveHospitalInfo.value.copyright_text}`,
        getPhone: () => effectiveContactInfo.value.phone,
        getEmail: () => effectiveContactInfo.value.email,
        getAddress: () => effectiveContactInfo.value.address,
        getBusinessHours: () => effectiveContactInfo.value.business_hours,
        getSiteTitle: () => effectiveSeoConfig.value.site_title,
        getNavTitle: () => effectiveSeoConfig.value.nav_title
    };
}
