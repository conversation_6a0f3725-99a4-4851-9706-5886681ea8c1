<template>
  <van-popup v-model:show="showVideoPopup" round closeable close-icon-position="top-right" class="video-popup">
    <div class="video-player">
      <div class="video-title">{{ currentVideo.title }}</div>
      <div class="video-placeholder">
        <van-icon name="play-circle-o" size="48" color="#fff" />
        <div class="video-loading">视频加载中...</div>
      </div>
    </div>
  </van-popup>
</template>

<script setup lang="ts">
import { ref } from 'vue';

// 视频播放相关
const showVideoPopup = ref(false);
const currentVideo = ref({} as any);
</script>

<style scoped>
/* 视频播放弹窗样式 */
.video-popup {
  width: 90%;
  max-width: 360px;
  border-radius: 12px;
  overflow: hidden;
}

.video-player {
  padding: 16px;
}

.video-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 12px;
  text-align: center;
}

.video-placeholder {
  height: 200px;
  background-color: #000;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
}

.video-loading {
  color: #fff;
  margin-top: 12px;
  font-size: 14px;
}
</style>
