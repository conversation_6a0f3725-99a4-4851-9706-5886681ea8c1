<template>
  <div class="leader-detail-container">
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no"
    />

    <!-- 全局页头组件 -->
    <GlobalHeader :title="pageTitle" @left-click="handleBack" />

    <!-- 使用领导详情卡片组件 -->
    <LeaderDetailCard 
      :leader-id="leaderId"
      @loaded="handleLeaderLoaded"
      @error="handleLeaderError"
      @retry="handleRetry"
      ref="leaderCardRef"
    />

    <!-- 返回顶部 -->
    <van-back-top right="16" bottom="80" />

    <!-- 全局页脚组件 -->
    <GlobalFooter />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, computed } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import type { LeaderDetail } from './api';
import GlobalHeader from '../../components/GlobalHeader.vue';
import GlobalFooter from '../../components/GlobalFooter.vue';
import LeaderDetailCard from '../../components/LeaderDetailCard.vue';

const router = useRouter();
const route = useRoute();
const leaderCardRef = ref();

// 领导ID
const leaderId = computed(() => {
  return Array.isArray(route.params.id) ? route.params.id[0] : route.params.id;
});

// 领导数据
const leaderData = ref<LeaderDetail | null>(null);

// 动态页面标题
const pageTitle = computed(() => {
  if (leaderData.value?.name) {
    return `${leaderData.value.name} - 领导详情`;
  }
  return '领导详情';
});

// 返回上一页
const handleBack = () => {
  if (document.referrer && document.referrer !== location.href) {
    router.back();
  } else {
    router.replace('/home');
  }
};

// 处理领导数据加载完成
const handleLeaderLoaded = (data: LeaderDetail) => {
  leaderData.value = data;
  console.log('领导详情加载完成:', data);
};

// 处理加载错误
const handleLeaderError = (error: string) => {
  console.error('领导详情加载失败:', error);
};

// 处理重试
const handleRetry = () => {
  console.log('重试加载领导详情');
};

// 监听路由参数变化
watch(
  () => route.params.id,
  (newId) => {
    if (newId) {
      console.log('领导ID变化，重新加载:', newId);
      // 组件会自动响应leaderId的变化
    }
  }
);

// 暴露刷新方法
const refresh = () => {
  if (leaderCardRef.value) {
    leaderCardRef.value.refresh();
  }
};

defineExpose({
  refresh
});
</script>

<style scoped>
.leader-detail-container {
  padding-bottom: 60px;
  min-height: 100vh;
}
</style>