import { get, requestWithRetry } from "../../api/request";
import {
  VIDEO_URLS,
  ADVERTISE_URLS,
  HOSPITAL_NEWS_URLS,
  buildUrl,
} from "../../api/urls";
import type { HospitalNewsListResponse } from "../../types/news";

/**
 * 获取列表
 * @param params 查询参数
 * @returns 列表数据
 */
export function getAdvertiseList(position: string) {
  return get<AdvertiseListResponse>(ADVERTISE_URLS.LIST, 
    {
      position
    }
  );
}

export function getHospitalNewsList(page: number = 1, pageSize: number = 4) {
  return get<HospitalNewsListResponse>(HOSPITAL_NEWS_URLS.NEWS, {
    page,
    page_size: pageSize
  });
  
}

export function getVideoList(page: number = 1, pageSize: number = 6) {
  return get<VideoListResponse>(VIDEO_URLS.VIDEO, {
    page,
    page_size: pageSize
  });
}

// 数据类型定义
export interface AdvertiseItem {
  id: number;
  name: string;
  linkurl: string;
  thumbnail: string;
}

export interface VideoListItem {
  id: number;
  name: string;
  desc: string;
  thumbnail: string;
  duration: string;
  author: string;
  authorAvatar: string;
}

export interface AdvertiseListResponse {
  results: AdvertiseItem[];
  count: number;
}

export interface VideoListResponse {
  results: VideoListItem[];
  count: number;
}
