import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { getVideoList } from '../api';
import SectionHeader from '../../../components/SectionHeader.vue'; // 引入通用标题组件
import CommonVideoCardList from '../../../components/CommonVideoCardList.vue';
const router = useRouter();
const loading = ref(false);
const error = ref("");
const refreshing = ref(false);
const pageSize = ref(6); // 每次加载5条数据
const finished = ref(false); // 是否已加载完所有数据
/// 列表数据
const items = ref([]);
// 加载列表数据
const loadItems = async () => {
    if (loading.value)
        return;
    loading.value = true;
    const res = await getVideoList();
    // 检查响应数据结构 - 修改这里以适应API返回的实际格式
    if (!res || !res.results || !Array.isArray(res.results)) {
        console.error('API响应格式不正确:', res);
        items.value = [];
        loading.value = false;
        return;
    }
    // 将API返回的数据转换为组件需要的格式
    const formattedItems = res.results.map((item) => ({
        id: item.id,
        name: item.name,
        desc: item.desc,
        thumbnail: item.thumbnail,
        duration: "15:30",
        author: "张医生",
        authorAvatar: "/images/20.jpg",
    }));
    console.log('格式化后的数据:', formattedItems);
    // 更新列表数据
    items.value = formattedItems;
    loading.value = false;
};
// 处理卡片点击
const handleItemClick = (item) => {
    console.log('点击的视频项:', item);
    router.push({ name: 'VideoDetail', params: { id: item.id } });
};
// 初始化
onMounted(() => {
    // 加载列表数据
    loadItems();
});
debugger; /* PartiallyEnd: #3632/scriptSetup.vue */
const __VLS_ctx = {};
let __VLS_components;
let __VLS_directives;
/** @type {__VLS_StyleScopedClasses['section-title']} */ ;
/** @type {__VLS_StyleScopedClasses['video-card']} */ ;
/** @type {__VLS_StyleScopedClasses['video-thumbnail']} */ ;
/** @type {__VLS_StyleScopedClasses['video-card']} */ ;
/** @type {__VLS_StyleScopedClasses['video-thumbnail']} */ ;
// CSS variable injection 
// CSS variable injection end 
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ class: "section-container" },
});
/** @type {[typeof SectionHeader, ]} */ ;
// @ts-ignore
const __VLS_0 = __VLS_asFunctionalComponent(SectionHeader, new SectionHeader({
    title: "热门视频",
    icon: "tv-o",
    showMore: (false),
}));
const __VLS_1 = __VLS_0({
    title: "热门视频",
    icon: "tv-o",
    showMore: (false),
}, ...__VLS_functionalComponentArgsRest(__VLS_0));
/** @type {[typeof CommonVideoCardList, ]} */ ;
// @ts-ignore
const __VLS_3 = __VLS_asFunctionalComponent(CommonVideoCardList, new CommonVideoCardList({
    ...{ 'onLoadMore': {} },
    ...{ 'onCardClick': {} },
    items: (__VLS_ctx.items),
    loading: (__VLS_ctx.loading),
    finished: (__VLS_ctx.finished),
    useInfiniteScroll: (true),
}));
const __VLS_4 = __VLS_3({
    ...{ 'onLoadMore': {} },
    ...{ 'onCardClick': {} },
    items: (__VLS_ctx.items),
    loading: (__VLS_ctx.loading),
    finished: (__VLS_ctx.finished),
    useInfiniteScroll: (true),
}, ...__VLS_functionalComponentArgsRest(__VLS_3));
let __VLS_6;
let __VLS_7;
let __VLS_8;
const __VLS_9 = {
    onLoadMore: (__VLS_ctx.loadItems)
};
const __VLS_10 = {
    onCardClick: (__VLS_ctx.handleItemClick)
};
var __VLS_5;
/** @type {__VLS_StyleScopedClasses['section-container']} */ ;
var __VLS_dollars;
const __VLS_self = (await import('vue')).defineComponent({
    setup() {
        return {
            SectionHeader: SectionHeader,
            CommonVideoCardList: CommonVideoCardList,
            loading: loading,
            finished: finished,
            items: items,
            loadItems: loadItems,
            handleItemClick: handleItemClick,
        };
    },
});
export default (await import('vue')).defineComponent({
    setup() {
        return {};
    },
});
; /* PartiallyEnd: #4569/main.vue */
