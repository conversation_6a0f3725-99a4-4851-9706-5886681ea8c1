<template>
  <div class="news-detail-page">
    <!-- 使用公共页头组件 -->
    <GlobalHeader title="医院新闻" />

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <van-loading type="spinner" color="#1989fa" />
      <p>加载中...</p>
    </div>

    <!-- 错误提示 -->
    <div v-else-if="error" class="error-container">
      <van-empty description="加载失败" image="error">
        <template #description>
          <p>{{ error }}</p>
        </template>
        <van-button round type="primary" @click="fetchHospitalDetail(news.id)">重试</van-button>
      </van-empty>
    </div>

    <!-- 医院新闻内容 -->
    <div v-else class="content">
      <!-- 文章头部信息区域 -->
      <div class="section-container">
        <!-- 文章标题 -->
        <h1 class="news-title">{{ news.name }}</h1>
        
        <!-- 文章标签 -->
        <div class="news-tags" v-if="displayTags.length > 0">
          <!-- 分类标签 -->
          <van-tag type="primary" size="medium" v-if="news.cat_display">
            {{ news.cat_display }}
          </van-tag>
          <van-tag type="primary" size="medium" v-else>
            医院新闻
          </van-tag>
          
          <!-- API返回的标签 -->
          <van-tag 
            v-for="(tag, index) in news.tags" 
            :key="tag"
            :type="getTagType(index)" 
            size="medium"
          >
            {{ tag }}
          </van-tag>
          
          <!-- 动态生成的标签 -->
          <van-tag type="danger" size="medium" v-if="isHotNews">🔥 热门</van-tag>
          <van-tag type="warning" size="medium" v-if="isRecentNews">✨ 最新</van-tag>
        </div>

        <!-- 文章元信息 -->
        <div class="news-meta">
          <div class="meta-item">
            <van-icon name="clock-o" />
            <span class="meta-label">发布时间：</span>
            <span class="meta-value">{{ formatDate(news.create_time) }}</span>
          </div>
          <div class="meta-item">
            <van-icon name="eye-o" />
            <span class="meta-label">浏览次数：</span>
            <span class="meta-value">{{ news.viewtimes_display }}</span>
          </div>
        </div>

        <!-- 文章封面图 -->
        <div class="news-image-container">
          <img :src="news.thumbnail" :alt="news.name" class="news-image card-image-base image-hover-scale" />
        </div>

        <!-- 文章摘要 -->
        <div class="news-summary">
          <div class="summary-header">
            <van-icon name="info-o" color="#1989fa" />
            <span>文章摘要</span>
          </div>
          <div class="summary-content">
            {{ news.desc }}
          </div>
        </div>
      </div>

      <!-- 医院新闻详细内容区 -->
      <div v-if="news.content" class="section-container">
        <div class="section-header">
          <div class="section-title">
            <van-icon name="description-o" color="#4b8bf4" />
            <span>正文内容</span>
          </div>
        </div>
        <div class="news-content" v-html="news.content"></div>
      </div>

    </div>

    <!-- 返回顶部 -->
    <van-back-top right="16" bottom="80" />

    <!-- 使用公共页脚组件 -->
    <GlobalFooter />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import GlobalHeader from '../../components/GlobalHeader.vue'; // 导入通用页头组件
import GlobalFooter from '../../components/GlobalFooter.vue'; // 导入通用页脚组件
import { getHospitalNewsDetailWithRetry } from './api';
import type { HospitalNewsDetail } from './api';
import '../../style/common.css'; // 导入公共样式
import './style.css';

const router = useRouter();
const route = useRoute();
const loading = ref(false);
const error = ref('');

const id = ref<number | null>(null);

// 医院新闻数据
const news = ref<HospitalNewsDetail>({
  id: 0,
  name: '',
  desc: '',
  thumbnail: '',
  content: '',
  create_time: '',
  viewtimes_display: '',
  cat_display: '',
  tags: [],
});

// 计算属性：是否为热门新闻（浏览次数超过100）
const isHotNews = computed(() => {
  const viewCount = parseInt(news.value.viewtimes_display) || 0;
  return viewCount > 100;
});

// 计算属性：是否为最新新闻（7天内发布）
const isRecentNews = computed(() => {
  if (!news.value.create_time) return false;
  const publishDate = new Date(news.value.create_time);
  const now = new Date();
  const diffTime = now.getTime() - publishDate.getTime();
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  return diffDays <= 7;
});

// 计算属性：显示的标签数组
const displayTags = computed(() => {
  const tags = [];
  
  // 分类标签
  if (news.value.cat_display) {
    tags.push(news.value.cat_display);
  } else {
    tags.push('医院新闻');
  }
  
  // API返回的标签
  if (news.value.tags && news.value.tags.length > 0) {
    tags.push(...news.value.tags);
  }
  
  // 动态标签
  if (isHotNews.value) {
    tags.push('热门');
  }
  if (isRecentNews.value) {
    tags.push('最新');
  }
  
  return tags;
});

// 格式化日期
const formatDate = (dateString: string) => {
  if (!dateString) return '';
  const date = new Date(dateString);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  return `${year}-${month}-${day} ${hours}:${minutes}`;
};

// 获取标签类型（用于不同颜色显示）
const getTagType = (index: number): 'default' | 'primary' | 'success' | 'warning' | 'danger' => {
  const types: ('default' | 'primary' | 'success' | 'warning' | 'danger')[] = ['default', 'success', 'warning', 'danger'];
  return types[index % types.length];
};

// 获取医院新闻详情
const fetchHospitalDetail = async (newsNewsId: number) => {
  try {
    loading.value = true;
    const res = await getHospitalNewsDetailWithRetry(newsNewsId);
    console.log('获取到医院新闻详情数据:', res);
    const data = res.hospital_news;

    // 将API返回的数据转换为组件需要的格式
    news.value = {
      id: data.id,
      name: data.name,
      desc: data.desc,
      thumbnail: data.thumbnail,
      content: data.content,
      create_time: data.create_time,
      viewtimes_display: data.viewtimes_display,
      cat_display: data.cat_display || '',
      tags: data.tags || []
    };

  } catch (err) {
    console.error('获取医院新闻详情失败:', err);
    error.value = '获取医院新闻详情失败，请稍后再试';
  } finally {
    loading.value = false;
  }
};

// 初始化
onMounted(() => {
  id.value = Number(route.params.id);
  console.log('医院新闻详情ID:', id.value);
  fetchHospitalDetail(id.value);
});
</script>

<style scoped>
/* 样式已移至 style.css */
</style>
