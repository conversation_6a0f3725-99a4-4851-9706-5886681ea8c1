# System Patterns: TCM H5 Application Architecture

## Architecture Overview

### High-Level System Design

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Frontend      │    │   API Gateway    │    │   Backend       │
│   (Vue 3 SPA)   │◄──►│   (Proxy/CORS)   │◄──►│   Services      │
└─────────────────┘    └──────────────────┘    └─────────────────┘
        │
        ▼
┌─────────────────┐
│   Static Assets │
│   (Images/CDN)  │
└─────────────────┘
```

### Component Hierarchy

```
App.vue
├── GlobalHeader.vue (conditional)
├── router-view (dynamic page components)
│   ├── Home/
│   │   ├── components/Carousel.vue
│   │   ├── components/FunctionGrid.vue
│   │   ├── components/HospitalNews.vue
│   │   └── components/...
│   ├── Knowledge/
│   ├── Activity/
│   └── [Feature Pages]/
└── TabBar (global navigation)
```

## Design Patterns Implementation

### 1. Component Composition Pattern

#### Shared Component Library

```typescript
// Reusable UI Components
-GlobalHeader.vue - // Standard page header
  GlobalFooter.vue - // Standard page footer
  CommonCardList.vue - // List display with pagination
  GridCard.vue - // Card-based content display
  NewsCard.vue - // News article card
  SectionHeader.vue; // Section title component
```

#### Component Props Interface

```typescript
interface CardProps {
  id: string | number;
  title: string;
  image?: string;
  badge?: string;
  subtitle?: string;
  originalData?: any;
}
```

### 2. API Layer Pattern

#### Centralized API Configuration

```typescript
// src/api/urls.ts - URL Management
export const ACTIVITY_URLS = {
  ACTIVITY: "/api/activity/activities/",
  HOME_CONTENT: "/api/activity/home_content/",
  NEWS: "/api/activity/activity_news/",
};

// src/api/request.ts - HTTP Client
export function get<T>(url: string, params?: any): Promise<T>;
export function post<T>(url: string, data?: any): Promise<T>;
```

#### Module-Specific API Pattern

```typescript
// Each view module has its own API file
// src/views/Activity/api.ts
export function getActivityList(params?: any) {
  return get<ActivityListResponse>(ACTIVITY_URLS.ACTIVITY, params);
}
```

### 3. State Management Pattern

#### Composition API State

```typescript
// Component-level reactive state
const { data, loading, error } = useAsyncData(fetchFunction);

// Shared state through composables
export function useCommonState() {
  const loading = ref(false);
  const error = ref<string | null>(null);

  return { loading, error };
}
```

#### Data Flow Pattern

```
API Response → Component State → Template Rendering
     ↓              ↓                    ↓
Error Handling → Loading States → User Feedback
```

### 4. Routing Pattern

#### Route Organization

```typescript
// Modular route definitions
const routes = [
  // Home
  { path: "/home", component: () => import("../views/Home/index.vue") },

  // Feature modules with consistent patterns
  { path: "/{module}", component: () => import("../views/{Module}/index.vue") },
  {
    path: "/{module}-detail/:id",
    component: () => import("../views/{Module}Detail/index.vue"),
  },
  {
    path: "/{module}/:type",
    component: () => import("../views/{Module}/index.vue"),
  },
];
```

#### Navigation Guard Pattern

```typescript
router.beforeEach((to, from, next) => {
  // Global navigation logic
  // Authentication checks
  // Loading states
  next();
});
```

### 5. Error Handling Pattern

#### Layered Error Handling

```typescript
// API Level
axiosInstance.interceptors.response.use(
  (response) => response,
  (error) => {
    // Global error handling
    handleApiError(error);
    return Promise.reject(error);
  }
);

// Component Level
try {
  await apiCall();
} catch (error) {
  // Component-specific error handling
  showErrorMessage(error);
}
```

### 5. Configuration Management Pattern

#### Environment Variable Configuration

```typescript
// Multi-environment configuration support
// .env.development, .env.production, .env.test
interface ImportMetaEnv {
  readonly VITE_API_BASE_URL: string;
  readonly VITE_PROXY_TARGET: string;
  readonly VITE_API_TIMEOUT: string;
}

// src/api/request.ts - Environment-based configuration
const baseURL = import.meta.env.VITE_API_BASE_URL || "";
const timeout = parseInt(import.meta.env.VITE_API_TIMEOUT) || 10000;
```

#### Dynamic System Configuration

```typescript
// src/composables/useSystemConfig.ts - Reactive configuration management
export function useSystemConfig() {
  const hospitalInfo = ref<HospitalInfo | null>(null);
  const contactInfo = ref<ContactInfo | null>(null);
  const seoConfig = ref<SeoConfig | null>(null);

  // 24-hour caching with localStorage
  const loadConfigFromCache = (key: string) => {
    const cached = localStorage.getItem(key);
    if (cached) {
      const { data, timestamp } = JSON.parse(cached);
      if (Date.now() - timestamp < CACHE_DURATION) {
        return data;
      }
    }
    return null;
  };

  // Silent fallback to defaults
  const getEffectiveHospitalInfo = computed(() => {
    return hospitalInfo.value || DEFAULT_HOSPITAL_INFO;
  });

  return {
    hospitalInfo: getEffectiveHospitalInfo,
    contactInfo: getEffectiveContactInfo,
    seoConfig: getEffectiveSeoConfig,
    loadAllConfigs,
  };
}
```

#### Configuration API Pattern

```typescript
// src/api/system.ts - Configuration API endpoints
const SYSTEM_CONFIG_URLS = {
  HOSPITAL_INFO: "/api/system/hospital_info/",
  CONTACT_INFO: "/api/system/contact_info/",
  SEO_CONFIG: "/api/system/seo_config/",
};

export async function getHospitalInfo(): Promise<HospitalInfo> {
  try {
    return await get<HospitalInfo>(SYSTEM_CONFIG_URLS.HOSPITAL_INFO);
  } catch (error) {
    console.warn("Failed to load hospital info:", error);
    return DEFAULT_HOSPITAL_INFO;
  }
}
```

### 6. Vite Configuration Pattern

#### Environment Variable Loading

```typescript
// vite.config.ts - Environment-aware configuration
import { defineConfig, loadEnv } from "vite";

export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd(), "");

  return {
    server: {
      proxy: {
        "/api": {
          target: env.VITE_PROXY_TARGET || "https://houma.sxaliyun.cn",
          changeOrigin: true,
          secure: false,
          rewrite: (path) => path,
        },
      },
    },
  };
});
```

## Component Design Patterns

### 1. List Component Pattern

#### CommonCardList Architecture

```vue
<template>
  <div class="common-card-list">
    <van-list v-model:loading="loading" :finished="finished" @load="onLoad">
      <GridCard
        v-for="item in items"
        :key="item.id"
        v-bind="item"
        @click="handleItemClick"
      />
    </van-list>
  </div>
</template>
```

#### Data Transformation Pattern

```typescript
// Transform API data to component props
const gridItems = computed(() => {
  return apiData.value.map((item) => ({
    id: item.id,
    title: item.name,
    image: item.thumbnail,
    badge: item.category,
    originalData: item,
  }));
});
```

### 2. Detail Page Pattern

#### Consistent Detail Structure

```vue
<template>
  <div class="detail-page">
    <div class="custom-header">
      <van-nav-bar :title="title" left-arrow @click-left="$router.back()" />
    </div>
    <div class="detail-content">
      <!-- Content sections -->
    </div>
    <div class="bottom-actions">
      <!-- Action buttons -->
    </div>
  </div>
</template>
```

### 3. Responsive Layout Pattern

#### Mobile-First Breakpoints

```scss
// Base styles for mobile
.component {
  padding: 16px;

  // Tablet adjustments
  @media (min-width: 768px) {
    padding: 24px;
  }

  // Desktop adjustments
  @media (min-width: 1024px) {
    padding: 32px;
    max-width: 1200px;
    margin: 0 auto;
  }
}
```

## Navigation Patterns

### 1. Tab Bar Navigation

```vue
<!-- Bottom navigation with consistent styling -->
<van-tabbar route fixed placeholder>
  <van-tabbar-item replace to="/home" icon="home-o">首页</van-tabbar-item>
  <van-tabbar-item replace to="/knowledge" icon="bulb-o">知识</van-tabbar-item>
  <van-tabbar-item replace to="/activity" icon="fire-o">活动</van-tabbar-item>
</van-tabbar>
```

### 2. Page Header Pattern

```vue
<!-- Conditional header with back navigation -->
<GlobalHeader v-if="!isHomePage" :title="pageTitle" :show-back="true" />
```

### 3. Grid Navigation Pattern

```vue
<!-- Function grid for feature access -->
<FunctionGrid :items="navigationItems" @item-click="handleNavigation" />
```

## Data Loading Patterns

### 1. Infinite Scroll Pattern

```typescript
const onLoad = async () => {
  try {
    loading.value = true;
    const response = await fetchData({ page: currentPage.value });

    if (response.data.length === 0) {
      finished.value = true;
    } else {
      items.value.push(...response.data);
      currentPage.value++;
    }
  } catch (error) {
    handleError(error);
  } finally {
    loading.value = false;
  }
};
```

### 2. Loading State Management

```typescript
interface LoadingState {
  initial: boolean; // First load
  pagination: boolean; // Loading more items
  refresh: boolean; // Pull to refresh
}
```

### 3. Cache-First Pattern

```typescript
// Simple cache implementation
const cache = new Map();

async function fetchWithCache(url: string) {
  if (cache.has(url)) {
    return cache.get(url);
  }

  const data = await api.get(url);
  cache.set(url, data);
  return data;
}
```

## Styling Architecture

### 1. CSS Organization

```
src/style/
├── global.css          # Global styles and resets
├── variables.css       # CSS custom properties
├── components/         # Component-specific styles
└── responsive.css      # Responsive utilities
```

### 2. Theme Variables

```css
:root {
  --primary-color: #4b8bf4;
  --text-primary: #333;
  --text-secondary: #666;
  --border-color: #eee;
  --background-light: #f8f9fa;
}
```

### 3. Component Scoping

```vue
<style scoped>
/* Component-specific styles */
.component-class {
  /* Styles here don't affect other components */
}
</style>

<style>
/* Global styles when needed */
:deep(.third-party-component) {
  /* Override third-party styles */
}
</style>
```

## Performance Patterns

### 1. Lazy Loading Pattern

```typescript
// Route-based code splitting
const routes = [
  {
    path: "/activity",
    component: () => import("../views/Activity/index.vue"),
  },
];

// Component lazy loading
const HeavyComponent = defineAsyncComponent(
  () => import("../components/HeavyComponent.vue")
);
```

### 2. Image Optimization Pattern

```vue
<template>
  <!-- Lazy loading with placeholder -->
  <van-image
    :src="imageUrl"
    lazy-load
    :loading-icon="placeholderIcon"
    :error-icon="errorIcon"
  />
</template>
```

### 3. Bundle Optimization

```typescript
// Selective imports
import { ref, computed } from "vue";
import { showToast } from "vant";

// Avoid full library imports
import * as Vant from "vant"; // ❌ Don't do this
```

## Security Patterns

### 1. API Security

```typescript
// Token management
const token = localStorage.getItem("auth_token");
if (token) {
  axiosInstance.defaults.headers.common["Authorization"] = `Bearer ${token}`;
}
```

### 2. Input Sanitization

```typescript
// XSS prevention
import DOMPurify from "dompurify";

const sanitizedContent = DOMPurify.sanitize(userInput);
```

### 3. Route Protection

```typescript
// Protected routes
router.beforeEach((to, from, next) => {
  if (to.meta.requiresAuth && !isAuthenticated()) {
    next("/login");
  } else {
    next();
  }
});
```

These patterns provide a consistent, maintainable, and scalable foundation for the TCM H5 application, ensuring code quality and developer productivity while delivering excellent user experience.
