/* 活动页面样式 */
.activity-page {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
  padding-bottom: 80px; /* 为固定的底部导航栏留出空间 */
  position: relative;
  width: 100%;
  max-width: 100vw;
  overflow-x: hidden;
  box-sizing: border-box;
}

/* 头部横幅 */
.header-banner {
  position: relative;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 40px 20px 30px;
  color: white;
  overflow: hidden;
}

.banner-content {
  position: relative;
  z-index: 2;
  text-align: center;
}

.banner-title {
  font-size: 28px;
  font-weight: bold;
  margin: 0 0 8px 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.banner-subtitle {
  font-size: 16px;
  margin: 0;
  opacity: 0.9;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.banner-decoration {
  position: absolute;
  top: -50%;
  right: -20%;
  width: 200px;
  height: 200px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  animation: float 6s ease-in-out infinite;
}

.banner-decoration::before {
  content: '';
  position: absolute;
  top: 30px;
  left: 30px;
  width: 100px;
  height: 100px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 50%;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(180deg); }
}

/* 标签页容器 */
.tabs-container {
  background: white;
  /* 移除浮动效果 box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); */
  border-radius: 20px 20px 0 0;
  margin-top: -20px;
  position: relative;
  z-index: 3;
  padding: 0 10px;
}

.van-tabs__wrap {
  height: 50px;
  border-radius: 20px 20px 0 0;
}

.van-tab {
  font-size: 14px;
  font-weight: 600;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
  color: #666;
  transition: all 0.3s ease;
}

.van-tab .van-icon {
  font-size: 16px;
  margin-bottom: 2px;
}

.van-tab--active {
  color: #667eea;
  transform: scale(1.05);
}

.van-tabs__line {
  background: linear-gradient(90deg, #667eea, #764ba2);
  height: 3px;
  border-radius: 2px;
}

/* 活动内容区域 */
.activity-content {
  padding: 10px 0;
  width: 100%;
}

/* 区块标题 */
.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 18px;
  font-weight: bold;
  color: #333;
  margin: 0 0 15px 0;
  padding: 0 5px;
}

.section-title .van-icon {
  color: #667eea;
  font-size: 20px;
}

/* 推荐活动区域 */
.featured-section {
  margin-bottom: 20px;
}

.featured-card {
  border-radius: 16px;
  overflow: hidden;
  /* 移除浮动效果 box-shadow: 0 8px 24px rgba(102, 126, 234, 0.2); */
  cursor: pointer;
  transition: all 0.3s ease;
  background: white;
  width: 100%;
  display: flex;
  flex-direction: column;
}

.featured-card:hover {
  transform: translateY(-4px);
  /* 移除浮动效果 box-shadow: 0 12px 32px rgba(102, 126, 234, 0.3); */
}

.featured-image-container {
  position: relative;
  height: 220px;
  overflow: hidden;
  width: 100%;
  border-radius: 16px 16px 0 0;
}

.featured-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
  display: block;
}

.featured-card:hover .featured-image {
  transform: scale(1.05);
}

.featured-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
  padding: 40px 20px 20px;
  color: white;
}

.status-tag.featured {
  position: absolute;
  top: 15px;
  right: 15px;
  background: linear-gradient(45deg, #ff6b6b, #ee5a24);
  color: white;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: bold;
  /* 移除浮动效果 box-shadow: 0 2px 8px rgba(255, 107, 107, 0.4); */
}

.featured-title {
  font-size: 20px;
  font-weight: bold;
  margin: 0 0 8px 0;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
}

.featured-desc {
  font-size: 14px;
  margin: 0;
  opacity: 0.9;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

/* 进行中活动区域 */
.ongoing-section {
  margin-bottom: 20px;
}

.activity-grid {
  display: grid;
  gap: 16px;
  margin-top: 15px;
}

.activity-card {
  background: white;
  border-radius: 16px;
  overflow: hidden;
  /* 移除浮动效果 box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08); */
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  width: 100%;
  display: flex;
  flex-direction: column;
}

.activity-card:hover {
  transform: translateY(-2px);
  /* 移除浮动效果 box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12); */
}

.card-image-container {
  position: relative;
  height: 180px;
  overflow: hidden;
  width: 100%;
  border-radius: 16px 16px 0 0;
}

.activity-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
  display: block;
}

.activity-card:hover .activity-image {
  transform: scale(1.03);
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.activity-card:hover .image-overlay {
  opacity: 1;
}

.status-tag {
  position: absolute;
  top: 12px;
  right: 12px;
  padding: 4px 10px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: bold;
  color: white;
  backdrop-filter: blur(10px);
  z-index: 2;
}

.status-tag.ongoing {
  background: linear-gradient(45deg, #10ac84, #00d2d3);
  /* 移除浮动效果 box-shadow: 0 2px 8px rgba(16, 172, 132, 0.4); */
}

.status-tag.upcoming {
  background: linear-gradient(45deg, #ff9ff3, #f368e0);
  /* 移除浮动效果 box-shadow: 0 2px 8px rgba(255, 159, 243, 0.4); */
}

.activity-type {
  position: absolute;
  top: 12px;
  left: 12px;
  background: rgba(0, 0, 0, 0.6);
  color: white;
  padding: 4px 8px;
  border-radius: 8px;
  font-size: 10px;
  backdrop-filter: blur(10px);
}

.card-content {
  padding: 16px;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.activity-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin: 0 0 12px 0;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.activity-meta {
  display: flex;
  flex-direction: column;
  gap: 6px;
  margin-bottom: 12px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: #666;
}

.meta-item .van-icon {
  color: #667eea;
  font-size: 14px;
}

.activity-description {
  font-size: 13px;
  color: #666;
  line-height: 1.5;
  margin: 0 0 12px 0;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 8px;
  border-top: 1px solid #f0f0f0;
}

.participants {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #999;
}

.participants .van-icon {
  color: #667eea;
  font-size: 14px;
}

.price {
  display: flex;
  align-items: baseline;
  gap: 2px;
  color: #667eea;
  font-weight: bold;
}

.price-label {
  font-size: 12px;
}

.price-value {
  font-size: 16px;
}

.free-tag {
  background: linear-gradient(45deg, #26de81, #20bf6b);
  color: white;
  padding: 4px 8px;
  border-radius: 8px;
  font-size: 11px;
  font-weight: bold;
}

/* 浮动操作按钮 */
.fab-container {
  position: fixed;
  bottom: 80px;
  right: 20px;
  z-index: 1000;
}

.fab-button {
  background: linear-gradient(45deg, #667eea, #764ba2);
  border: none;
  /* 移除浮动效果 box-shadow: 0 4px 16px rgba(102, 126, 234, 0.4); */
  font-weight: bold;
  padding: 0 20px;
  height: 48px;
  transition: all 0.3s ease;
}

.fab-button:hover {
  transform: translateY(-2px);
  /* 移除浮动效果 box-shadow: 0 6px 20px rgba(102, 126, 234, 0.5); */
}

.fab-button:active {
  transform: translateY(0);
}

/* 创建活动弹窗 */
.create-activity-popup {
  padding: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #eee;
}

.popup-header h3 {
  margin: 0;
  font-size: 18px;
  color: #333;
}

.popup-header .van-icon {
  font-size: 20px;
  color: #999;
  cursor: pointer;
  padding: 5px;
}

.popup-content {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
  font-size: 16px;
}

/* 功能导航样式 */
.function-grid {
  margin-top: 10px;
}

.grid-item-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative; /* 为底部指示器提供定位基准 */
}

.grid-item-content:hover .icon-wrapper {
  transform: scale(1.1);
  /* 移除浮动效果 box-shadow: 0 6px 15px rgba(102, 126, 234, 0.2); */
}

.icon-wrapper {
  width: 48px;
  height: 48px;
  border-radius: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
  transition: all 0.3s ease;
}

/* 选中状态的图标容器 */
.icon-wrapper.active {
  border-radius: 24px !important; /* 圆形 */
  box-shadow: 0 2px 8px rgba(75, 139, 244, 0.3);
  transform: scale(1.05);
}

/* 选中状态的图标 */
.icon-wrapper.active .van-icon {
  color: #fff !important;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.2));
}

.grid-text {
  font-size: 12px;
  color: #333;
  text-align: center;
  font-weight: 500;
  transition: all 0.3s ease;
}

/* 选中状态的文字 */
.grid-text.active {
  color: #4b8bf4 !important;
  font-weight: 600;
}

/* 选中状态的底部指示器 */
.grid-item-content.active::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 50%;
  transform: translateX(-50%);
  width: 4px;
  height: 4px;
  background: #4b8bf4;
  border-radius: 50%;
  animation: dotFadeIn 0.3s ease;
}

@keyframes dotFadeIn {
  0% {
    opacity: 0;
    transform: translateX(-50%) scale(0);
  }
  100% {
    opacity: 1;
    transform: translateX(-50%) scale(1);
  }
}

/* 响应式设计 */
@media (min-width: 768px) {
  .activity-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .banner-title {
    font-size: 32px;
  }
  
  .banner-subtitle {
    font-size: 18px;
  }
}

@media (min-width: 1024px) {
  .activity-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 加载更多容器 */
.load-more-container {
  text-align: center;
  margin-top: 20px;
  padding: 10px;
}

.no-more-text {
  color: #999;
  font-size: 14px;
  margin: 10px 0;
}

/* 空状态样式 */
.van-empty {
  padding: 40px 0;
  background: #fff;
  border-radius: 8px;
  margin: 20px 0;
}

/* 加载状态 */
.activity-card.loading {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #999;
}

.empty-state .van-icon {
  font-size: 48px;
  color: #ddd;
  margin-bottom: 16px;
}

.empty-state h3 {
  font-size: 16px;
  margin: 0 0 8px 0;
  color: #666;
}

.empty-state p {
  font-size: 14px;
  margin: 0;
  color: #999;
}

/* 滚动优化 */
.activity-list {
  scroll-behavior: smooth;
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .activity-page {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  }
  
  .activity-card {
    background: #2c3e50;
    color: #ecf0f1;
  }
  
  .activity-title {
    color: #ecf0f1;
  }
  
  .activity-description {
    color: #bdc3c7;
  }
  
  .card-footer {
    border-top-color: #34495e;
  }
}
