import{v as T,G as E,V as L,d as z,a as N,u as B,r as d,k as C,l as R,c as i,o as n,b as l,i as h,f as S,g as o,F as $,I as q,t as u,j as v,L as A,B as F,_ as G}from"./index-BE8XLQ59.js";import{E as M}from"./index-B27ddgxZ.js";import{G as H,a as O}from"./GlobalHeader-lL88u8sR.js";import{s as P}from"./function-call-BUl5915X.js";function U(y){return T(E(L.VIDEO,y))}const j={class:"video-promotion-detail-container"},Y={key:0,class:"detail-content"},Z={class:"video-player"},J={class:"video-wrapper"},K=["src"],Q=["src","alt"],W={class:"play-button"},X={class:"video-duration"},ee=["src"],te={class:"detail-header"},oe={class:"detail-title"},ae={class:"detail-meta"},se={class:"author"},ne={class:"date"},le={class:"detail-body"},ie={class:"content-section"},ce={class:"content-section"},re=["innerHTML"],de={key:1,class:"loading-container"},ue=z({__name:"index",setup(y){const _=N(),b=B(),c=d(!1),r=d(""),m=d(!1),g=d(null),V=a=>{const e=a.match(/\/page\/([a-zA-Z0-9]+)\.html/);return e?e[1]:null},k=C(()=>{var a,e;return(e=(a=s.value)==null?void 0:a.video)!=null&&e.includes("v.qq.com/x/page/")?V(s.value.video):null}),s=d(null),D=async()=>{const a=Array.isArray(_.params.id)?_.params.id[0]:_.params.id;if(!a){r.value="视频ID无效";return}try{c.value=!0;const t=(await U(a)).videos;if(!t)throw new Error("数据异常");console.log("API返回的原始视频数据:",t),console.log("创作者字段:",t.creater),console.log("发布时间字段:",t.create_time),s.value={id:t.id,name:t.name,duration:"35:20",cat_display:t.cat_display,desc:t.desc,thumbnail:t.thumbnail.trim(),content:t.content,creater:t.creater||"未知创作者",create_time:t.create_time||new Date().toISOString().split("T")[0],video:t.video,tags:["养生保健","健身运动"]},console.log("处理后的视频详情数据:",s.value)}catch(e){console.error(e),r.value="获取视频失败"}finally{c.value=!1}},w=a=>{if(!a)return"";try{const e=new Date(a);if(isNaN(e.getTime()))return a;const t=e.getFullYear(),p=String(e.getMonth()+1).padStart(2,"0"),f=String(e.getDate()).padStart(2,"0");return`${t}-${p}-${f}`}catch(e){return console.warn("时间格式化失败:",e),a}},x=()=>{m.value=!0,F(()=>{var a;(a=g.value)==null||a.play().catch(()=>{P("无法播放视频")})})},I=()=>{b.back()};return R(D),(a,e)=>{const t=q,p=A,f=M;return n(),i("div",j,[l(H,{title:"视频详情",onLeftClick:I}),!c.value&&!r.value&&s.value?(n(),i("div",Y,[o("div",Z,[o("div",J,[k.value?(n(),i("iframe",{key:0,class:"tencent-video-iframe",src:`https://v.qq.com/txp/iframe/player.html?vid=${k.value}`,frameborder:"0",allowfullscreen:""},null,8,K)):(n(),i($,{key:1},[m.value?(n(),i("video",{key:1,ref_key:"videoRef",ref:g,class:"video-element",controls:"",autoplay:"",src:s.value.video,onEnded:e[0]||(e[0]=ve=>m.value=!1)},null,40,ee)):(n(),i("div",{key:0,class:"video-placeholder",onClick:x},[o("img",{src:s.value.thumbnail,alt:s.value.name,class:"video-thumbnail"},null,8,Q),o("div",W,[l(t,{name:"play",size:"48",color:"white"})]),o("div",X,u(s.value.duration),1)]))],64))])]),o("div",te,[o("h1",oe,u(s.value.name),1),o("div",ae,[o("span",se,[l(t,{name:"user-o",size:"12"}),v(" "+u(s.value.creater),1)]),o("span",ne,[l(t,{name:"clock-o",size:"12"}),v(" "+u(w(s.value.create_time)),1)])])]),o("div",le,[o("div",ie,[o("h3",null,[l(t,{name:"info-o",size:"18",color:"#f5a623"}),e[1]||(e[1]=v(" 视频简介 "))]),o("p",null,u(s.value.desc),1)]),o("div",ce,[o("h3",null,[l(t,{name:"notes-o",size:"18",color:"#722ed1"}),e[2]||(e[2]=v(" 详细介绍 "))]),o("div",{innerHTML:s.value.content},null,8,re)])])])):h("",!0),c.value?(n(),i("div",de,[l(p,{type:"spinner",color:"#1989fa"}),e[3]||(e[3]=o("p",null,"加载中...",-1))])):h("",!0),!c.value&&r.value?(n(),S(f,{key:2,description:r.value},null,8,["description"])):h("",!0),l(O)])}}}),he=G(ue,[["__scopeId","data-v-b49ec3b8"]]);export{he as default};
