<template>
  <div class="grid-card-example">
    <h2>GridCard 组件使用示例</h2>
    
    <!-- 基础用法 -->
    <section class="example-section">
      <h3>基础用法</h3>
      <GridCard 
        :items="basicItems" 
        @card-click="handleCardClick"
        @image-error="handleImageError"
      />
    </section>

    <!-- 带标识的用法 -->
    <section class="example-section">
      <h3>带右下角标识</h3>
      <GridCard 
        :items="badgeItems" 
        @card-click="handleCardClick"
      />
    </section>

    <!-- 不可点击状态 -->
    <section class="example-section">
      <h3>不可点击状态</h3>
      <GridCard 
        :items="basicItems" 
        :clickable="false"
      />
    </section>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { showToast } from 'vant';
import GridCard from './GridCard.vue';
import type { GridCardItem } from './GridCard.vue';

// 基础示例数据
const basicItems = ref<GridCardItem[]>([
  {
    id: 1,
    title: '名医讲堂：高血压的中医治疗方法',
    image: 'https://via.placeholder.com/300x200/4CAF50/white?text=高血压'
  },
  {
    id: 2,
    title: '中医育儿：小儿推拿手法',
    image: 'https://via.placeholder.com/300x200/2196F3/white?text=小儿推拿'
  },
  {
    id: 3,
    title: '急救常识：中医急救方法',
    image: 'https://via.placeholder.com/300x200/FF9800/white?text=急救'
  },
  {
    id: 4,
    title: '中医养生：四季养生之道',
    image: 'https://via.placeholder.com/300x200/9C27B0/white?text=养生'
  }
]);

// 带标识的示例数据
const badgeItems = ref<GridCardItem[]>([
  {
    id: 5,
    title: '最新医疗技术突破',
    image: 'https://via.placeholder.com/300x200/607D8B/white?text=医疗技术',
    badge: '推荐'
  },
  {
    id: 6,
    title: '健康饮食搭配指南',
    image: 'https://via.placeholder.com/300x200/4CAF50/white?text=健康饮食',
    badge: '热门'
  },
  {
    id: 7,
    title: '中医诊断学基础',
    image: 'https://via.placeholder.com/300x200/E91E63/white?text=中医诊断',
    badge: '精选'
  },
  {
    id: 8,
    title: '针灸治疗常见疾病',
    image: 'https://via.placeholder.com/300x200/3F51B5/white?text=针灸',
    badge: '新品'
  }
]);

// 处理卡片点击
const handleCardClick = (item: GridCardItem) => {
  showToast(`点击了: ${item.title}`);
  console.log('卡片点击:', item);
};

// 处理图片错误
const handleImageError = (item: GridCardItem) => {
  console.log('图片加载失败:', item);
  showToast('图片加载失败');
};
</script>

<style scoped>
.grid-card-example {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.example-section {
  margin-bottom: 30px;
  background: white;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.example-section h3 {
  margin: 0 0 15px 0;
  color: #333;
  font-size: 16px;
  font-weight: 600;
}

h2 {
  text-align: center;
  color: #333;
  margin-bottom: 30px;
  font-size: 24px;
  font-weight: 700;
}
</style> 