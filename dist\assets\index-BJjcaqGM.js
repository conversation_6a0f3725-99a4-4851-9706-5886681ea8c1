import{d as T,O as j,Q as N,R as z,S as A,T as K,b as m,U as D,W as M,X as q,Y as Q,Z as X,$ as Z,k as O,a0 as J,a1 as ee,a2 as te,a3 as ne,I as oe,v as V,a4 as se,a5 as ae,V as re,u as ie,r as C,l as le,y as ce,a6 as ue,c as l,o as r,g as y,F as x,e as B,w as U,f as de,a7 as ge,i as pe,n as W,t as me,L as fe,h as he,_ as ve}from"./index-BE8XLQ59.js";import{E as _e}from"./index-B27ddgxZ.js";import{S as ye,a as ke}from"./index-sbxbYdRt.js";import{s as L}from"./function-call-BUl5915X.js";const[G,be]=A("grid"),Se={square:Boolean,center:z,border:z,gutter:N,reverse:Boolean,iconSize:N,direction:String,clickable:Boolean,columnNum:j(4)},F=Symbol(G);var we=T({name:G,props:Se,setup(n,{slots:s}){const{linkChildren:a}=K(F);return a({props:n}),()=>{var k;return m("div",{style:{paddingLeft:D(n.gutter)},class:[be(),{[M]:n.border&&!n.gutter}]},[(k=s.default)==null?void 0:k.call(s)])}}});const Fe=q(we),[Ie,R]=A("grid-item"),$e=Q({},ee,{dot:Boolean,text:String,icon:String,badge:N,iconColor:String,iconPrefix:String,badgeProps:Object});var Le=T({name:Ie,props:$e,setup(n,{slots:s}){const{parent:a,index:k}=X(F),I=Z();if(!a)return;const c=O(()=>{const{square:h,gutter:i,columnNum:u}=a.props,b=`${100/+u}%`,g={flexBasis:b};if(h)g.paddingTop=b;else if(i){const S=D(i);g.paddingRight=S,k.value>=+u&&(g.marginTop=S)}return g}),E=O(()=>{const{square:h,gutter:i}=a.props;if(h&&i){const u=D(i);return{right:u,bottom:u,height:"auto"}}}),$=()=>{if(s.icon)return m(ne,te({dot:n.dot,content:n.badge},n.badgeProps),{default:s.icon});if(n.icon)return m(oe,{dot:n.dot,name:n.icon,size:a.props.iconSize,badge:n.badge,class:R("icon"),color:n.iconColor,badgeProps:n.badgeProps,classPrefix:n.iconPrefix},null)},f=()=>{if(s.text)return s.text();if(n.text)return m("span",{class:R("text")},[n.text])},_=()=>s.default?s.default():[$(),f()];return()=>{const{center:h,border:i,square:u,gutter:b,reverse:g,direction:S,clickable:e}=a.props,t=[R("content",[S,{center:h,square:u,reverse:g,clickable:e,surround:i&&b}]),{[J]:i}];return m("div",{class:[R({square:u})],style:c.value},[m("div",{role:e?"button":void 0,class:t,style:E.value,tabindex:e?0:void 0,onClick:I},[_()])])}}});const He=q(Le);function Ee(n){return V(se.LIST,{position:n})}function Ye(n=1,s=4){return V(ae.NEWS,{page:n,page_size:s})}function je(n=1,s=6){return V(re.VIDEO,{page:n,page_size:s})}const Pe={class:"section-container"},Ce={class:"swipe-container animate__animated animate__fadeIn"},Re={key:0,class:"skeleton-swipe"},xe={key:1},Be={class:"swipe-item-content"},Ne=["src","alt","onLoad","onError"],De=["alt","onLoad","onError"],Te={class:"swipe-caption"},Ve={key:2,class:"image-loading"},ze={class:"custom-indicator"},Oe={key:2,class:"no-data"},Ue=T({__name:"Carousel",props:{position:{type:String,required:!0,description:"广告位置标识，用于获取不同位置的广告数据"},autoplay:{type:Number,default:3e3,description:"轮播图自动切换时间间隔(ms)"},height:{type:String,default:"200px",description:"轮播图高度"}},emits:["click"],setup(n,{emit:s}){const a=n,k=s,I=ie(),c=C([]),E=C(0),$=C(!0),f=C([]),_=e=>{if(!e)return"";if(e.startsWith("http://")||e.startsWith("https://"))return e;const t="https://houma.sxaliyun.cn",o=e.startsWith("/")?e:`/${e}`;return`${t}${o}`},h=e=>{f.value[e]=!1,console.log(`轮播图${e+1}加载成功`)},i=e=>{f.value[e]=!1,console.error(`轮播图${e+1}加载失败`)},u=async e=>{if(console.log("开始预加载轮播图片..."),f.value=new Array(e.length).fill(!0),e.length>0){const t=new Image;t.src=_(e[0].thumbnail),t.onload=()=>{console.log("首张轮播图预加载完成"),f.value[0]=!1},t.onerror=()=>{console.error("首张轮播图预加载失败"),f.value[0]=!1}}e.slice(1).forEach((t,o)=>{setTimeout(()=>{const v=new Image;v.src=_(t.thumbnail),v.onload=()=>{console.log(`轮播图${o+2}预加载完成`)}},(o+1)*200)})},b=e=>{E.value=e},g=async()=>{try{$.value=!0;const e=await Ee(a.position);if(console.log(`获取到${a.position}位置的轮播列表:`,e),!e||!Array.isArray(e)||e.length===0){c.value=[];return}const t=e.map(o=>({id:typeof o.id=="number"?o.id:Number(o.id)||0,name:o.name||"",linkurl:o.linkurl||"",thumbnail:o.thumbnail||o.url||""}));c.value=[...t],console.log(`${a.position}位置轮播图数据:`,c.value),c.value.forEach((o,v)=>{console.log(`轮播图${v+1}: ${o.name} -> ${o.linkurl}`),console.log(`处理后的图片URL: ${_(o.thumbnail)}`)}),u(c.value)}catch(e){console.error(`获取${a.position}位置的广告数据失败:`,e),L("获取数据失败，请稍后重试")}finally{$.value=!1}},S=e=>{if(!e.linkurl){L("链接地址不存在");return}console.log("轮播图点击链接:",e.linkurl),k("click",e);try{const t=e.linkurl.trim();if(!t){L("链接地址为空");return}if(t.includes("#/")){const v=t.indexOf("#/"),w=t.substring(v+1);console.log("提取的路由路径:",w),w&&w.length>1?I.push(w):L("无效的路由路径");return}if((t.startsWith("http://")||t.startsWith("https://"))&&!t.includes("#/")){window.open(t,"_blank");return}if(t.startsWith("/")){I.push(t);return}const o=`/${t}`;console.log("作为相对路径处理:",o),I.push(o)}catch(t){console.error("轮播图链接跳转失败:",t),L("链接跳转失败，请联系管理员检查链接格式")}};return le(()=>{g()}),ce(()=>a.position,(e,t)=>{e!==t&&g()},{immediate:!1}),(e,t)=>{const o=fe,v=ke,w=ye,H=_e,Y=ue("lazy");return r(),l("div",Pe,[y("div",Ce,[$.value?(r(),l("div",Re,[(r(),l(x,null,B(3,d=>y("div",{class:"skeleton-item",key:d},t[0]||(t[0]=[y("div",{class:"skeleton-image"},null,-1),y("div",{class:"skeleton-caption"},null,-1)]))),64))])):c.value.length>0?(r(),l("div",xe,[m(w,{autoplay:n.autoplay,"show-indicators":!1,class:"my-swipe",onChange:b},{default:U(()=>[(r(!0),l(x,null,B(c.value,(d,p)=>(r(),de(v,{key:d.id,onClick:P=>S(d)},{default:U(()=>[y("div",Be,[p===0?(r(),l("img",{key:0,src:_(d.thumbnail),class:"swipe-image",style:W({height:n.height}),alt:d.name,onLoad:P=>h(p),onError:P=>i(p)},null,44,Ne)):ge((r(),l("img",{key:1,class:"swipe-image",style:W({height:n.height}),alt:d.name,onLoad:P=>h(p),onError:P=>i(p)},null,44,De)),[[Y,_(d.thumbnail)]]),y("div",Te,me(d.name),1),f.value[p]?(r(),l("div",Ve,[m(o,{type:"spinner",color:"#fff"})])):pe("",!0)])]),_:2},1032,["onClick"]))),128))]),_:1},8,["autoplay"]),y("div",ze,[(r(!0),l(x,null,B(c.value,(d,p)=>(r(),l("div",{key:p,class:he(["indicator-dot",{active:E.value===p}])},null,2))),128))])])):(r(),l("div",Oe,[m(H,{image:"empty",description:"暂无数据"})]))])])}}}),Ke=ve(Ue,[["__scopeId","data-v-fee4ff44"]]);export{Ke as C,Fe as G,He as a,je as b,Ye as g};
