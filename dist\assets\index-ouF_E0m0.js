import{b0 as E,d as L,R as C,a9 as R,O as w,S as _,r as h,aG as y,k as N,y as b,b1 as j,l as A,ac as K,ak as O,b as r,B as U,aK as m,aJ as F,L as G,X as H}from"./index-BE8XLQ59.js";const I=Symbol(),J=()=>E(I,null),[M,o,V]=_("list"),X={error:<PERSON><PERSON><PERSON>,offset:w(300),loading:<PERSON><PERSON><PERSON>,disabled:<PERSON><PERSON><PERSON>,finished:<PERSON><PERSON><PERSON>,scroller:Object,errorText:String,direction:R("down"),loadingText:String,finishedText:String,immediateCheck:C};var Y=L({name:M,props:X,emits:["load","update:error","update:loading"],setup(e,{emit:c,slots:a}){const l=h(e.loading),u=h(),g=h(),s=J(),T=y(u),v=N(()=>e.scroller||T.value),n=()=>{U(()=>{if(l.value||e.finished||e.disabled||e.error||(s==null?void 0:s.value)===!1)return;const{direction:t}=e,d=+e.offset,i=m(v);if(!i.height||F(u))return;let f=!1;const x=m(g);t==="up"?f=i.top-x.top<=d:f=x.bottom-i.bottom<=d,f&&(l.value=!0,c("update:loading",!0),c("load"))})},S=()=>{if(e.finished){const t=a.finished?a.finished():e.finishedText;if(t)return r("div",{class:o("finished-text")},[t])}},k=()=>{c("update:error",!1),n()},P=()=>{if(e.error){const t=a.error?a.error():e.errorText;if(t)return r("div",{role:"button",class:o("error-text"),tabindex:0,onClick:k},[t])}},B=()=>{if(l.value&&!e.finished&&!e.disabled)return r("div",{class:o("loading")},[a.loading?a.loading():r(G,{class:o("loading-icon")},{default:()=>[e.loadingText||V("loading")]})])};return b(()=>[e.loading,e.finished,e.error],n),s&&b(s,t=>{t&&n()}),j(()=>{l.value=e.loading}),A(()=>{e.immediateCheck&&n()}),K({check:n}),O("scroll",n,{target:v,passive:!0}),()=>{var t;const d=(t=a.default)==null?void 0:t.call(a),i=r("div",{ref:g,class:o("placeholder")},null);return r("div",{ref:u,role:"feed",class:o(),"aria-busy":l.value},[e.direction==="down"?d:i,B(),S(),P(),e.direction==="up"?d:i])}}});const z=H(Y);export{z as L,I as T};
