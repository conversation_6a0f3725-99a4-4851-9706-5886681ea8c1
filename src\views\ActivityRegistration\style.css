/* 活动报名页面样式 - 与活动页保持一致 */
.activity-registration-page {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
  padding-bottom: 80px; /* 为页脚留出空间 */
  position: relative;
  width: 100%;
  max-width: 100vw;
  overflow-x: hidden;
  box-sizing: border-box;
}

/* 自定义导航栏 */
.custom-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  height: 44px;
}

.back-icon {
  font-size: 20px;
  color: #333;
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.back-icon:hover {
  background: rgba(0, 0, 0, 0.1);
  transform: scale(1.1);
}

.header-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.header-placeholder {
  width: 36px;
}

/* 加载和错误状态 */
.loading-container, .error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  padding: 20px;
}

.loading-container p {
  margin-top: 16px;
  color: #666;
  font-size: 14px;
}

/* 主要内容区域 */
.content {
  padding-top: 60px;
  width: 100%;
  box-sizing: border-box;
}

/* 活动信息卡片 - 使用section-container样式 */
.activity-info-card {
  margin: 5px 5px 24px 5px;
  padding: 5px;
  width: auto;
  box-sizing: border-box;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.activity-image-container {
  position: relative;
  height: 160px;
  overflow: hidden;
}

.activity-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.activity-status {
  position: absolute;
  top: 12px;
  right: 12px;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  color: white;
  backdrop-filter: blur(10px);
}

.activity-status.upcoming {
  background: rgba(75, 139, 244, 0.9);
}

.activity-status.ongoing {
  background: rgba(16, 172, 132, 0.9);
}

.activity-status.ended {
  background: rgba(156, 163, 175, 0.9);
}

.activity-info {
  padding: 16px;
}

.activity-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin: 0 0 12px 0;
  line-height: 1.4;
}

.activity-meta {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #666;
}

.meta-item .van-icon {
  color: #4b8bf4;
}

/* 报名状态提示 - 使用section-container样式 */
.registration-status {
  margin: 5px 5px 24px 5px;
  padding: 16px;
  width: auto;
  box-sizing: border-box;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 15px;
  font-weight: 500;
}

.registration-status.status-upcoming {
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%) !important;
  color: #1976d2;
  border: 1px solid #90caf9;
}

.registration-status.status-ongoing {
  background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%) !important;
  color: #388e3c;
  border: 1px solid #81c784;
}

.registration-status.status-ended {
  background: linear-gradient(135deg, #fafafa 0%, #f5f5f5 100%) !important;
  color: #757575;
  border: 1px solid #e0e0e0;
}

.registration-status .van-icon {
  font-size: 18px;
}

/* 报名表单容器 - 使用section-container样式 */
.registration-form-container {
  margin: 5px 5px 24px 5px;
  padding: 5px;
  width: auto;
  box-sizing: border-box;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.form-header {
  padding: 20px 20px 16px;
  border-bottom: 1px solid #f0f0f0;
  text-align: center;
}

.form-header h3 {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin: 0 0 8px 0;
}

.form-header p {
  font-size: 14px;
  color: #666;
  margin: 0;
}

/* 表单区域 */
.form-section {
  padding: 20px;
  border-bottom: 1px solid #f8f9fa;
}

.form-section:last-child {
  border-bottom: none;
}

/* 确保表单整体布局优化 */
.form-section .van-cell {
  padding: 0 !important;
  background: transparent !important;
  align-items: center;
}

/* 优化表单字段的内部布局 */
.form-section .van-field .van-field__body {
  display: flex;
  align-items: center;
  min-height: 44px;
}

.form-section h4 {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0 0 16px 0;
  padding-left: 12px;
  position: relative;
}

.form-section h4::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 16px;
  background: linear-gradient(135deg, #4b8bf4, #1976d2);
  border-radius: 2px;
}

/* 表单字段样式 */
.form-section .van-field {
  margin-bottom: 16px;
  border-radius: 12px;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  transition: all 0.3s ease;
  align-items: center;
  min-height: 54px; /* 确保最小高度 */
  padding: 0 16px; /* 增加左右内边距 */
}

/* 优化表单标签样式 */
.form-section .van-field .van-field__label {
  color: #333;
  font-weight: 500;
  font-size: 15px;
  white-space: nowrap; /* 防止标签文字换行 */
  flex-shrink: 0; /* 防止标签被压缩 */
}

/* 优化输入框样式 */
.form-section .van-field .van-field__control {
  font-size: 15px;
  line-height: 1.5;
}

/* 优化placeholder样式 */
.form-section .van-field .van-field__control::placeholder {
  color: #999;
  font-size: 14px;
}

.form-section .van-field:focus-within {
  border-color: #4b8bf4;
  box-shadow: 0 0 0 3px rgba(75, 139, 244, 0.1);
  background: white;
}

.form-section .van-field:last-child {
  margin-bottom: 0;
}

/* 单选按钮组 */
.van-radio-group {
  display: flex;
  gap: 24px;
}

.van-radio {
  flex: none;
}

/* 协议区域 */
.agreement-section {
  padding: 20px;
  border-top: 1px solid #f0f0f0;
}

.agreement-link {
  color: #4b8bf4;
  text-decoration: underline;
  cursor: pointer;
}

.agreement-link:hover {
  color: #1976d2;
}

/* 提交区域 */
.submit-section {
  padding: 0 20px 50px;
}

.submit-btn {
  height: 48px;
  font-size: 16px;
  font-weight: 600;
  background: linear-gradient(135deg, #4b8bf4 0%, #1976d2 100%);
  border: none;
  box-shadow: 0 4px 15px rgba(75, 139, 244, 0.3);
  transition: all 0.3s ease;
}

.submit-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(75, 139, 244, 0.4);
}

.submit-btn:disabled {
  background: #ccc;
  box-shadow: none;
  transform: none;
}

/* 报名已关闭状态 - 使用section-container样式 */
.registration-disabled {
  margin: 5px 5px 24px 5px;
  padding: 40px 20px;
  width: auto;
  box-sizing: border-box;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

/* 协议弹窗 */
.agreement-popup {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: white;
}

.popup-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
  background: #f8f9fa;
}

.popup-header h3 {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.popup-header .van-icon {
  font-size: 20px;
  color: #666;
  cursor: pointer;
  padding: 4px;
}

.popup-content {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
}

.agreement-content h4 {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 20px 0 12px 0;
}

.agreement-content h4:first-child {
  margin-top: 0;
}

.agreement-content p {
  font-size: 14px;
  line-height: 1.6;
  color: #555;
  margin: 8px 0;
  padding-left: 16px;
}

.popup-footer {
  padding: 16px 20px;
  border-top: 1px solid #f0f0f0;
  background: #f8f9fa;
}

.popup-footer .van-button {
  height: 44px;
  font-size: 16px;
  font-weight: 600;
}

/* 响应式设计 - 与活动页保持一致 */
@media (max-width: 374px) {
  .activity-title {
    font-size: 16px;
  }
  
  .form-section {
    padding: 12px;
  }
  
  .form-header {
    padding: 12px;
  }
  
  .success-header {
    padding: 30px 16px;
  }
  
  /* 小屏幕下的表单优化 */
  .form-section .van-field {
    padding: 0 12px;
    min-height: 52px;
  }
  
  .form-section .van-field .van-field__label {
    font-size: 14px;
  }
  
  .form-section .van-field .van-field__control {
    font-size: 14px;
  }
  
  .van-field__button .van-button {
    height: 34px;
    padding: 0 12px;
    font-size: 12px;
    min-width: 70px;
  }
}

@media (min-width: 375px) and (max-width: 413px) {
  .form-section {
    padding: 16px;
  }
  
  .form-header {
    padding: 16px;
  }
  
  .success-header {
    padding: 35px 18px;
  }
}

@media (min-width: 414px) and (max-width: 767px) {
  .form-section {
    padding: 20px;
  }
  
  .form-header {
    padding: 20px 20px 16px;
  }
  
  .success-header {
    padding: 40px 20px;
  }
}

/* 动画效果 - 与活动页保持一致 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.activity-info-card {
  animation: fadeInUp 0.6s ease-out;
}

.registration-status {
  animation: fadeInUp 0.6s ease-out 0.1s both;
}

.registration-form-container {
  animation: fadeInUp 0.6s ease-out 0.2s both;
}

.registration-disabled {
  animation: fadeInUp 0.6s ease-out;
}

/* 减少动画的用户偏好 */
@media (prefers-reduced-motion: reduce) {
  .activity-info-card,
  .registration-status,
  .registration-form-container,
  .registration-disabled {
    animation: none;
  }
}

/* 报名成功页面 */
.success-page {
  padding-top: 60px;
  min-height: 100vh;
}

.success-container {
  padding: 20px 5px;
}

.success-header {
  text-align: center;
  padding: 40px 20px;
  margin: 5px 5px 24px 5px;
  width: auto;
  box-sizing: border-box;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.success-icon {
  font-size: 64px;
  color: #10ac84;
  margin-bottom: 16px;
}

.success-header h2 {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin: 0 0 8px 0;
}

.success-header p {
  font-size: 16px;
  color: #666;
  margin: 0;
}

/* 报名信息卡片 - 使用section-container样式 */
.registration-info-card {
  margin: 5px 5px 24px 5px;
  padding: 20px;
  width: auto;
  box-sizing: border-box;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.registration-info-card h3 {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin: 0 0 16px 0;
  padding-bottom: 12px;
  border-bottom: 1px solid #f0f0f0;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f8f9fa;
}

.info-item:last-child {
  border-bottom: none;
}

.info-item .label {
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

.info-item .value {
  font-size: 14px;
  color: #333;
  font-weight: 600;
}

/* 二维码卡片 - 使用section-container样式 */
.qrcode-card {
  margin: 5px 5px 24px 5px;
  padding: 20px;
  width: auto;
  box-sizing: border-box;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  text-align: center;
}

.qrcode-card h3 {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin: 0 0 8px 0;
}

.qrcode-card p {
  font-size: 14px;
  color: #666;
  margin: 0 0 20px 0;
}

.qrcode-container {
  display: flex;
  justify-content: center;
  margin-bottom: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 12px;
}

.qrcode {
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.qrcode-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
}

.qrcode-actions .van-button {
  flex: 1;
  max-width: 120px;
}

/* 温馨提示卡片 - 使用section-container样式 */
.tips-card {
  margin: 5px 5px 24px 5px;
  padding: 20px;
  width: auto;
  box-sizing: border-box;
  background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%);
  border-radius: 8px;
  border: 1px solid #ffcc02;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.tips-card h3 {
  font-size: 16px;
  font-weight: 600;
  color: #e65100;
  margin: 0 0 12px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.tips-card h3::before {
  content: '💡';
  font-size: 18px;
}

.tips-card ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.tips-card li {
  position: relative;
  padding: 8px 0 8px 20px;
  font-size: 14px;
  line-height: 1.5;
  color: #bf360c;
}

.tips-card li::before {
  content: '•';
  position: absolute;
  left: 0;
  top: 8px;
  color: #ff9800;
  font-weight: bold;
}

/* 成功页面操作按钮 */
.success-actions {
  margin-top: 20px;
}

.success-actions .van-button {
  height: 48px;
  font-size: 16px;
  font-weight: 600;
}

/* 短信验证码按钮优化 */
.van-field__button .van-button {
  height: 36px;
  padding: 0 16px;
  font-size: 13px;
  border-radius: 18px;
  min-width: 80px; /* 确保按钮最小宽度 */
  flex-shrink: 0; /* 防止按钮被压缩 */
  white-space: nowrap; /* 防止文字换行 */
}

.van-field__button .van-button:disabled {
  background: #f5f5f5;
  color: #ccc;
}

/* 确保验证码字段有足够空间 */
.form-section .van-field--has-button .van-field__control {
  margin-right: 12px; /* 与按钮保持间距 */
}

/* 优化图标样式 */
.form-section .van-field .van-field__left-icon {
  color: #4b8bf4;
  font-size: 16px;
  margin-right: 12px;
  flex-shrink: 0; /* 防止图标被压缩 */
}

/* 滚动条样式 */
.popup-content::-webkit-scrollbar {
  width: 4px;
}

.popup-content::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.popup-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 2px;
}

.popup-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 成功页面动画 */
.success-page {
  animation: fadeIn 0.6s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.registration-info-card {
  animation: slideInUp 0.6s ease-out 0.1s both;
}

.qrcode-card {
  animation: slideInUp 0.6s ease-out 0.2s both;
}

.tips-card {
  animation: slideInUp 0.6s ease-out 0.3s both;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
} 