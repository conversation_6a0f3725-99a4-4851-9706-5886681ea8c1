# 中医文化推广 H5 应用

## 项目概述

本项目是一个基于 Vue 3 + TypeScript + Vant UI 的响应式 H5 应用，致力于中医文化、健康知识的传播与互动。系统集成了知识库、活动、专家、科室、案例、视频宣传等丰富内容，支持用户个性化管理和学习。

## 技术架构

- **前端框架**：Vue 3 + TypeScript
- **UI 组件库**：Vant 4.x
- **路由管理**：Vue Router 4
- **HTTP 客户端**：Axios（二次封装，支持拦截器、环境切换、错误处理）
- **构建工具**：Vite
- **动画库**：Animate.css
- **代码规范**：ESLint + Prettier
- **自动导入**：unplugin-auto-import, unplugin-vue-components

## 项目架构详细分析

### 1. API 接口架构设计

#### 1.1 请求封装层 (`src/api/request.ts`)

项目采用统一的 API 请求封装，基于 Axios 实现：

```typescript
// 环境配置
const API_BASE_URL = {
  development: "", // 开发环境使用代理
  production: "https://zyy.sxaliyun.cn",
  test: "https://test-api.zyy.sxaliyun.cn",
};

// 统一请求方法
export function get<T>(url: string, params?: any): Promise<T>;
export function post<T>(url: string, data?: any): Promise<T>;
export function requestWithRetry<T>(
  requestFn: () => Promise<T>,
  retries = 3
): Promise<T>;
```

**核心特性：**

- **环境自适应**：根据 `import.meta.env.MODE` 自动切换 API 地址
- **请求拦截器**：自动注入 Authorization token
- **响应拦截器**：统一错误处理、业务状态码判断、登录态管理
- **重试机制**：网络错误时自动重试，支持指数退避
- **错误处理**：开发环境显示 Toast 提示，生产环境仅控制台输出

#### 1.2 API URL 管理 (`src/api/urls.ts`)

采用模块化 URL 管理，便于维护：

```typescript
// 活动相关API
export const ACTIVITY_URLS = {
  ACTIVITY: "/api/activity/activities/",
  HOME_CONTENT: "/api/activity/home_content/",
  NEWS: "/api/activity/activity_news/",
};

// URL构建工具
export function buildUrl(baseUrl: string, id?: string | number): string {
  return id ? `${baseUrl}${id}/` : baseUrl;
}
```

#### 1.3 模块化 API 设计

每个业务模块都有独立的 API 文件，例如：

```typescript
// src/views/Activity/api.ts
export function getActivityList(params?: any) {
  return get<ActivityListResponse>(ACTIVITY_URLS.ACTIVITY, params);
}

export function getActivityDetail(id: string | number) {
  return get<ActivityDetailResponse>(buildUrl(ACTIVITY_URLS.ACTIVITY, id));
}
```

### 2. 页面布局架构

#### 2.1 响应式布局设计

项目采用移动端优先的响应式设计：

```css
/* 移动端基础断点 */
@media (max-width: 374px) /* 超小屏手机 */ @media (max-width: 480px) /* 小屏手机 */ @media (max-width: 768px) /* 平板 */ @media (min-width: 768px); /* 桌面端 */
```

#### 2.2 全局布局组件

**GlobalHeader 组件** (`src/components/GlobalHeader.vue`)：

- 自适应返回按钮显示逻辑
- 毛笔字效果的标题样式
- 渐变背景和毛玻璃效果
- 响应式字体和间距调整

**GlobalFooter 组件** (`src/components/GlobalFooter.vue`)：

- 统一的页脚装饰
- 中医主题的视觉设计
- 响应式布局适配

#### 2.3 页面布局模式

项目中主要采用以下几种布局模式：

1. **列表页布局**：

```vue
<template>
  <div class="page-container">
    <GlobalHeader :title="pageTitle" />
    <div class="content-section">
      <!-- 内容区域 -->
    </div>
    <GlobalFooter />
  </div>
</template>
```

2. **详情页布局**：

```vue
<template>
  <div class="detail-page">
    <div class="custom-header">
      <!-- 自定义导航栏 -->
    </div>
    <div class="detail-content">
      <!-- 详情内容 -->
    </div>
    <div class="bottom-actions">
      <!-- 底部操作栏 -->
    </div>
  </div>
</template>
```

### 3. 组件设计架构

#### 3.1 通用列表组件

**CommonCardList 组件** (`src/components/CommonCardList.vue`)：

- 支持无限滚动和分页加载
- 统一的卡片样式和交互效果
- 加载状态和空状态处理
- 响应式网格布局

**GridCard 组件** (`src/components/GridCard.vue`)：

- 网格布局的卡片展示
- 支持图片、标题、标签等元素
- 点击事件和错误处理
- 移动端触摸优化

#### 3.2 组件数据流设计

```typescript
// 父组件数据准备
const gridItems = computed(() => {
  return items.value.map((item: ActivityNewsItem) => ({
    id: item.id,
    title: item.name,
    image: item.thumbnail,
    badge: "新闻",
    subtitle: item.desc,
    originalData: item, // 保留原始数据
  }));
});

// 子组件事件处理
const handleGridCardClick = (item: any) => {
  const newsData = item.originalData || item;
  router.push({ name: "ActivityNewsDetail", params: { id: newsData.id } });
};
```

### 4. 路由设计与页面跳转逻辑

#### 4.1 路由配置 (`src/router/index.ts`)

采用 Hash 模式路由，支持懒加载：

```typescript
const routes = [
  {
    path: "/activity-detail/:id",
    name: "ActivityDetail",
    component: () => import("../views/ActivityDetail/index.vue"),
  },
  {
    path: "/activity-news-detail/:id",
    name: "ActivityNewsDetail",
    component: () => import("../views/ActivityNewsDetail/index.vue"),
  },
];
```

#### 4.2 页面跳转逻辑

项目中实现了智能的页面跳转逻辑：

```typescript
// 活动回顾跳转到活动新闻详情
const handleReviewGridClick = (item: any) => {
  const activityData = item.originalData || item;
  router.push({ name: "ActivityNewsDetail", params: { id: activityData.id } });
};

// 正在进行的活动跳转到活动详情
const handleCardClick = (news: any) => {
  router.push({
    name: "ActivityDetail",
    params: { id: news.id },
    query: { from: "ongoing" },
  });
};
```

### 5. 数据流管理

#### 5.1 页面数据加载模式

```typescript
// 标准数据加载模式
const loadItems = async () => {
  if (finished.value || loading.value) return;

  loading.value = true;
  try {
    const res = await getActivityNewsListlWithRetry({
      page: page.value,
      page_size: pageSize.value,
    });

    items.value.push(...res.results);
    page.value += 1;
    finished.value = res.is_last_page === true;
  } catch (error) {
    console.error("加载失败:", error);
  } finally {
    loading.value = false;
  }
};
```

#### 5.2 数据转换与适配

项目中实现了灵活的数据转换机制：

```typescript
// API数据转换为组件数据
const activityReviewGridItems = computed(() => {
  return activityNews.value.map((item) => ({
    id: item.id,
    title: item.name,
    image: item.thumbnail,
    badge: "回顾",
    originalData: item,
  }));
});
```

### 6. 样式架构设计

#### 6.1 全局样式系统 (`src/style.css`)

```css
:root {
  /* 中医药主题色彩 */
  --primary-color: #667eea;
  --secondary-color: #764ba2;
  --accent-color: #26de81;

  /* 响应式字体 */
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "PingFang SC";

  /* 移动端优化 */
  -webkit-text-size-adjust: 100%;
  -webkit-font-smoothing: antialiased;
}
```

#### 6.2 响应式设计策略

```css
/* 移动端优先设计 */
.container {
  padding: 0 16px;
}

@media (max-width: 480px) {
  .container {
    padding: 0 8px;
  }
}

@media (min-width: 768px) {
  .container {
    max-width: 768px;
    margin: 0 auto;
  }
}
```

#### 6.3 组件样式隔离

每个组件都有独立的样式文件，采用 scoped CSS：

```vue
<style scoped>
.component-container {
  /* 组件特定样式 */
}
</style>
```

### 7. 性能优化策略

#### 7.1 代码分割与懒加载

- 路由级别的代码分割
- 组件按需导入
- 图片懒加载

#### 7.2 请求优化

- API 请求去重
- 请求缓存机制
- 错误重试策略

#### 7.3 渲染优化

- 虚拟滚动（大列表）
- 图片预加载
- 动画性能优化

### 8. 错误处理与用户体验

#### 8.1 全局错误处理

```typescript
// 响应拦截器中的错误处理
if (error.response?.status === 401) {
  localStorage.removeItem("token");
  showDialog({
    title: "登录已过期",
    message: "您的登录已过期，请重新登录",
  }).then(() => {
    window.location.href = "/login";
  });
}
```

#### 8.2 用户体验优化

- 加载状态提示
- 空状态页面
- 网络错误提示
- 操作反馈动画

## 主要功能模块

### 首页

- 资讯轮播、医院动态、功能快捷入口
- 最新文章、热门视频、订阅表单弹窗

### 知识库

- 中医知识分类浏览、搜索、收藏
- 文章详情页

### 活动中心

- **活动列表**：支持按类型筛选（进行中、历史、预告）
- **活动详情**：完整的活动信息展示和报名功能
- **活动回顾**：使用 GridCard 组件展示，跳转到活动新闻详情
- **活动新闻**：独立的新闻列表和详情页面

### 个人中心

- 用户信息、积分、学习进度、证书
- 我的收藏、学习记录、积分商城、在线咨询、预约挂号
- 账户设置、退出登录

### 专家/医生/领导

- 专家、医生、领导列表与详情
- 支持按类型分类浏览

### 科室/主科室

- 科室、主科室列表与详情
- 支持按类型分类浏览

### 中药

- 中药列表与详情，支持分类

### 中医案例

- 案例列表与详情，展示典型中医诊疗案例

### 中医文化

- 文化内容列表与详情，弘扬中医传统文化

### 中医知识

- 中医知识专题、详情

### 科教图文

- 科教文章列表与详情，普及医学知识

### 视频宣传

- 视频宣传列表与详情，支持在线播放

## 目录结构

```
src/
├── api/                # API请求封装与各模块API
│   ├── request.ts      # 统一请求封装
│   ├── urls.ts         # API地址管理
│   └── index.ts        # API导出
├── assets/             # 静态资源
├── components/         # 公共组件
│   ├── GlobalHeader.vue    # 全局头部组件
│   ├── GlobalFooter.vue    # 全局底部组件
│   ├── CommonCardList.vue  # 通用卡片列表
│   └── GridCard.vue        # 网格卡片组件
├── router/             # 路由配置
├── style/              # 公共样式
├── views/              # 页面模块
│   ├── Activity/              # 活动列表
│   │   ├── index.vue          # 活动主页
│   │   ├── list.vue           # 活动列表页
│   │   ├── api.ts             # 活动API
│   │   └── style.css          # 活动样式
│   ├── ActivityDetail/        # 活动详情
│   ├── ActivityNews/          # 活动新闻
│   │   ├── list.vue           # 新闻列表（GridCard布局）
│   │   └── api.ts             # 新闻API
│   ├── ActivityNewsDetail/    # 活动新闻详情
│   ├── Cases/                 # 中医案例
│   ├── CasesDetail/           # 案例详情
│   ├── Culture/               # 中医文化
│   ├── CultureDetail/         # 文化详情
│   ├── Dept/                  # 科室
│   ├── DeptDetail/            # 科室详情
│   ├── Doctor/                # 医生
│   ├── DoctorDetail/          # 医生详情
│   ├── EducationMedia/        # 科教图文
│   ├── EducationMediaDetail/  # 科教详情
│   ├── Home/                  # 首页及子组件
│   ├── Knowledge/             # 知识库
│   ├── KnowledgeCategory/     # 知识分类
│   ├── KnowledgeDetail/       # 知识详情
│   ├── Leader/                # 领导
│   ├── LeaderDetail/          # 领导详情
│   ├── MainDept/              # 主科室
│   ├── MainDeptDetail/        # 主科室详情
│   ├── Medicine/              # 中药
│   ├── MedicineDetail/        # 中药详情
│   ├── My.vue                 # 个人中心
│   ├── TcmKnowledge/          # 中医知识
│   ├── TcmKnowledgeDetail/    # 中医知识详情
│   ├── VideoPromotion/        # 视频宣传
│   └── VideoPromotionDetail/  # 视频宣传详情
├── App.vue             # 根组件
├── main.ts             # 入口文件
└── style.css           # 全局样式
```

## 开发指南

### 环境要求

- Node.js >= 16.0.0
- npm >= 7.0.0

### 安装依赖

```bash
npm install
```

### 启动开发环境

```bash
npm run dev
```

### 生产构建

```bash
npm run build
```

### 预览生产版本

```bash
npm run preview
```

## 代码规范

- 使用 ESLint 进行代码检查
- 使用 Prettier 进行代码格式化
- 组件采用 Composition API + `<script setup>` 语法
- 组件和样式文件使用 PascalCase 命名规范

## 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 许可证

[MIT](LICENSE) © 2023-2025 中医智慧

## 联系方式

如有任何问题或建议，请联系项目维护者。

## 代码调用与渲染流程说明

本节详细说明整个项目的调用逻辑、调用过程以及渲染过程，帮助开发者快速理解项目的运行机制。

### 1. 应用启动与全局依赖注入

- 入口文件 `src/main.ts`：
  1. 通过 `createApp(App)` 创建 Vue 应用实例。
  2. 注入全局依赖：
     - 注册路由（`app.use(router)`），实现页面导航与懒加载。
     - 注册 Vant 的图片懒加载插件（`app.use(Lazyload)`）。
     - 挂载全局样式（`import './style.css'`）。
  3. 最终通过 `app.mount('#app')` 将应用挂载到 HTML 根节点。

### 2. 根组件渲染与全局布局

- 根组件 `src/App.vue`：
  1. 顶层 `<router-view />` 动态渲染当前路由对应的页面组件。
  2. 全局底部导航栏 `<van-tabbar>`，实现主页面间的快速切换。
  3. 通过 `onMounted` 生命周期钩子可进行全局初始化（如全局数据预取、全局 API 调用等）。

### 3. 路由注册与页面懒加载

- 路由配置 `src/router/index.ts`：
  1. 使用 `vue-router` 的 Hash 模式（`createWebHashHistory()`）。
  2. 所有页面均采用路由懒加载（`component: () => import('...')`），提升首屏加载速度。
  3. 支持动态路由参数（如 `/knowledge-detail/:id`），便于详情页等场景。
  4. 路由切换时，`<router-view />` 自动渲染对应页面组件。

### 4. API 调用封装与数据流转

- API 封装 `src/api/request.ts`：
  1. 基于 Axios 创建全局请求实例，支持环境切换（开发、生产、测试）。
  2. 请求拦截器：自动注入 token、统一请求头。
  3. 响应拦截器：统一处理业务错误码、弹出提示、自动跳转登录等。
  4. 封装常用请求方法（get/post/put/delete），并支持带重试机制的请求。
  5. 页面和组件通过 `import { get, post, ... } from '@/api/request'` 进行数据请求，数据流向页面/组件的响应式变量。

### 5. 典型页面的数据获取与渲染流程（以活动新闻列表为例）

- 活动新闻列表页 `src/views/ActivityNews/list.vue`：

  1. **页面初始化**：

     ```typescript
     onMounted(() => {
       loadItems(); // 页面挂载时加载数据
     });
     ```

  2. **数据加载流程**：

     ```typescript
     const loadItems = async () => {
       loading.value = true;
       try {
         const res = await getActivityNewsListlWithRetry({
           page: page.value,
           page_size: pageSize.value,
         });
         items.value.push(...res.results);
         finished.value = res.is_last_page;
       } catch (error) {
         console.error("加载失败:", error);
       } finally {
         loading.value = false;
       }
     };
     ```

  3. **数据转换与渲染**：

     ```typescript
     const gridItems = computed(() => {
       return items.value.map((item: ActivityNewsItem) => ({
         id: item.id,
         title: item.name,
         image: item.thumbnail,
         badge: "新闻",
         originalData: item,
       }));
     });
     ```

  4. **组件渲染**：

     ```vue
     <GridCard :items="gridItems" @card-click="handleGridCardClick" />
     ```

  5. **用户交互处理**：
     ```typescript
     const handleGridCardClick = (item: any) => {
       const newsData = item.originalData || item;
       router.push({ name: "ActivityNewsDetail", params: { id: newsData.id } });
     };
     ```

### 6. 组件通信与数据流

- **父子组件通信**：

  ```vue
  <!-- 父组件 -->
  <GridCard :items="gridItems" @card-click="handleClick" />

  <!-- 子组件 -->
  <script setup>
  const emit = defineEmits<{
    cardClick: [item: GridCardItem];
  }>();
  </script>
  ```

- **路由参数传递**：

  ```typescript
  // 跳转时传递参数
  router.push({
    name: "ActivityDetail",
    params: { id: item.id },
    query: { from: "list" },
  });

  // 目标页面接收参数
  const route = useRoute();
  const id = route.params.id;
  ```

### 7. 渲染过程总结

- 应用启动后，Vue 实例根据路由渲染对应页面。
- 页面组件按需加载子组件，子组件独立获取和渲染数据。
- 所有数据请求均通过统一 API 封装层，保证安全、可维护。
- 响应式数据驱动视图自动更新，用户操作通过事件和路由实现页面跳转与交互。

> 通过上述分层与流程，项目实现了高内聚、低耦合、易维护的现代前端架构。

## 其他开发须知与协作建议

### 1. 目录与文件命名规范

- **页面目录**：每个业务模块（如 Activity、Doctor、Knowledge 等）均有独立目录，目录下通常包含 `index.vue`、`api.ts`、`style.css` 等文件，便于模块化开发和维护。
- **组件命名**：公共组件统一放在 `src/components/`，采用 PascalCase 命名（如 `GlobalHeader.vue`）。
- **样式文件**：页面样式建议独立为 `style.css`，全局样式统一在 `src/style/` 或 `src/style.css`。

### 2. 代码风格与开发约定

- **TypeScript**：全项目推荐使用 TypeScript，类型声明文件统一放在 `*.d.ts`。
- **Composition API**：组件开发统一采用 `<script setup lang="ts">` 语法，提升类型推断和开发效率。
- **API 调用**：所有数据请求均通过 `src/api/request.ts` 封装的方法，禁止直接使用 axios，便于统一错误处理和后期维护。
- **响应式数据**：页面和组件数据均使用 `ref` 或 `reactive`，避免直接操作 DOM。
- **事件与通信**：组件间通信优先使用 props、emits，复杂场景可考虑全局状态管理（如 pinia）。

### 3. 常见开发流程

- **新增页面**：在 `src/views/` 下新建目录，配置路由，编写页面组件和对应的 API 文件。
- **新增 API**：在对应模块的 `api.ts` 中添加方法，并在 `src/api/index.ts` 统一导出。
- **新增组件**：在 `src/components/` 下新建组件，按需引入到页面或其他组件中。
- **样式扩展**：如需全局样式，统一在 `src/style/` 下维护，避免污染局部样式作用域。

### 4. 重要约定与注意事项

- **环境变量**：通过 `.env` 文件配置不同环境的 API 地址，开发环境接口支持代理。
- **Token 管理**：登录态通过 localStorage 存储，API 拦截器自动注入，401 自动跳转登录。
- **懒加载与性能优化**：页面和图片均采用懒加载，提升首屏速度。
- **错误处理**：所有 API 错误均有统一 toast 或弹窗提示，便于用户感知和调试。
- **移动端适配**：全站采用 rem/vw 适配，Vant 组件天然支持移动端。

### 5. 推荐开发工具与插件

- **VSCode 插件**：Volar、ESLint、Prettier、Vetur、Vant Snippets
- **调试工具**：Vue Devtools、Vant Playground
- **接口调试**：Postman、Apifox

### 6. 代码提交与协作

- **分支管理**：建议采用 feature/bugfix/release 分支模型，主分支保持稳定。
- **提交规范**：建议使用 `feat: xxx`、`fix: xxx`、`docs: xxx` 等语义化提交信息。
- **代码评审**：所有合并需经 Pull Request 审核，确保代码质量。

### 7. 常见问题与解决方案

- **依赖安装失败**：请确认 Node.js 版本 >= 16，删除 `node_modules` 后重新 `npm install`。
- **样式未生效**：检查是否正确引入 Vant 和全局样式文件。
- **接口跨域**：开发环境已配置代理，生产环境需后端支持 CORS。

---

如有更多问题，建议查阅项目内的相关文档，或直接联系项目维护者。
