/**
 * 系统配置相关类型定义
 */

// 医院基本信息
export interface HospitalInfo {
  id: number;                      // 医院ID
  hospital_name: string;           // 医院名称
  hospital_slogan: string;         // 医院口号
  copyright_text: string;          // 版权文字
  copyright_holder: string;        // 版权持有者
  establishment_year: string;      // 建院年份（字符串格式）
  show_auto_year: boolean;         // 是否自动计算年份范围
}

// 联系方式信息
export interface ContactInfo {
  id: number;                      // 联系信息ID
  phone: string;                   // 联系电话
  email: string;                   // 邮箱地址
  address: string;                 // 详细地址
  business_hours: string;          // 营业时间
}

// SEO配置信息
export interface SeoConfig {
  id: number;                      // SEO配置ID
  site_title: string;              // 网站标题
  site_description: string;        // 网站描述
  site_keywords: string;           // 网站关键词
  site_author: string;             // 网站作者
  site_name: string;               // 网站名称
  app_title: string;               // 移动端应用标题
  nav_title: string;               // 导航标题
}

// 分页响应结构
export interface PaginatedResponse<T> {
  count: number;                   // 总记录数
  page: number;                    // 当前页码
  page_size: number;               // 每页大小
  is_last_page: boolean;           // 是否最后一页
  results: T[];                    // 结果数组
}

// API响应的基础结构（旧格式，保持兼容）
export interface ApiResponse<T> {
  code: number;
  message: string;
  data: T;
}

// 医院信息API响应类型
export type HospitalInfoResponse = PaginatedResponse<HospitalInfo>;

// 联系信息API响应类型
export type ContactInfoResponse = PaginatedResponse<ContactInfo>;

// SEO配置API响应类型
export type SeoConfigResponse = PaginatedResponse<SeoConfig>;

// 系统配置完整结构
export interface SystemConfig {
  hospitalInfo: HospitalInfo | null;
  contactInfo: ContactInfo | null;
  seoConfig: SeoConfig | null;
}

// 缓存键名常量
export const CACHE_KEYS = {
  HOSPITAL_INFO: 'system_hospital_info',
  CONTACT_INFO: 'system_contact_info',
  SEO_CONFIG: 'system_seo_config',
  CACHE_TIME: 'system_config_cache_time'
} as const;

// 缓存有效期：24小时（毫秒）
export const CACHE_DURATION = 24 * 60 * 60 * 1000; 