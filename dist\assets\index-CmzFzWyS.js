import{ax as q,ay as de,az as ue,aA as Ne,r as C,aB as Oe,aC as He,aD as Le,aE as Ee,aF as fe,d as M,O,a9 as ve,Q as z,S as Z,aG as he,aj as be,k as R,aH as me,Y as ge,aI as We,y as $,ak as we,aJ as G,B as A,aK as H,aL as ye,aM as De,b as g,X as p,ar as Y,l as Me,ac as ee,R as D,T as Ze,N as Ue,aN as Fe,U as ie,aa as J,aO as Ve,ag as je,aP as oe,aQ as Ke,aR as Xe,a3 as Ye,Z as Qe,aS as qe,aT as Ge,a7 as Je,aU as pe,a1 as et,a8 as tt,h as nt,aV as at,n as lt,a2 as it,at as re,aW as ot,ap as rt}from"./index-BE8XLQ59.js";import{u as Te}from"./index-B27ddgxZ.js";import{T as st}from"./index-ouF_E0m0.js";import{S as ct,a as dt}from"./index-sbxbYdRt.js";function ut(e,a,o){let l,s=0;const t=e.scrollLeft,r=o===0?1:Math.round(o*1e3/16);let c=t;function h(){de(l)}function m(){c+=(a-t)/r,e.scrollLeft=c,++s<r&&(l=q(m))}return m(),h}function ft(e,a,o,l){let s,t=ue(e);const r=t<a,c=o===0?1:Math.round(o*1e3/16),h=(a-t)/c;function m(){de(s)}function y(){t+=h,(r&&t>a||!r&&t<a)&&(t=a),Ne(e,t),r&&t<a||!r&&t>a?s=q(y):l&&(s=q(l))}return y(),m}function vt(){const e=C([]),a=[];return Oe(()=>{e.value=[]}),[e,l=>(a[l]||(a[l]=s=>{e.value[l]=s}),a[l])]}function xe(e,a){if(!He||!window.IntersectionObserver)return;const o=new IntersectionObserver(t=>{a(t[0].intersectionRatio>0)},{root:document.body}),l=()=>{e.value&&o.observe(e.value)},s=()=>{e.value&&o.unobserve(e.value)};Le(s),Ee(s),fe(l)}const[ht,bt]=Z("sticky"),mt={zIndex:z,position:ve("top"),container:Object,offsetTop:O(0),offsetBottom:O(0)};var gt=M({name:ht,props:mt,emits:["scroll","change"],setup(e,{emit:a,slots:o}){const l=C(),s=he(l),t=be({fixed:!1,width:0,height:0,transform:0}),r=C(!1),c=R(()=>me(e.position==="top"?e.offsetTop:e.offsetBottom)),h=R(()=>{if(r.value)return;const{fixed:f,height:S,width:d}=t;if(f)return{width:`${d}px`,height:`${S}px`}}),m=R(()=>{if(!t.fixed||r.value)return;const f=ge(We(e.zIndex),{width:`${t.width}px`,height:`${t.height}px`,[e.position]:`${c.value}px`});return t.transform&&(f.transform=`translate3d(0, ${t.transform}px, 0)`),f}),y=f=>a("scroll",{scrollTop:f,isFixed:t.fixed}),I=()=>{if(!l.value||G(l))return;const{container:f,position:S}=e,d=H(l),x=ue(window);if(t.width=d.width,t.height=d.height,S==="top")if(f){const u=H(f),k=u.bottom-c.value-t.height;t.fixed=c.value>d.top&&u.bottom>0,t.transform=k<0?k:0}else t.fixed=c.value>d.top;else{const{clientHeight:u}=document.documentElement;if(f){const k=H(f),b=u-k.top-c.value-t.height;t.fixed=u-c.value<d.bottom&&u>k.top,t.transform=b<0?-b:0}else t.fixed=u-c.value<d.bottom}y(x)};return $(()=>t.fixed,f=>a("change",f)),we("scroll",I,{target:s,passive:!0}),xe(l,I),$([ye,De],()=>{!l.value||G(l)||!t.fixed||(r.value=!0,A(()=>{const f=H(l);t.width=f.width,t.height=f.height,r.value=!1}))}),()=>{var f;return g("div",{ref:l,style:h.value},[g("div",{class:bt({fixed:t.fixed&&!r.value}),style:m.value},[(f=o.default)==null?void 0:f.call(o)])])}}});const wt=p(gt),[yt,se]=Z("tabs");var Tt=M({name:yt,props:{count:Y(Number),inited:Boolean,animated:Boolean,duration:Y(z),swipeable:Boolean,lazyRender:Boolean,currentIndex:Y(Number)},emits:["change"],setup(e,{emit:a,slots:o}){const l=C(),s=c=>a("change",c),t=()=>{var c;const h=(c=o.default)==null?void 0:c.call(o);return e.animated||e.swipeable?g(ct,{ref:l,loop:!1,class:se("track"),duration:+e.duration*1e3,touchable:e.swipeable,lazyRender:e.lazyRender,showIndicators:!1,onChange:s},{default:()=>[h]}):h},r=c=>{const h=l.value;h&&h.state.active!==c&&h.swipeTo(c,{immediate:!e.inited})};return $(()=>e.currentIndex,r),Me(()=>{r(e.currentIndex)}),ee({swipeRef:l}),()=>g("div",{class:se("content",{animated:e.animated||e.swipeable})},[t()])}});const[Se,X]=Z("tabs"),xt={type:ve("line"),color:String,border:Boolean,sticky:Boolean,shrink:Boolean,active:O(0),duration:O(.3),animated:Boolean,ellipsis:D,swipeable:Boolean,scrollspy:Boolean,offsetTop:O(0),background:String,lazyRender:D,showHeader:D,lineWidth:z,lineHeight:z,beforeChange:Function,swipeThreshold:O(5),titleActiveColor:String,titleInactiveColor:String},Ce=Symbol(Se);var St=M({name:Se,props:xt,emits:["change","scroll","rendered","clickTab","update:active"],setup(e,{emit:a,slots:o}){let l,s,t,r,c;const h=C(),m=C(),y=C(),I=C(),f=Te(),S=he(h),[d,x]=vt(),{children:u,linkChildren:k}=Ze(Ce),b=be({inited:!1,position:"",lineStyle:{},currentIndex:-1}),L=R(()=>u.length>+e.swipeThreshold||!e.ellipsis||e.shrink),U=R(()=>({borderColor:e.color,background:e.background})),E=(n,i)=>{var v;return(v=n.name)!=null?v:i},F=R(()=>{const n=u[b.currentIndex];if(n)return E(n,b.currentIndex)}),N=R(()=>me(e.offsetTop)),te=R(()=>e.sticky?N.value+l:0),W=n=>{const i=m.value,v=d.value;if(!L.value||!i||!v||!v[b.currentIndex])return;const T=v[b.currentIndex].$el,w=T.offsetLeft-(i.offsetWidth-T.offsetWidth)/2;r&&r(),r=ut(i,w,n?0:+e.duration)},_=()=>{const n=b.inited;A(()=>{const i=d.value;if(!i||!i[b.currentIndex]||e.type!=="line"||G(h.value))return;const v=i[b.currentIndex].$el,{lineWidth:T,lineHeight:w}=e,B=v.offsetLeft+v.offsetWidth/2,P={width:ie(T),backgroundColor:e.color,transform:`translateX(${B}px) translateX(-50%)`};if(n&&(P.transitionDuration=`${e.duration}s`),J(w)){const K=ie(w);P.height=K,P.borderRadius=K}b.lineStyle=P})},Re=n=>{const i=n<b.currentIndex?-1:1;for(;n>=0&&n<u.length;){if(!u[n].disabled)return n;n+=i}},V=(n,i)=>{const v=Re(n);if(!J(v))return;const T=u[v],w=E(T,v),B=b.currentIndex!==null;b.currentIndex!==v&&(b.currentIndex=v,i||W(),_()),w!==e.active&&(a("update:active",w),B&&a("change",w,T.title)),t&&!e.scrollspy&&je(Math.ceil(oe(h.value)-N.value))},j=(n,i)=>{const v=u.findIndex((T,w)=>E(T,w)===n);V(v===-1?0:v,i)},ne=(n=!1)=>{if(e.scrollspy){const i=u[b.currentIndex].$el;if(i&&S.value){const v=oe(i,S.value)-te.value;s=!0,c&&c(),c=ft(S.value,v,n?0:+e.duration,()=>{s=!1})}}},Ie=(n,i,v)=>{const{title:T,disabled:w}=u[i],B=E(u[i],i);w||(Ke(e.beforeChange,{args:[B],done:()=>{V(i),ne()}}),Xe(n)),a("clickTab",{name:B,title:T,event:v,disabled:w})},ke=n=>{t=n.isFixed,a("scroll",n)},Be=n=>{A(()=>{j(n),ne(!0)})},$e=()=>{for(let n=0;n<u.length;n++){const{top:i}=H(u[n].$el);if(i>te.value)return n===0?0:n-1}return u.length-1},_e=()=>{if(e.scrollspy&&!s){const n=$e();V(n)}},Pe=()=>{if(e.type==="line"&&u.length)return g("div",{class:X("line"),style:b.lineStyle},null)},ae=()=>{var n,i,v;const{type:T,border:w,sticky:B}=e,P=[g("div",{ref:B?void 0:y,class:[X("wrap"),{[Ve]:T==="line"&&w}]},[g("div",{ref:m,role:"tablist",class:X("nav",[T,{shrink:e.shrink,complete:L.value}]),style:U.value,"aria-orientation":"horizontal"},[(n=o["nav-left"])==null?void 0:n.call(o),u.map(K=>K.renderTitle(Ie)),Pe(),(i=o["nav-right"])==null?void 0:i.call(o)])]),(v=o["nav-bottom"])==null?void 0:v.call(o)];return B?g("div",{ref:y},[P]):P},le=()=>{_(),A(()=>{var n,i;W(!0),(i=(n=I.value)==null?void 0:n.swipeRef.value)==null||i.resize()})};$(()=>[e.color,e.duration,e.lineWidth,e.lineHeight],_),$(ye,le),$(()=>e.active,n=>{n!==F.value&&j(n)}),$(()=>u.length,()=>{b.inited&&(j(e.active),_(),A(()=>{W(!0)}))});const Ae=()=>{j(e.active,!0),A(()=>{b.inited=!0,y.value&&(l=H(y.value).height),W(!0)})},ze=(n,i)=>a("rendered",n,i);return ee({resize:le,scrollTo:Be}),Ue(_),Fe(_),fe(Ae),xe(h,_),we("scroll",_e,{target:S,passive:!0}),k({id:f,props:e,setLine:_,scrollable:L,onRendered:ze,currentName:F,setTitleRefs:x,scrollIntoView:W}),()=>g("div",{ref:h,class:X([e.type])},[e.showHeader?e.sticky?g(wt,{container:h.value,offsetTop:N.value,onScroll:ke},{default:()=>[ae()]}):ae():null,g(Tt,{ref:I,count:u.length,inited:b.inited,animated:e.animated,duration:e.duration,swipeable:e.swipeable,lazyRender:e.lazyRender,currentIndex:b.currentIndex,onChange:V},{default:()=>{var n;return[(n=o.default)==null?void 0:n.call(o)]}})])}});const[Ct,ce]=Z("tab"),Rt=M({name:Ct,props:{id:String,dot:Boolean,type:String,color:String,title:String,badge:z,shrink:Boolean,isActive:Boolean,disabled:Boolean,controls:String,scrollable:Boolean,activeColor:String,inactiveColor:String,showZeroBadge:D},setup(e,{slots:a}){const o=R(()=>{const s={},{type:t,color:r,disabled:c,isActive:h,activeColor:m,inactiveColor:y}=e;r&&t==="card"&&(s.borderColor=r,c||(h?s.backgroundColor=r:s.color=r));const f=h?m:y;return f&&(s.color=f),s}),l=()=>{const s=g("span",{class:ce("text",{ellipsis:!e.scrollable})},[a.title?a.title():e.title]);return e.dot||J(e.badge)&&e.badge!==""?g(Ye,{dot:e.dot,content:e.badge,showZero:e.showZeroBadge},{default:()=>[s]}):s};return()=>g("div",{id:e.id,role:"tab",class:[ce([e.type,{grow:e.scrollable&&!e.shrink,shrink:e.shrink,active:e.isActive,disabled:e.disabled}])],style:o.value,tabindex:e.disabled?void 0:e.isActive?0:-1,"aria-selected":e.isActive,"aria-disabled":e.disabled||void 0,"aria-controls":e.controls,"data-allow-mismatch":"attribute"},[l()])}}),[It,Q]=Z("tab"),kt=ge({},et,{dot:Boolean,name:z,badge:z,title:String,disabled:Boolean,titleClass:tt,titleStyle:[String,Object],showZeroBadge:D});var Bt=M({name:It,props:kt,setup(e,{slots:a}){const o=Te(),l=C(!1),s=ot(),{parent:t,index:r}=Qe(Ce);if(!t)return;const c=()=>{var d;return(d=e.name)!=null?d:r.value},h=()=>{l.value=!0,t.props.lazyRender&&A(()=>{t.onRendered(c(),e.title)})},m=R(()=>{const d=c()===t.currentName.value;return d&&!l.value&&h(),d}),y=C(""),I=C("");qe(()=>{const{titleClass:d,titleStyle:x}=e;y.value=d?nt(d):"",I.value=x&&typeof x!="string"?at(lt(x)):x});const f=d=>g(Rt,it({key:o,id:`${t.id}-${r.value}`,ref:t.setTitleRefs(r.value),style:I.value,class:y.value,isActive:m.value,controls:o,scrollable:t.scrollable.value,activeColor:t.props.titleActiveColor,inactiveColor:t.props.titleInactiveColor,onClick:x=>d(s.proxy,r.value,x)},re(t.props,["type","color","shrink"]),re(e,["dot","badge","title","disabled","showZeroBadge"])),{title:a.title}),S=C(!m.value);return $(m,d=>{d?S.value=!1:Ge(()=>{S.value=!0})}),$(()=>e.title,()=>{t.setLine(),t.scrollIntoView()}),rt(st,m),ee({id:o,renderTitle:f}),()=>{var d;const x=`${t.id}-${r.value}`,{animated:u,swipeable:k,scrollspy:b,lazyRender:L}=t.props;if(!a.default&&!u)return;const U=b||m.value;if(u||k)return g(dt,{id:o,role:"tabpanel",class:Q("panel-wrapper",{inactive:S.value}),tabindex:m.value?0:-1,"aria-hidden":!m.value,"aria-labelledby":x,"data-allow-mismatch":"attribute"},{default:()=>{var N;return[g("div",{class:Q("panel")},[(N=a.default)==null?void 0:N.call(a)])]}});const F=l.value||b||!L?(d=a.default)==null?void 0:d.call(a):null;return Je(g("div",{id:o,role:"tabpanel",class:Q("panel"),tabindex:U?0:-1,"aria-labelledby":x,"data-allow-mismatch":"attribute"},[F]),[[pe,U]])}}});const zt=p(Bt),Nt=p(St);export{zt as T,Nt as a};
