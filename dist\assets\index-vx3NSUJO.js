import{s as k,v as y,D as C,d as x,u as B,r as o,y as E,l as I,c as _,o as f,b as u,w as g,F as R,e as G,f as L,g as b,_ as F}from"./index-BE8XLQ59.js";import{T as S,a as V}from"./index-CmzFzWyS.js";import"./index-sbxbYdRt.js";import{G as z,a as M}from"./GlobalHeader-lL88u8sR.js";import{C as N}from"./CommonCardList-1yAjUs8b.js";import"./index-B27ddgxZ.js";import"./index-ouF_E0m0.js";import"./index-CAfqjps3.js";function P(){return y(C.CATEGORIES)}function U(r,n=1,t=10){return y(C.DEPT,{cat:r,page:n,page_size:t})}function W(r,n=1,t=10){return k(()=>U(r,n,t))}const q={class:"news-page category-tabs-container"},A={class:"section-container"},H={class:"news-grid"},O=x({__name:"index",setup(r){const n=B(),t=o(!1),i=o(!1),d=o([]),p=o(1),l=o(0),h=o(5),v=o([]),w=async()=>{const e=await P();console.log("获取到领导分类sdfsdfsd:",e);const s=e.categories.map(a=>({id:a.value,name:a.label}));v.value=[...s]};E(l,e=>{p.value=1,d.value=[],i.value=!1,m()});const m=async()=>{var s;if(i.value||t.value)return;t.value=!0;const e=(s=v.value[l.value])==null?void 0:s.id;console.log("当前选中标签ID:",e);try{const a=await W(e,p.value,h.value);d.value.push(...a.results),p.value+=1,i.value=a.is_last_page===!0}catch(a){console.error("加载失败:",a)}finally{t.value=!1}},D=e=>{n.push({name:"DeptDetail",params:{id:e.id}})};return I(()=>{w(),m()}),(e,s)=>{const a=S,T=V;return f(),_("div",q,[u(z,{title:"特色科室"}),u(T,{active:l.value,"onUpdate:active":s[0]||(s[0]=c=>l.value=c),class:"category-tabs",sticky:"","sticky-offset-top":0,swipeable:""},{default:g(()=>[(f(!0),_(R,null,G(v.value,c=>(f(),L(a,{key:c.id,title:c.name},{default:g(()=>[b("div",A,[b("div",H,[u(N,{items:d.value,loading:t.value,finished:i.value,"use-infinite-scroll":!0,onLoadMore:m,onCardClick:D},null,8,["items","loading","finished"])])])]),_:2},1032,["title"]))),128))]),_:1},8,["active"]),u(M)])}}}),ee=F(O,[["__scopeId","data-v-72d155e2"]]);export{ee as default};
