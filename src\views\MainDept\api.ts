import { get, requestWithRetry } from '../../api/request';
import { MAINDEPT_URLS, buildUrl } from '../../api/urls';

/**
 * 获取分类
 * @returns 分类数据
 */
export function getMainDeptCategories() {
  return get<MainDeptCategoriesResponse>(MAINDEPT_URLS.CATEGORIES);
}

/**
 * 根据分类获取列表
 * @param cat 分类ID
 * @param page 页码
 * @param pageSize 每页数量
 * @returns 列表数据
 */
export function getMainDeptByCategory(cat: string, page: number = 1, pageSize: number = 10) {
  return get<MainDeptListResponse>(MAINDEPT_URLS.MAINDEPT, {
    cat,
    page,
    page_size: pageSize
  });
}

/**
 * 带重试功能的获取分类
 * 在网络不稳定情况下使用
 * @returns 分类数据
 */

export function getMainDeptCategoriesWithRetry(cat: string, page: number = 1, pageSize: number = 10) {
  return requestWithRetry<MainDeptListResponse>(() => getMainDeptByCategory(
    cat,
    page,
    pageSize
  ));
}

// 分类接口
export interface MainDeptCategory {
  value: string;
  label: string;
}

// 分类响应接口
export interface MainDeptCategoriesResponse {
  categories: MainDeptCategory[];
}

// 信息接口
export interface MainDeptItem {
  id: number;
  name: string;
  title: string;
  job: string;
  cat_display: string;
  desc: string;
  thumbnail: string;
  category: string;
  tags?: string[];
}

// 列表响应接口
export interface MainDeptListResponse {
  results: MainDeptItem[];
  page: number;
  page_size: number;
  total: number;
  is_last_page: boolean;
}