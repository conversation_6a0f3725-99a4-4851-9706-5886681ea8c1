/**
 * 微信分享组合函数
 * 在Vue组件中使用微信分享功能
 */

import { onMounted, onUnmounted } from 'vue';
import { useRoute } from 'vue-router';
import { initPageShare, updatePageShare, type ShareConfig } from '../utils/wechatShare';
import { updateMetaProperty, updateMetaTag } from '../utils/seoManager';

/**
 * 微信分享组合函数
 * @param shareConfig 分享配置
 * @returns 分享相关方法
 */
export function useWechatShare(shareConfig?: Partial<ShareConfig>) {
  const route = useRoute();

  /**
   * 设置页面分享信息
   * @param config 分享配置
   */
  const setShareConfig = (config: Partial<ShareConfig>) => {
    // 更新页面meta标签
    if (config.title) {
      document.title = config.title;
      updateMetaProperty('og:title', config.title);
      updateMetaTag('twitter:title', config.title);
    }
    
    if (config.desc) {
      updateMetaTag('description', config.desc);
      updateMetaProperty('og:description', config.desc);
      updateMetaTag('twitter:description', config.desc);
    }
    
    if (config.imgUrl) {
      updateMetaProperty('og:image', config.imgUrl);
      updateMetaTag('twitter:image', config.imgUrl);
    }
    
    // 更新当前页面URL
    const currentUrl = window.location.origin + route.fullPath;
    updateMetaProperty('og:url', currentUrl);
    
    // 配置微信分享
    updatePageShare(config.title, config.desc, config.imgUrl);
  };

  /**
   * 初始化分享配置
   */
  const initShare = () => {
    if (shareConfig) {
      setShareConfig(shareConfig);
    } else {
      // 使用默认配置
      initPageShare();
    }
  };

  /**
   * 获取详情页分享配置
   * @param title 详情标题
   * @param description 详情描述
   * @param imageUrl 详情图片
   * @returns ShareConfig
   */
  const getDetailShareConfig = (
    title: string, 
    description?: string, 
    imageUrl?: string
  ): Partial<ShareConfig> => {
    return {
      title: `${title} - 中医智慧`,
      desc: description || '专业的中医健康平台，传承千年中医文化，守护您的健康。',
      imgUrl: imageUrl || 'https://gujiao.sxaliyun.cn/images/og-image.png',
      link: window.location.href.split('#')[0] // 移除hash部分
    };
  };

  /**
   * 设置详情页分享
   * @param title 详情标题
   * @param description 详情描述
   * @param imageUrl 详情图片
   */
  const setDetailShare = (
    title: string, 
    description?: string, 
    imageUrl?: string
  ) => {
    const config = getDetailShareConfig(title, description, imageUrl);
    setShareConfig(config);
  };

  // 组件挂载时初始化分享
  onMounted(() => {
    // 延迟初始化，确保页面内容已加载
    setTimeout(() => {
      initShare();
    }, 500);
  });

  return {
    setShareConfig,
    setDetailShare,
    getDetailShareConfig,
    initShare
  };
}

/**
 * 页面级别的分享配置
 * 根据路由自动配置分享信息
 */
export function usePageShare() {
  const route = useRoute();
  
  const getPageConfig = (): Partial<ShareConfig> => {
    const routeName = route.name as string;
    
    // 根据路由名称配置不同的分享信息
    const pageConfigs: Record<string, Partial<ShareConfig>> = {
      'Home': {
        title: '中医智慧 - 传承千年中医文化，守护您的健康',
        desc: '专业的中医健康平台，提供中医知识、养生指导、专家咨询等服务。'
      },
      'Doctor': {
        title: '名医专家 - 中医智慧',
        desc: '汇聚中医名家，传承医道精髓，为您的健康保驾护航。'
      },
      'Medicine': {
        title: '中药百科 - 中医智慧',
        desc: '中药知识大全，了解中药功效，科学用药，健康养生。'
      },
      'Culture': {
        title: '中医文化 - 中医智慧',
        desc: '传承千年中医文化，弘扬中华医学智慧，感受中医魅力。'
      },
      'Cases': {
        title: '中医案例 - 中医智慧',
        desc: '真实中医诊疗案例，学习中医诊断思路，提升中医认知。'
      },
      'Video': {
        title: '视频宣传 - 中医智慧',
        desc: '中医视频资料，直观了解中医文化，感受中医魅力。'
      },
      'Activity': {
        title: '活动组织 - 中医智慧',
        desc: '中医健康活动，专家讲座，养生课程，与您共享健康生活。'
      },
      'TcmKnowledge': {
        title: '中医知识 - 中医智慧',
        desc: '中医理论知识，养生保健方法，让中医智慧融入生活。'
      }
    };
    
    return pageConfigs[routeName] || pageConfigs['Home'];
  };
  
  return useWechatShare(getPageConfig());
}
