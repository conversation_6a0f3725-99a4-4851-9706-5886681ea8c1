import { get, post, requestWithRetry } from '../../api/request';
import { LEADER_URLS, buildUrl } from '../../api/urls';

/**
 * 获取领导详情
 * @param id 领导ID
 * @returns 领导详情数据
 */
export function getLeaderDetail(id: string | number) {
  return get<LeaderDetailResponse>(buildUrl(LEADER_URLS.LEADER, id));
}

/**
 * 带重试功能的获取领导详情
 * 在网络不稳定情况下使用
 * @param id 领导ID
 * @returns 领导详情数据
 */
export function getLeaderDetailWithRetry(id: string | number) {
  return requestWithRetry<LeaderDetailResponse>(() => getLeaderDetail(id));
}

// 工作经历接口
export interface Experience {
  period: string;      // 时间段，如 "2015-2020"
  position: string;    // 职位
  organization: string; // 组织/单位
}

// API返回的领导详情响应接口（根据实际API数据结构）
export interface LeaderDetailResponse {
  leader: {
    id: number;           // 领导ID
    name: string;         // 姓名
    job: string;          // 职位
    desc: string;         // 详细描述/简介
    thumbnail: string;    // 缩略图/头像
    cat_display: string;  // 分类显示名称
    tags: string[];       // 标签数组
    content: string;      // 富文本内容
    create_time: string;  // 创建时间
    viewtimes_display: string; // 浏览次数显示
  }
}

// 领导详情接口（组件内部使用）
export interface LeaderDetail {
  id: number;           // 领导ID
  name: string;         // 姓名
  job: string;          // 职位
  desc: string;         // 详细描述/简介
  thumbnail: string;    // 缩略图/头像
  cat_display: string;  // 分类显示名称
  tags?: string[];      // 标签数组
  content?: string;     // 富文本内容
  create_time?: string; // 创建时间
  viewtimes_display?: string; // 浏览次数显示
  // 以下字段为前端处理后的数据
  specialties?: string[]; // 专业特长（从desc中提取或tags中获取）
  experience?: Experience[]; // 工作经历（从desc中提取）
  publications?: string[]; // 学术成果/出版物（从desc中提取）
  achievements?: string[]; // 成就和荣誉（从desc中提取）
  research_directions?: string[]; // 研究方向（从desc中提取）
}