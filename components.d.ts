/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    ActivityCommonCardList: typeof import('./src/components/ActivityCommonCardList.vue')['default']
    ActivityDetailCard: typeof import('./src/components/ActivityDetailCard.vue')['default']
    ActivityHistoryCardList: typeof import('./src/components/ActivityHistoryCardList.vue')['default']
    'ActivityHistoryCardList.example': typeof import('./src/components/ActivityHistoryCardList.example.vue')['default']
    ArticleGridSection: typeof import('./src/components/ArticleGridSection.vue')['default']
    CommonCardList: typeof import('./src/components/CommonCardList.vue')['default']
    CommonVideoCardList: typeof import('./src/components/CommonVideoCardList.vue')['default']
    GlobalFooter: typeof import('./src/components/GlobalFooter.vue')['default']
    GlobalHeader: typeof import('./src/components/GlobalHeader.vue')['default']
    GridCard: typeof import('./src/components/GridCard.vue')['default']
    'GridCard.example': typeof import('./src/components/GridCard.example.vue')['default']
    LeaderDetailCard: typeof import('./src/components/LeaderDetailCard.vue')['default']
    NewsCard: typeof import('./src/components/NewsCard.vue')['default']
    'NewsCard.example': typeof import('./src/components/NewsCard.example.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SectionHeader: typeof import('./src/components/SectionHeader.vue')['default']
    VanBackTop: typeof import('vant/es')['BackTop']
    VanButton: typeof import('vant/es')['Button']
    VanCheckbox: typeof import('vant/es')['Checkbox']
    VanDivider: typeof import('vant/es')['Divider']
    VanEmpty: typeof import('vant/es')['Empty']
    VanField: typeof import('vant/es')['Field']
    VanForm: typeof import('vant/es')['Form']
    VanGrid: typeof import('vant/es')['Grid']
    VanGridItem: typeof import('vant/es')['GridItem']
    VanIcon: typeof import('vant/es')['Icon']
    VanList: typeof import('vant/es')['List']
    VanLoading: typeof import('vant/es')['Loading']
    VanPopup: typeof import('vant/es')['Popup']
    VanSwipe: typeof import('vant/es')['Swipe']
    VanSwipeItem: typeof import('vant/es')['SwipeItem']
    VanTab: typeof import('vant/es')['Tab']
    VanTabbar: typeof import('vant/es')['Tabbar']
    VanTabbarItem: typeof import('vant/es')['TabbarItem']
    VanTabs: typeof import('vant/es')['Tabs']
    VanTag: typeof import('vant/es')['Tag']
  }
}
