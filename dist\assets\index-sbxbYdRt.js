import{d as J,R as A,O as _,Q as V,S as K,r as j,aj as Q,aX as re,T as ve,k as d,ac as U,y as E,aL as fe,aM as de,aY as he,l as Z,N as ge,aN as we,aD as me,aE as ye,ak as be,b as $,aJ as F,B as q,aT as M,an as R,ad as Te,X as G,Z as xe}from"./index-BE8XLQ59.js";const[ee,z]=K("swipe"),Se={loop:A,width:V,height:V,vertical:Boolean,autoplay:_(0),duration:_(500),touchable:A,lazyRender:Boolean,initialSwipe:_(0),indicatorColor:String,showIndicators:A,stopPropagation:A},te=Symbol(ee);var pe=J({name:ee,props:Se,emits:["change","dragStart","dragEnd"],setup(a,{emit:b,slots:g}){const u=j(),h=j(),t=Q({rect:null,width:0,height:0,offset:0,active:0,swiping:!1});let T=!1;const r=re(),{children:w,linkChildren:s}=ve(te),i=d(()=>w.length),o=d(()=>t[a.vertical?"height":"width"]),v=d(()=>a.vertical?r.deltaY.value:r.deltaX.value),y=d(()=>t.rect?(a.vertical?t.rect.height:t.rect.width)-o.value*i.value:0),I=d(()=>o.value?Math.ceil(Math.abs(y.value)/o.value):i.value),O=d(()=>i.value*o.value),x=d(()=>(t.active+i.value)%i.value),B=d(()=>{const e=a.vertical?"vertical":"horizontal";return r.direction.value===e}),ae=d(()=>{const e={transitionDuration:`${t.swiping?0:a.duration}ms`,transform:`translate${a.vertical?"Y":"X"}(${+t.offset.toFixed(2)}px)`};if(o.value){const l=a.vertical?"height":"width",n=a.vertical?"width":"height";e[l]=`${O.value}px`,e[n]=a[n]?`${a[n]}px`:""}return e}),ie=e=>{const{active:l}=t;return e?a.loop?R(l+e,-1,i.value):R(l+e,0,I.value):l},X=(e,l=0)=>{let n=e*o.value;a.loop||(n=Math.min(n,-y.value));let f=l-n;return a.loop||(f=R(f,y.value,0)),f},m=({pace:e=0,offset:l=0,emitChange:n})=>{if(i.value<=1)return;const{active:f}=t,c=ie(e),C=X(c,l);if(a.loop){if(w[0]&&C!==y.value){const D=C<y.value;w[0].setOffset(D?O.value:0)}if(w[i.value-1]&&C!==0){const D=C>0;w[i.value-1].setOffset(D?-O.value:0)}}t.active=c,t.offset=C,n&&c!==f&&b("change",x.value)},k=()=>{t.swiping=!0,t.active<=-1?m({pace:i.value}):t.active>=i.value&&m({pace:-i.value})},ne=()=>{k(),r.reset(),M(()=>{t.swiping=!1,m({pace:-1,emitChange:!0})})},Y=()=>{k(),r.reset(),M(()=>{t.swiping=!1,m({pace:1,emitChange:!0})})};let N;const p=()=>clearTimeout(N),P=()=>{p(),+a.autoplay>0&&i.value>1&&(N=setTimeout(()=>{Y(),P()},+a.autoplay))},S=(e=+a.initialSwipe)=>{if(!u.value)return;const l=()=>{var n,f;if(!F(u)){const c={width:u.value.offsetWidth,height:u.value.offsetHeight};t.rect=c,t.width=+((n=a.width)!=null?n:c.width),t.height=+((f=a.height)!=null?f:c.height)}i.value&&(e=Math.min(i.value-1,e),e===-1&&(e=i.value-1)),t.active=e,t.swiping=!0,t.offset=X(e),w.forEach(c=>{c.setOffset(0)}),P()};F(u)?q().then(l):l()},H=()=>S(t.active);let L;const le=e=>{!a.touchable||e.touches.length>1||(r.start(e),T=!1,L=Date.now(),p(),k())},oe=e=>{a.touchable&&t.swiping&&(r.move(e),B.value&&(!a.loop&&(t.active===0&&v.value>0||t.active===i.value-1&&v.value<0)||(Te(e,a.stopPropagation),m({offset:v.value}),T||(b("dragStart",{index:x.value}),T=!0))))},W=()=>{if(!a.touchable||!t.swiping)return;const e=Date.now()-L,l=v.value/e;if((Math.abs(l)>.25||Math.abs(v.value)>o.value/2)&&B.value){const f=a.vertical?r.offsetY.value:r.offsetX.value;let c=0;a.loop?c=f>0?v.value>0?-1:1:0:c=-Math[v.value>0?"ceil":"floor"](v.value/o.value),m({pace:c,emitChange:!0})}else v.value&&m({pace:0});T=!1,t.swiping=!1,b("dragEnd",{index:x.value}),P()},se=(e,l={})=>{k(),r.reset(),M(()=>{let n;a.loop&&e===i.value?n=t.active===0?0:e:n=e%i.value,l.immediate?M(()=>{t.swiping=!1}):t.swiping=!1,m({pace:n-t.active,emitChange:!0})})},ce=(e,l)=>{const n=l===x.value,f=n?{backgroundColor:a.indicatorColor}:void 0;return $("i",{style:f,class:z("indicator",{active:n})},null)},ue=()=>{if(g.indicator)return g.indicator({active:x.value,total:i.value});if(a.showIndicators&&i.value>1)return $("div",{class:z("indicators",{vertical:a.vertical})},[Array(i.value).fill("").map(ce)])};return U({prev:ne,next:Y,state:t,resize:H,swipeTo:se}),s({size:o,props:a,count:i,activeIndicator:x}),E(()=>a.initialSwipe,e=>S(+e)),E(i,()=>S(t.active)),E(()=>a.autoplay,P),E([fe,de,()=>a.width,()=>a.height],H),E(he(),e=>{e==="visible"?P():p()}),Z(S),ge(()=>S(t.active)),we(()=>S(t.active)),me(p),ye(p),be("touchmove",oe,{target:h}),()=>{var e;return $("div",{ref:u,class:z()},[$("div",{ref:h,style:ae.value,class:z("track",{vertical:a.vertical}),onTouchstartPassive:le,onTouchend:W,onTouchcancel:W},[(e=g.default)==null?void 0:e.call(g)]),ue()])}}});const ke=G(pe),[Pe,Ce]=K("swipe-item");var Ee=J({name:Pe,setup(a,{slots:b}){let g;const u=Q({offset:0,inited:!1,mounted:!1}),{parent:h,index:t}=xe(te);if(!h)return;const T=d(()=>{const s={},{vertical:i}=h.props;return h.size.value&&(s[i?"height":"width"]=`${h.size.value}px`),u.offset&&(s.transform=`translate${i?"Y":"X"}(${u.offset}px)`),s}),r=d(()=>{const{loop:s,lazyRender:i}=h.props;if(!i||g)return!0;if(!u.mounted)return!1;const o=h.activeIndicator.value,v=h.count.value-1,y=o===0&&s?v:o-1,I=o===v&&s?0:o+1;return g=t.value===o||t.value===y||t.value===I,g}),w=s=>{u.offset=s};return Z(()=>{q(()=>{u.mounted=!0})}),U({setOffset:w}),()=>{var s;return $("div",{class:Ce(),style:T.value},[r.value?(s=b.default)==null?void 0:s.call(b):null])}}});const Ae=G(Ee);export{ke as S,Ae as a};
