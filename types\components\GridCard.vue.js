import { defineProps, defineEmits } from 'vue';
const props = withDefaults(defineProps(), {
    clickable: true,
    loading: false
});
const emit = defineEmits();
// 处理卡片点击
const handleCardClick = (item) => {
    if (props.clickable) {
        emit('cardClick', item);
    }
};
// 处理图片加载错误
const handleImageError = (event, item) => {
    const img = event.target;
    img.src = '/default-image.png';
    emit('imageError', item);
};
debugger; /* PartiallyEnd: #3632/scriptSetup.vue */
const __VLS_withDefaultsArg = (function (t) { return t; })({
    clickable: true,
    loading: false
});
const __VLS_ctx = {};
let __VLS_components;
let __VLS_directives;
/** @type {__VLS_StyleScopedClasses['grid-card-item']} */ ;
/** @type {__VLS_StyleScopedClasses['grid-card-item']} */ ;
/** @type {__VLS_StyleScopedClasses['grid-card-item']} */ ;
/** @type {__VLS_StyleScopedClasses['card-image']} */ ;
/** @type {__VLS_StyleScopedClasses['grid-card-container']} */ ;
/** @type {__VLS_StyleScopedClasses['card-image-section']} */ ;
/** @type {__VLS_StyleScopedClasses['card-content-section']} */ ;
/** @type {__VLS_StyleScopedClasses['card-title']} */ ;
/** @type {__VLS_StyleScopedClasses['card-badge']} */ ;
/** @type {__VLS_StyleScopedClasses['grid-card-container']} */ ;
/** @type {__VLS_StyleScopedClasses['card-image-section']} */ ;
/** @type {__VLS_StyleScopedClasses['card-content-section']} */ ;
/** @type {__VLS_StyleScopedClasses['card-title']} */ ;
/** @type {__VLS_StyleScopedClasses['grid-card-item']} */ ;
/** @type {__VLS_StyleScopedClasses['grid-card-item']} */ ;
// CSS variable injection 
// CSS variable injection end 
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ class: "grid-card-container" },
});
for (const [item] of __VLS_getVForSourceType((__VLS_ctx.items))) {
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
        ...{ onClick: (...[$event]) => {
                __VLS_ctx.handleCardClick(item);
            } },
        key: (item.id),
        ...{ class: "grid-card-item" },
    });
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
        ...{ class: "card-image-section" },
    });
    __VLS_asFunctionalElement(__VLS_intrinsicElements.img)({
        ...{ onError: (...[$event]) => {
                __VLS_ctx.handleImageError($event, item);
            } },
        src: (item.image),
        alt: (item.title),
        ...{ class: "card-image" },
    });
    if (item.badge) {
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "card-badge" },
        });
        (item.badge);
    }
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
        ...{ class: "card-content-section" },
    });
    __VLS_asFunctionalElement(__VLS_intrinsicElements.h3, __VLS_intrinsicElements.h3)({
        ...{ class: "card-title" },
    });
    (item.title);
}
/** @type {__VLS_StyleScopedClasses['grid-card-container']} */ ;
/** @type {__VLS_StyleScopedClasses['grid-card-item']} */ ;
/** @type {__VLS_StyleScopedClasses['card-image-section']} */ ;
/** @type {__VLS_StyleScopedClasses['card-image']} */ ;
/** @type {__VLS_StyleScopedClasses['card-badge']} */ ;
/** @type {__VLS_StyleScopedClasses['card-content-section']} */ ;
/** @type {__VLS_StyleScopedClasses['card-title']} */ ;
var __VLS_dollars;
const __VLS_self = (await import('vue')).defineComponent({
    setup() {
        return {
            handleCardClick: handleCardClick,
            handleImageError: handleImageError,
        };
    },
    __typeEmits: {},
    __typeProps: {},
    props: {},
});
export default (await import('vue')).defineComponent({
    setup() {
        return {};
    },
    __typeEmits: {},
    __typeProps: {},
    props: {},
});
; /* PartiallyEnd: #4569/main.vue */
