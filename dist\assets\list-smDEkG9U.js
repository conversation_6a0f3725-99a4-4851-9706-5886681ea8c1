import{d as U,r as g,l as N,B as G,y as V,c as r,o as n,f as I,b as u,L as P,w as $,j as M,F as S,e as H,g as e,t as o,i as d,I as j,_ as F,u as q,k as R}from"./index-BE8XLQ59.js";import{G as W,a as J}from"./GlobalHeader-lL88u8sR.js";import{L as K}from"./index-ouF_E0m0.js";import{T as O}from"./index-CAfqjps3.js";import{E as Q}from"./index-B27ddgxZ.js";import{f as z,b as B,c as X,d as Y,e as Z,h as tt}from"./dateTime-CGItJ1-U.js";import{s as E}from"./function-call-BUl5915X.js";const at={key:0,class:"loading-container"},et=["onClick"],st={class:"activity-header"},nt={class:"activity-number"},it={class:"number-circle"},ct={class:"activity-time"},ot={class:"time-content"},rt={class:"activity-content"},lt={class:"activity-image-container"},dt=["src","alt"],ut={class:"activity-text"},pt={class:"activity-title"},_t={class:"activity-description"},mt={key:0,class:"activity-meta"},vt={key:0,class:"meta-item"},ht={key:1,class:"meta-item"},yt={key:2,class:"meta-item"},ft={key:3},gt=["onClick"],kt={class:"activity-header"},bt={class:"activity-number"},wt={class:"number-circle"},Ct={class:"activity-time"},Tt={class:"time-content"},Lt={class:"activity-content"},Dt={class:"activity-image-container"},At=["src","alt"],It={class:"activity-text"},$t={class:"activity-title"},Bt={class:"activity-description"},Mt={key:0,class:"activity-meta"},xt={key:0,class:"meta-item"},St={key:1,class:"meta-item"},Ht={key:2,class:"meta-item"},Rt=U({__name:"ActivityHistoryCardList",props:{items:{},loading:{type:Boolean},finished:{type:Boolean,default:!1},emptyText:{default:"暂无活动历史"},useInfiniteScroll:{type:Boolean,default:!0},activityType:{default:"past"}},emits:["load-more","card-click"],setup(x,{emit:T}){const p=g(null),_=()=>p.value;N(()=>{G(()=>{console.log("ActivityHistoryCardList 滚动容器:",p.value)})});const h=x,y=T,m=g(!1);V(()=>h.loading,a=>{m.value=a},{immediate:!0});const l=()=>{console.log("ActivityHistoryCardList 触发加载更多"),y("load-more")},v=a=>a.startdate?a.enddate?z(a.startdate,a.enddate):B(a.startdate):a.start_time?a.end_time?z(a.start_time,a.end_time):B(a.start_time):a.activity_date?B(a.activity_date):a.create_time?B(a.create_time):"时间待定",f=a=>{const i=new Date;if(a.status)return a.status;switch(h.activityType){case"upcoming":return"活动预告";case"current":return"本月活动";case"past":return"历史活动";default:if(a.startdate&&a.enddate){const w=new Date(a.startdate),C=new Date(a.enddate);return i<w?"未开始":i>C?"已结束":"进行中"}return"已结束"}},b=a=>{switch(f(a)){case"进行中":return"success";case"未开始":return"primary";case"已结束":return"default";case"已取消":return"danger";case"活动预告":return"primary";case"本月活动":return"success";case"历史活动":return"default";default:return"default"}},L=a=>!!(a.location||a.address||a.participants||a.current_participants||a.cat_display||a.category);return(a,i)=>{const w=P,C=Q,D=O,s=j,c=K;return n(),r("div",{class:"activity-history-card-list",ref_key:"scrollContainerRef",ref:p},[a.loading&&!a.items.length?(n(),r("div",at,[u(w,{size:"24px"},{default:$(()=>i[1]||(i[1]=[M("加载中...")])),_:1,__:[1]})])):a.items.length?a.useInfiniteScroll?(n(),I(c,{key:2,loading:m.value,"onUpdate:loading":i[0]||(i[0]=t=>m.value=t),finished:a.finished,"finished-text":"没有更多了",onLoad:l,"immediate-check":!1,"scroll-container":_},{default:$(()=>[(n(!0),r(S,null,H(a.items,(t,k)=>(n(),r("div",{key:t.id,class:"history-card animate__animated animate__fadeInUp",onClick:A=>a.$emit("card-click",t)},[e("div",st,[e("div",nt,[e("span",it,o(k+1),1)]),e("div",ct,[i[2]||(i[2]=e("div",{class:"time-label"},"时间：",-1)),e("div",ot,o(v(t)),1)])]),e("div",rt,[e("div",lt,[e("img",{src:t.thumbnail||t.image,alt:t.name||t.title,class:"activity-image"},null,8,dt),f(t)?(n(),I(D,{key:0,type:b(t),size:"medium",class:"status-tag",round:""},{default:$(()=>[M(o(f(t)),1)]),_:2},1032,["type"])):d("",!0)]),e("div",ut,[e("h3",pt,o(t.name||t.title),1),e("p",_t,o(t.desc||t.description||t.summary),1),L(t)?(n(),r("div",mt,[t.location||t.address?(n(),r("div",vt,[u(s,{name:"location-o"}),e("span",null,o(t.location||t.address),1)])):d("",!0),t.participants||t.current_participants?(n(),r("div",ht,[u(s,{name:"friends-o"}),e("span",null,o(t.participants||t.current_participants)+"人参与",1)])):d("",!0),t.cat_display||t.category?(n(),r("div",yt,[u(s,{name:"label-o"}),e("span",null,o(t.cat_display||t.category),1)])):d("",!0)])):d("",!0)])])],8,et))),128))]),_:1},8,["loading","finished"])):(n(),r("div",ft,[(n(!0),r(S,null,H(a.items,(t,k)=>(n(),r("div",{key:t.id,class:"history-card animate__animated animate__fadeInUp",onClick:A=>a.$emit("card-click",t)},[e("div",kt,[e("div",bt,[e("span",wt,o(k+1),1)]),e("div",Ct,[i[3]||(i[3]=e("div",{class:"time-label"},"时间：",-1)),e("div",Tt,o(v(t)),1)])]),e("div",Lt,[e("div",Dt,[e("img",{src:t.thumbnail||t.image,alt:t.name||t.title,class:"activity-image"},null,8,At),f(t)?(n(),I(D,{key:0,type:b(t),size:"medium",class:"status-tag",round:""},{default:$(()=>[M(o(f(t)),1)]),_:2},1032,["type"])):d("",!0)]),e("div",It,[e("h3",$t,o(t.name||t.title),1),e("p",Bt,o(t.desc||t.description||t.summary),1),L(t)?(n(),r("div",Mt,[t.location||t.address?(n(),r("div",xt,[u(s,{name:"location-o"}),e("span",null,o(t.location||t.address),1)])):d("",!0),t.participants||t.current_participants?(n(),r("div",St,[u(s,{name:"friends-o"}),e("span",null,o(t.participants||t.current_participants)+"人参与",1)])):d("",!0),t.cat_display||t.category?(n(),r("div",Ht,[u(s,{name:"label-o"}),e("span",null,o(t.cat_display||t.category),1)])):d("",!0)])):d("",!0)])])],8,gt))),128))])):(n(),I(C,{key:1,description:a.emptyText},null,8,["description"]))],512)}}}),zt=F(Rt,[["__scopeId","data-v-be0a55db"]]),Et={class:"activity-list-page"},Ut={class:"section-container"},Nt={class:"activity-grid"},Vt=U({__name:"list",setup(x){const T=q(),p=g(!1),_=g(!1),h=g([]),y=g(1),m=g(10),l=R(()=>T.currentRoute.value.query.type),v=R(()=>{switch(l.value){case"past":return"历史活动";case"current":return"本月活动";case"upcoming":return"活动预告";default:return"活动列表"}}),f=()=>{switch(l.value){case"past":return"暂无历史活动";case"current":return"暂无本月活动";case"upcoming":return"暂无活动预告";default:return"暂无活动数据"}},b=()=>[{id:1,name:"中医养生健康讲座：冬季进补指南",cat:"health_lecture",cat_name:"健康讲座",desc:"本次讲座由资深中医专家为大家详细介绍冬季进补的原理、方法和注意事项，帮助大家科学养生，增强体质，预防疾病。",thumbnail:"https://picsum.photos/400/300?random=1",isfree:1,startdate:"2024-01-15",enddate:"2024-01-15",views:1256,location:"医院三楼学术报告厅",tags:["中医","养生","冬季进补"]},{id:2,name:"针灸技术培训班：传统医学实践",cat:"professional_training",cat_name:"专业培训",desc:"面向医务人员的针灸技术专业培训，由国家级名老中医亲自授课，理论与实践相结合，提升临床技能。",thumbnail:"https://picsum.photos/400/300?random=2",isfree:0,startdate:"2024-01-20",enddate:"2024-01-22",views:856,location:"中医技能培训中心",tags:["针灸","培训","中医技术"]},{id:3,name:"中药材识别与应用科普活动",cat:"science_education",cat_name:"科普教育",desc:"带领市民了解常见中药材的识别方法、功效作用和日常应用，增进对中医药文化的理解和认识。",thumbnail:"https://picsum.photos/400/300?random=3",isfree:1,startdate:"2024-02-10",enddate:"2024-02-10",views:2341,location:"中药标本馆",tags:["中药材","科普","传统文化"]},{id:4,name:"太极拳健身班：强身健体养心神",cat:"fitness_activity",cat_name:"健身活动",desc:"专业太极拳教练指导，适合各年龄段人群参与，通过太极拳练习达到强身健体、修身养性的目的。",thumbnail:"https://picsum.photos/400/300?random=4",isfree:1,startdate:"2024-02-25",enddate:"2024-03-25",views:1876,location:"医院健身广场",tags:["太极拳","健身","养生"]},{id:5,name:"中医诊疗技术研讨会",cat:"academic_conference",cat_name:"学术会议",desc:"邀请国内知名中医专家分享最新诊疗技术和临床经验，促进中医药学术交流与发展。",thumbnail:"https://picsum.photos/400/300?random=5",isfree:0,startdate:"2024-03-08",enddate:"2024-03-09",views:945,location:"国际会议中心",tags:["学术","研讨会","中医诊疗"]},{id:6,name:"春季养肝护肝健康讲座",cat:"health_lecture",cat_name:"健康讲座",desc:"春季是养肝的最佳时节，专家将详细讲解春季养肝的重要性、方法和注意事项，帮助大家健康度过春季。",thumbnail:"https://picsum.photos/400/300?random=6",isfree:1,startdate:"2024-03-15",enddate:"2024-03-15",views:1567,location:"医院门诊大厅",tags:["春季养生","养肝","健康讲座"]},{id:7,name:"中医药文化展览活动",cat:"cultural_activity",cat_name:"文化活动",desc:"通过图片、实物、互动体验等形式，全面展示中医药文化的深厚底蕴和现代发展成就。",thumbnail:"https://picsum.photos/400/300?random=7",isfree:1,startdate:"2024-03-20",enddate:"2024-03-30",views:3245,location:"文化展览馆",tags:["中医药文化","展览","传统文化"]},{id:8,name:"儿童推拿技术培训班",cat:"professional_training",cat_name:"专业培训",desc:"专门针对儿童常见疾病的推拿技术培训，适合家长和医护人员参加，学习安全有效的儿童保健方法。",thumbnail:"https://picsum.photos/400/300?random=8",isfree:0,startdate:"2024-04-05",enddate:"2024-04-07",views:1234,location:"儿科培训室",tags:["儿童推拿","培训","儿童保健"]}],L=()=>[{id:101,name:"冬季中医养生保健讲座",cat:"health_lecture",cat_name:"健康讲座",desc:"专家讲解冬季养生要点，包括起居调养、饮食调理、运动保健等，帮助大家健康过冬。",thumbnail:"https://picsum.photos/400/300?random=101",isfree:1,startdate:"2024-12-20",enddate:"2024-12-20",views:456,location:"医院学术报告厅",tags:["冬季养生","保健","中医"]},{id:102,name:"中医体质辨识与调理",cat:"health_consultation",cat_name:"健康咨询",desc:"通过专业的中医体质辨识，为每位参与者制定个性化的调理方案，提升身体健康水平。",thumbnail:"https://picsum.photos/400/300?random=102",isfree:1,startdate:"2024-12-22",enddate:"2024-12-22",views:234,location:"中医体质辨识中心",tags:["体质辨识","个性化调理","中医诊疗"]},{id:103,name:"八段锦健身操教学",cat:"fitness_activity",cat_name:"健身活动",desc:"学习传统八段锦健身操，通过简单易学的动作达到强身健体、延年益寿的效果。",thumbnail:"https://picsum.photos/400/300?random=103",isfree:1,startdate:"2024-12-25",enddate:"2024-12-25",views:678,location:"医院健身广场",tags:["八段锦","健身操","传统运动"]},{id:104,name:"中医药膳制作体验",cat:"practical_activity",cat_name:"实践活动",desc:"现场学习制作几种简单易做的中医药膳，了解药食同源的理念，掌握日常养生技巧。",thumbnail:"https://picsum.photos/400/300?random=104",isfree:0,startdate:"2024-12-28",enddate:"2024-12-28",views:345,location:"中医药膳制作室",tags:["药膳","制作体验","药食同源"]},{id:105,name:"年末健康体检活动",cat:"health_checkup",cat_name:"健康体检",desc:"提供全面的中医健康体检服务，包括脉诊、舌诊、体质检测等，为新年健康打好基础。",thumbnail:"https://picsum.photos/400/300?random=105",isfree:0,startdate:"2024-12-30",enddate:"2024-12-31",views:567,location:"体检中心",tags:["健康体检","中医诊断","年末检查"]}],a=()=>[{id:201,name:"新年中医养生规划讲座",cat:"health_lecture",cat_name:"健康讲座",desc:"新年伊始，专家为大家制定全年的中医养生计划，包括四季养生要点和个人调理方案。",thumbnail:"https://picsum.photos/400/300?random=201",isfree:1,startdate:"2025-01-05",enddate:"2025-01-05",views:0,location:"医院大礼堂",tags:["新年养生","养生规划","四季调理"]},{id:202,name:"春节期间饮食调理指导",cat:"health_consultation",cat_name:"健康咨询",desc:"针对春节期间的饮食特点，提供专业的中医饮食调理建议，帮助大家健康过节。",thumbnail:"https://picsum.photos/400/300?random=202",isfree:1,startdate:"2025-01-15",enddate:"2025-01-15",views:0,location:"营养咨询室",tags:["春节饮食","饮食调理","节日养生"]},{id:203,name:"中医美容养颜技术培训",cat:"professional_training",cat_name:"专业培训",desc:"学习中医美容养颜的理论和实践技术，包括面部按摩、穴位美容、中药面膜制作等。",thumbnail:"https://picsum.photos/400/300?random=203",isfree:0,startdate:"2025-01-20",enddate:"2025-01-22",views:0,location:"美容培训中心",tags:["中医美容","养颜技术","专业培训"]},{id:204,name:"立春养生文化节",cat:"cultural_activity",cat_name:"文化活动",desc:"庆祝立春节气，举办中医养生文化展示、互动体验、专家义诊等丰富多彩的活动。",thumbnail:"https://picsum.photos/400/300?random=204",isfree:1,startdate:"2025-02-04",enddate:"2025-02-04",views:0,location:"医院广场",tags:["立春","养生文化节","节气养生"]},{id:205,name:"儿童春季调理与保健",cat:"health_lecture",cat_name:"健康讲座",desc:"专门针对儿童春季常见问题的中医调理方法，帮助家长掌握儿童春季保健知识。",thumbnail:"https://picsum.photos/400/300?random=205",isfree:1,startdate:"2025-02-15",enddate:"2025-02-15",views:0,location:"儿科诊疗中心",tags:["儿童保健","春季调理","家长教育"]},{id:206,name:"中医药研究成果发布会",cat:"academic_conference",cat_name:"学术会议",desc:"发布医院最新的中医药研究成果，邀请业内专家学者共同探讨中医药发展前景。",thumbnail:"https://picsum.photos/400/300?random=206",isfree:0,startdate:"2025-02-28",enddate:"2025-02-28",views:0,location:"学术会议厅",tags:["研究成果","学术发布","中医药发展"]}],i=async()=>{if(!(_.value||p.value)){p.value=!0;try{const s={page:y.value,page_size:m.value,param:l.value};console.log(`加载${v.value}数据，参数:`,s);let c;switch(l.value){case"past":c=await tt(s);break;case"current":c=await Z(s);break;case"upcoming":c=await Y(s);break;default:c=await X(s)}console.log(`${v.value}API响应:`,c),h.value.push(...c.results),y.value+=1,_.value=c.is_last_page||c.results.length<m.value}catch(s){console.error(`加载${v.value}失败:`,s),E(`加载${v.value}失败，请重试`);try{const c=w(l.value),t=(y.value-1)*m.value,k=t+m.value,A=c.slice(t,k);A.length>0?(h.value.push(...A),y.value+=1,_.value=k>=c.length,E("已加载离线数据")):_.value=!0}catch(c){console.error("加载模拟数据也失败:",c),_.value=!0}}finally{p.value=!1}}},w=s=>{switch(s){case"past":return b();case"current":return L();case"upcoming":return a();default:return b()}},C=s=>{console.log("点击活动卡片:",s),T.push(`/activity-detail/${s.id}`)},D=()=>{h.value=[],y.value=1,_.value=!1,p.value=!1};return V(()=>l.value,()=>{console.log("活动类型参数变化:",l.value),D(),i()},{immediate:!1}),N(()=>{console.log("ActivityList页面加载，类型:",l.value),i()}),(s,c)=>(n(),r("div",Et,[u(W,{title:v.value},null,8,["title"]),e("div",Ut,[e("div",Nt,[u(zt,{items:h.value,loading:p.value,finished:_.value,"use-infinite-scroll":!0,"empty-text":f(),"activity-type":l.value,onLoadMore:i,onCardClick:C},null,8,["items","loading","finished","empty-text","activity-type"])])]),u(J)]))}}),Kt=F(Vt,[["__scopeId","data-v-14e9801b"]]);export{Kt as default};
