<template>
  <div class="news-page category-tabs-container">
    <GlobalHeader title="中医文化" />
    <van-tabs 
      v-model:active="activeTab" 
      class="category-tabs" 
      sticky 
      :sticky-offset-top="0"
      swipeable
    >
      <van-tab v-for="tab in tabs" :key="tab.id" :title="tab.name">
        <div class="section-container">
          <div class="news-grid">
            <CommonCardList :items="items" :loading="loading" :finished="finished" :use-infinite-scroll="true"
              @load-more="loadItems" @card-click="handleCardClick" />
          </div>
        </div>
      </van-tab>
    </van-tabs>
    <GlobalFooter />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue';
import { useRouter } from 'vue-router';
import GlobalHeader from '../../components/GlobalHeader.vue';
import GlobalFooter from '../../components/GlobalFooter.vue';
import CommonCardList from '../../components/CommonCardList.vue';
import { getCultureCategoriesWithRetry, getCultureCategories } from './api'; // 假设支持分页参数
import type { CultureItem, CultureCategory } from './api'; // 假设有定义的类型

const router = useRouter();
const loading = ref(false);
const finished = ref(false);
const items = ref<CultureItem[]>([]);
const page = ref(1);
const activeTab = ref(0);
const pageSize = ref(5); // 每次加载10条数据

// 标签数据
const tabs = ref<{ id: string; name: string }[]>([]);
// 加载标签数据
const loadCategories = async () => {
  const res = await getCultureCategories();
  console.log('获取到领导分类sdfsdfsd:', res);

  // 将API返回的分类数据转换为标签格式
  const categoryTabs = res.categories.map((category: CultureCategory) => ({
    id: category.value,
    name: category.label
  }));

  // 更新标签数据，保留"全部"标签
  tabs.value = [...categoryTabs];
};
// 监听标签变化
watch(activeTab, (newVal) => {
  // 重置分页相关
  page.value = 1;
  items.value = [];
  finished.value = false;

  // 重新加载数据
  loadItems();
});

const loadItems = async () => {
  if (finished.value || loading.value) return;

  loading.value = true;
  const currentTabId = tabs.value[activeTab.value]?.id;
  console.log('当前选中标签ID:', currentTabId);

  try {
    const res = await getCultureCategoriesWithRetry(
      currentTabId,
      page.value,
      pageSize.value
    );

    items.value.push(...res.results);
    page.value += 1;

    finished.value = res.is_last_page === true;

  } catch (error) {
    console.error('加载失败:', error);
  } finally {
    loading.value = false;
  }
};

const handleCardClick = (news: any) => {
  router.push({ name: 'CultureDetail', params: { id: news.id } });
};
onMounted(() => {
  loadCategories();
  loadItems();
});
</script>

<style scoped>
@import '../../style/tabs.css';

/* 页面基础样式 */
.news-page {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  padding-bottom: 80px;
  position: relative;
  width: 100%;
  max-width: 100vw;
  overflow-x: hidden;
  box-sizing: border-box;
}

.section-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 5px;
  background-color: #f8f9fa;
}

.news-grid {
  flex: 1;
  display: flex;
  flex-direction: column;
}
</style>
