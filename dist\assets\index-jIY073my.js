import{d as T,O as I,Q as c,S,r as i,k as p,Y as x,aI as P,U as v,ak as w,l as B,N as C,aD as N,y as z,b as s,I as O,a2 as j,b2 as A,aC as D,B as U,b3 as _,az as q,b4 as E,X as L}from"./index-BE8XLQ59.js";const[M,u]=S("back-top"),Q={right:c,bottom:c,zIndex:c,target:[String,Object],offset:I(200),immediate:Boolean,teleport:{type:[String,Object],default:"body"}};var R=T({name:M,inheritAttrs:!1,props:Q,emits:["click"],setup(e,{emit:g,slots:d,attrs:b}){let r=!1;const a=i(!1),n=i(),o=i(),h=p(()=>x(P(e.zIndex),{right:v(e.right),bottom:v(e.bottom)})),k=t=>{var l;g("click",t),(l=o.value)==null||l.scrollTo({top:0,behavior:e.immediate?"auto":"smooth"})},f=()=>{a.value=o.value?q(o.value)>=+e.offset:!1},y=()=>{const{target:t}=e;if(typeof t=="string"){const l=document.querySelector(t);if(l)return l}else return t},m=()=>{D&&U(()=>{o.value=e.target?y():_(n.value),f()})};return w("scroll",E(f,100),{target:o}),B(m),C(()=>{r&&(a.value=!0,r=!1)}),N(()=>{a.value&&e.teleport&&(a.value=!1,r=!0)}),z(()=>e.target,m),()=>{const t=s("div",j({ref:e.teleport?void 0:n,class:u({active:a.value}),style:h.value,onClick:k},b),[d.default?d.default():s(O,{name:"back-top",class:u("icon")},null)]);return e.teleport?[s("div",{ref:n,class:u("placeholder")},null),s(A,{to:e.teleport},{default:()=>[t]})]:t}}});const X=L(R);export{X as B};
