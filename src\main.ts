import { createApp, createVNode } from 'vue'
import './style.css'
import App from './App.vue'
import router from './router'; // 引入路由
import { Lazyload } from 'vant'; // 引入Lazyload
import { useSystemConfig } from './composables/useSystemConfig'; // 引入系统配置
import { updateSeoMeta } from './utils/seoManager'; // 引入SEO管理工具

const app = createApp(App);
app.use(router); // 使用路由

// 配置懒加载，优化轮播图加载性能
app.use(Lazyload, {
  lazyComponent: true,
  preLoad: 1.5, // 预加载高度比例，提前加载
  attempt: 3, // 尝试次数
  throttleWait: 100, // 减少节流等待时间，提高响应速度
  observerOptions: {
    rootMargin: '300px', // 提前300px开始加载
    threshold: 0
  }
});

// 初始化系统配置
const systemConfig = useSystemConfig();
// 应用启动时立即加载配置（异步，不阻塞应用启动）
systemConfig.loadSystemConfig().then(() => {
  // 配置加载成功后，更新SEO信息
  const seoConfig = systemConfig.effectiveSeoConfig.value;
  updateSeoMeta(seoConfig);
}).catch(error => {
  console.warn('系统配置初始化失败，将使用默认配置:', error);
  // 即使配置加载失败，也尝试更新SEO（会使用默认配置）
  const seoConfig = systemConfig.effectiveSeoConfig.value;
  updateSeoMeta(seoConfig);
});

app.mount('#app');
