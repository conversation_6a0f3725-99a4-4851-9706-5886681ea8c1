/**
 * 预渲染脚本
 * 为微信分享生成静态HTML页面
 */

const fs = require('fs');
const path = require('path');

// 页面配置
const pages = [
  {
    route: '/',
    title: '中医智慧 - 传承千年中医文化，守护您的健康',
    description: '专业的中医健康平台，提供中医知识、养生指导、专家咨询等服务。传承千年中医文化，守护您的健康。',
    keywords: '中医,中药,养生,健康,传统医学,中医文化'
  },
  {
    route: '/home',
    title: '医院介绍 - 中医智慧',
    description: '了解我们的中医医院，专业的医疗团队，先进的诊疗设备，为您提供优质的中医医疗服务。',
    keywords: '中医医院,医院介绍,中医科室,医疗团队'
  },
  {
    route: '/knowledge',
    title: '中医文化 - 中医智慧',
    description: '传承千年中医文化，弘扬中华医学智慧，感受中医魅力，学习中医养生知识。',
    keywords: '中医文化,中医理论,养生知识,中医智慧'
  },
  {
    route: '/activity',
    title: '活动组织 - 中医智慧',
    description: '中医健康活动，专家讲座，养生课程，与您共享健康生活，传播中医文化。',
    keywords: '中医活动,健康讲座,养生课程,中医培训'
  }
];

// 读取模板文件
const templatePath = path.join(__dirname, '../dist/index.html');
const template = fs.readFileSync(templatePath, 'utf-8');

// 为每个页面生成HTML
pages.forEach(page => {
  let html = template;
  
  // 替换title
  html = html.replace(
    /<title>.*?<\/title>/,
    `<title>${page.title}</title>`
  );
  
  // 替换description
  html = html.replace(
    /<meta name="description" content=".*?">/,
    `<meta name="description" content="${page.description}">`
  );
  
  // 替换keywords
  html = html.replace(
    /<meta name="keywords" content=".*?">/,
    `<meta name="keywords" content="${page.keywords}">`
  );
  
  // 替换og:title
  html = html.replace(
    /<meta property="og:title" content=".*?">/,
    `<meta property="og:title" content="${page.title}">`
  );
  
  // 替换og:description
  html = html.replace(
    /<meta property="og:description" content=".*?">/,
    `<meta property="og:description" content="${page.description}">`
  );
  
  // 替换og:url
  const fullUrl = `https://gujiao.sxaliyun.cn${page.route}`;
  html = html.replace(
    /<meta property="og:url" content=".*?">/,
    `<meta property="og:url" content="${fullUrl}">`
  );
  
  // 替换twitter:title
  html = html.replace(
    /<meta name="twitter:title" content=".*?">/,
    `<meta name="twitter:title" content="${page.title}">`
  );
  
  // 替换twitter:description
  html = html.replace(
    /<meta name="twitter:description" content=".*?">/,
    `<meta name="twitter:description" content="${page.description}">`
  );
  
  // 替换itemprop name
  html = html.replace(
    /<meta itemprop="name" content=".*?">/,
    `<meta itemprop="name" content="${page.title}">`
  );
  
  // 替换itemprop description
  html = html.replace(
    /<meta itemprop="description" content=".*?">/,
    `<meta itemprop="description" content="${page.description}">`
  );
  
  // 添加canonical链接
  if (!html.includes('<link rel="canonical"')) {
    html = html.replace(
      /<link rel="canonical" href=".*?">/,
      `<link rel="canonical" href="${fullUrl}">`
    );
  }
  
  // 确定输出路径
  let outputPath;
  if (page.route === '/') {
    outputPath = path.join(__dirname, '../dist/index.html');
  } else {
    const routePath = page.route.substring(1); // 移除开头的 /
    const dirPath = path.join(__dirname, '../dist', routePath);
    
    // 创建目录
    if (!fs.existsSync(dirPath)) {
      fs.mkdirSync(dirPath, { recursive: true });
    }
    
    outputPath = path.join(dirPath, 'index.html');
  }
  
  // 写入文件
  fs.writeFileSync(outputPath, html);
  console.log(`Generated: ${outputPath}`);
});

console.log('预渲染完成！');
console.log('建议：');
console.log('1. 将生成的HTML文件部署到服务器');
console.log('2. 配置nginx正确路由到对应的HTML文件');
console.log('3. 清除CDN缓存');
console.log('4. 使用微信开发者工具测试分享效果');
