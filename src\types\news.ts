// 统一的医院新闻数据类型
export interface HospitalNewsItem {
  id: number;
  name: string;
  desc: string;
  thumbnail: string;
  viewtimes?: string;
  create_time?: string;
}

// API响应类型
export interface HospitalNewsListResponse {
  results: HospitalNewsItem[];
  count: number;
  page?: number;
  page_size?: number;
  is_last_page?: boolean;
  next?: string | null;
  previous?: string | null;
}

// NewsCard组件需要的数据格式
export interface NewsCardData {
  id: number | string;
  name: string;
  desc?: string;
  thumbnail: string;
  category?: string;
  publishDate?: string;
  author?: string;
  views?: number;
} 