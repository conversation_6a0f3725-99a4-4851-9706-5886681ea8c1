import{s as K,v as y,K as C,d as x,u as E,r as o,y as G,l as L,c as _,o as f,b as u,w as g,F as B,e as I,f as R,g as b,_ as W}from"./index-BE8XLQ59.js";import{T as D,a as N}from"./index-CmzFzWyS.js";import"./index-sbxbYdRt.js";import{G as F,a as O}from"./GlobalHeader-lL88u8sR.js";import{C as S}from"./CommonCardList-1yAjUs8b.js";import"./index-B27ddgxZ.js";import"./index-ouF_E0m0.js";import"./index-CAfqjps3.js";function V(){return y(C.CATEGORIES)}function z(r,n=1,t=10){return y(C.KNOWLWDGE,{cat:r,page:n,page_size:t})}function M(r,n=1,t=10){return K(()=>z(r,n,t))}const U={class:"news-page category-tabs-container"},q={class:"section-container"},A={class:"news-grid"},H=x({__name:"index",setup(r){const n=E(),t=o(!1),l=o(!1),d=o([]),m=o(1),i=o(0),h=o(5),v=o([]),w=async()=>{const e=await V();console.log("获取到领导分类sdfsdfsd:",e);const s=e.categories.map(a=>({id:a.value,name:a.label}));v.value=[...s]};G(i,e=>{m.value=1,d.value=[],l.value=!1,p()});const p=async()=>{var s;if(l.value||t.value)return;t.value=!0;const e=(s=v.value[i.value])==null?void 0:s.id;console.log("当前选中标签ID:",e);try{const a=await M(e,m.value,h.value);d.value.push(...a.results),m.value+=1,l.value=a.is_last_page===!0}catch(a){console.error("加载失败:",a)}finally{t.value=!1}},T=e=>{n.push({name:"TcmKnowledgeDetail",params:{id:e.id}})};return L(()=>{w(),p()}),(e,s)=>{const a=D,k=N;return f(),_("div",U,[u(F,{title:"中医知识"}),u(k,{active:i.value,"onUpdate:active":s[0]||(s[0]=c=>i.value=c),class:"category-tabs",sticky:"","sticky-offset-top":0,swipeable:""},{default:g(()=>[(f(!0),_(B,null,I(v.value,c=>(f(),R(a,{key:c.id,title:c.name},{default:g(()=>[b("div",q,[b("div",A,[u(S,{items:d.value,loading:t.value,finished:l.value,"use-infinite-scroll":!0,onLoadMore:p,onCardClick:T},null,8,["items","loading","finished"])])])]),_:2},1032,["title"]))),128))]),_:1},8,["active"]),u(O)])}}}),ee=W(H,[["__scopeId","data-v-a9c5b0a2"]]);export{ee as default};
