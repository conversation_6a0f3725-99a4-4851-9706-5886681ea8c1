<template>
  <div class="activity-history-example">
    <div class="example-header">
      <h2>活动历史卡片列表示例</h2>
      <p>ActivityHistoryCardList 组件展示</p>
    </div>

    <div class="example-container">
      <!-- 基础示例 -->
      <div class="example-section">
        <h3>基础示例</h3>
        <ActivityHistoryCardList
          :items="sampleItems"
          :loading="loading"
          :finished="finished"
          :useInfiniteScroll="false"
          @card-click="handleCardClick"
        />
      </div>

      <!-- 无限滚动示例 -->
      <div class="example-section">
        <h3>无限滚动示例</h3>
        <ActivityHistoryCardList
          :items="infiniteItems"
          :loading="infiniteLoading"
          :finished="infiniteFinished"
          :useInfiniteScroll="true"
          @card-click="handleCardClick"
          @load-more="loadMoreItems"
        />
      </div>

      <!-- 空状态示例 -->
      <div class="example-section">
        <h3>空状态示例</h3>
        <ActivityHistoryCardList
          :items="[]"
          :loading="false"
          :finished="true"
          emptyText="暂无活动历史记录"
          @card-click="handleCardClick"
        />
      </div>

      <!-- 加载状态示例 -->
      <div class="example-section">
        <h3>加载状态示例</h3>
        <ActivityHistoryCardList
          :items="[]"
          :loading="true"
          :finished="false"
          @card-click="handleCardClick"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import ActivityHistoryCardList from './ActivityHistoryCardList.vue';

const loading = ref(false);
const finished = ref(true);
const infiniteLoading = ref(false);
const infiniteFinished = ref(false);

// 示例数据
const sampleItems = ref([
  {
    id: 1,
    name: '中医养生健康讲座',
    desc: '本次讲座将介绍中医养生的基本理念和实用方法，帮助大家了解如何通过中医理论来保持身体健康。',
    thumbnail: 'https://picsum.photos/300/200?random=1',
    startdate: '2024-01-15',
    enddate: '2024-01-15',
    location: '中医院大礼堂',
    participants: 120,
    cat_display: '健康讲座',
    status: '已结束'
  },
  {
    id: 2,
    name: '针灸技术培训班',
    desc: '面向医务人员的针灸技术专业培训，由资深中医师授课，理论与实践相结合。',
    thumbnail: 'https://picsum.photos/300/200?random=2',
    startdate: '2024-01-20',
    enddate: '2024-01-22',
    location: '培训中心',
    participants: 45,
    cat_display: '专业培训',
    status: '已结束'
  },
  {
    id: 3,
    name: '中药材识别活动',
    desc: '带领市民了解常见中药材的识别方法和功效，增进对中医药文化的了解。',
    thumbnail: 'https://picsum.photos/300/200?random=3',
    startdate: '2024-02-01',
    enddate: '2024-02-01',
    location: '中药园',
    participants: 80,
    cat_display: '科普活动',
    status: '已结束'
  }
]);

const infiniteItems = ref([...sampleItems.value]);

// 处理卡片点击
const handleCardClick = (item: any) => {
  console.log('点击了活动:', item);
  // 这里可以跳转到活动详情页
  // router.push(`/activity-detail/${item.id}`);
};

// 加载更多数据
const loadMoreItems = () => {
  if (infiniteLoading.value || infiniteFinished.value) return;
  
  infiniteLoading.value = true;
  
  // 模拟API请求
  setTimeout(() => {
    const newItems = [
      {
        id: infiniteItems.value.length + 1,
        name: `活动 ${infiniteItems.value.length + 1}`,
        desc: '这是一个模拟的活动描述，用于展示无限滚动功能。',
        thumbnail: `https://picsum.photos/300/200?random=${infiniteItems.value.length + 10}`,
        startdate: '2024-02-10',
        enddate: '2024-02-10',
        location: '会议室',
        participants: 60,
        cat_display: '模拟活动',
        status: '已结束'
      },
      {
        id: infiniteItems.value.length + 2,
        name: `活动 ${infiniteItems.value.length + 2}`,
        desc: '这是另一个模拟的活动描述，展示列表的多样性。',
        thumbnail: `https://picsum.photos/300/200?random=${infiniteItems.value.length + 11}`,
        startdate: '2024-02-15',
        enddate: '2024-02-15',
        location: '大厅',
        participants: 90,
        cat_display: '模拟活动',
        status: '已结束'
      }
    ];
    
    infiniteItems.value.push(...newItems);
    infiniteLoading.value = false;
    
    // 模拟到达底部
    if (infiniteItems.value.length >= 10) {
      infiniteFinished.value = true;
    }
  }, 1500);
};

onMounted(() => {
  console.log('ActivityHistoryCardList 示例页面已加载');
});
</script>

<style scoped>
.activity-history-example {
  padding: 20px;
  background: #f5f5f5;
  min-height: 100vh;
}

.example-header {
  text-align: center;
  margin-bottom: 30px;
  padding: 20px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.example-header h2 {
  color: #333;
  margin-bottom: 8px;
  font-size: 24px;
  font-weight: 600;
}

.example-header p {
  color: #666;
  font-size: 16px;
}

.example-container {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.example-section {
  background: #fff;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.example-section h3 {
  color: #333;
  margin-bottom: 16px;
  font-size: 18px;
  font-weight: 600;
  padding-bottom: 8px;
  border-bottom: 2px solid #10ac84;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .activity-history-example {
    padding: 16px;
  }

  .example-header {
    padding: 16px;
  }

  .example-header h2 {
    font-size: 20px;
  }

  .example-header p {
    font-size: 14px;
  }

  .example-section {
    padding: 16px;
  }

  .example-section h3 {
    font-size: 16px;
  }
}
</style> 