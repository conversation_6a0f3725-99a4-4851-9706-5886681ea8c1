import{d as p,c as s,o as t,F as u,e as h,g as c,i as k,t as d,_ as C}from"./index-BE8XLQ59.js";const f={class:"grid-card-container"},v=["onClick"],b={class:"card-image-section"},E=["src","alt","onError"],y={key:0,class:"card-badge"},B={class:"card-content-section"},G={class:"card-title"},x=p({__name:"GridCard",props:{items:{},clickable:{type:Boolean,default:!0},loading:{type:Boolean,default:!1}},emits:["cardClick","imageError"],setup(i,{emit:l}){const _=i,r=l,g=a=>{_.clickable&&r("cardClick",a)},m=(a,o)=>{const e=a.target;e.src="/default-image.png",r("imageError",o)};return(a,o)=>(t(),s("div",f,[(t(!0),s(u,null,h(a.items,e=>(t(),s("div",{key:e.id,class:"grid-card-item",onClick:n=>g(e)},[c("div",b,[c("img",{src:e.image,alt:e.title,class:"card-image",onError:n=>m(n,e)},null,40,E),e.badge?(t(),s("div",y,d(e.badge),1)):k("",!0)]),c("div",B,[c("h3",G,d(e.title),1)])],8,v))),128))]))}}),I=C(x,[["__scopeId","data-v-c9be6d61"]]);export{I as G};
