/**
 * API URL常量
 * 集中管理所有API路径，便于维护和修改
 */

// 轮播图相关API
export const ADVERTISE_URLS = {
  LIST: '/api/advertise/advertises/'
};
// 领导信息相关API
export const LEADER_URLS = {
  CATEGORIES: '/api/base/leader_choices/',
  LEADER: '/api/base/leader/',
};
// 名中医相关API
export const FAMOUS_DOCTOR_URLS = {
  CATEGORIES: '/api/base/famous_doctor_choices/',
  DOCTOR: '/api/base/famous_doctor/'
};
// 特色制剂相关API
export const MEDICINE_URLS = {
  CATEGORIES: '/api/base/special_preparations_choices/',
  MEDICINE: '/api/base/special_preparations/'
};
// 特色科室相关API
export const DEPT_URLS = {
  CATEGORIES: '/api/base/characteristic_depart_choices/',
  DEPT: '/api/base/characteristic_depart/'
};
// 重点科室相关API
export const MAINDEPT_URLS = {
  CATEGORIES: '/api/base/main_depart_choices/',
  MAINDEPT: '/api/base/main_depart/'
};
// 医院新闻相关API
export const HOSPITAL_NEWS_URLS = {
  NEWS: '/api/base/hospital_news/'
};
// 科教图文相关API
export const EDUCATION_MEDIA_URLS = {
  CATEGORIES: '/api/drumbeating/drumbeating_choices/',
  EDUCATION: '/api/drumbeating/drumbeating/'
};
// 中医知识相关API
export const KNOWLEDGE_URLS = {
  CATEGORIES: '/api/drumbeating/knowledge_choices/',
  KNOWLWDGE: '/api/drumbeating/knowledge/',
  HOME_CONTENT: '/api/drumbeating/home_content/'
};
// 中医文化相关API
export const CULTURE_URLS = {
  CATEGORIES: '/api/drumbeating/culture_choices/',
  CULTURE: '/api/drumbeating/culture/',
};
// 中医案例相关API
export const CASES_URLS = {
  CATEGORIES: '/api/drumbeating/case_choices/',
  CASE: '/api/drumbeating/case/',
};
// 视频宣传相关API
export const VIDEO_URLS = {
  CATEGORIES: '/api/drumbeating/videos_choices/',
  VIDEO: '/api/drumbeating/videos/',
};
// 活动相关API
export const ACTIVITY_URLS = {
  ACTIVITY: '/api/activity/activities/',
  HOME_CONTENT: '/api/activity/home_content/',
  NEWS: '/api/activity/activity_news/',
  REGIST: '/api/activity/activity_registration/',
  SENDSMS: '/api/activity/sms_code/send/',
  VERIFYCODE: '/api/activity/sms_code/verify/',
};


// 分类相关API
export const CATEGORY_URLS = {
  LEADER_CATEGORIES: '/base/get_leader_categories/',
  LEADER_LIST: '/base/get_leaders/',
  LEADER_BY_CATEGORY: '/base/get_leaders_by_cat/',
  LEADER_DETAIL: '/base/leader_detail/'
};





















// 特色科室新接口
export const CHARACTERISTIC_DEPT_URLS = {
  CATEGORIES: '/api/base/characteristic_depart_choices/',
  BY_CATEGORY: '/api/base/characteristic_depart/',
  DETAIL: '/api/base/characteristic_depart/'
};

// 系统配置相关API
export const SYSTEM_CONFIG_URLS = {
  HOSPITAL_INFO: '/api/system/hospital_info/',   // 医院基本信息
  CONTACT_INFO: '/api/system/contact_info/',     // 联系方式信息
  SEO_CONFIG: '/api/system/seo_config/',         // SEO配置信息
};

// 构建完整的API路径
export function buildUrl(baseUrl: string, id?: string | number): string {
  if (id !== undefined) {
    return `${baseUrl}${id}/`;
  }
  return baseUrl;
}