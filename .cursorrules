# TCM H5 Application - Project Intelligence

## Project Overview

- **Type**: Traditional Chinese Medicine (TCM) culture promotion H5 mobile application
- **Architecture**: Vue 3.5 + TypeScript + Vant UI + Vite
- **Status**: 85-90% complete, production-ready with ongoing enhancements
- **Domain**: Healthcare education, cultural promotion, expert network

## Critical Development Patterns

### Component Architecture Preferences

- **Shared Components**: Always use existing shared components (GlobalHeader, CommonCardList, GridCard, etc.) before creating new ones
- **Mobile-First**: All components must work excellently on mobile devices first
- **Vant UI**: Prefer Vant components for consistency with mobile design system
- **TypeScript**: Full type safety required - all new code must be properly typed

### API Integration Standards

- **Centralized API**: All API calls go through `src/api/request.ts` with proper error handling
- **Mock Data**: Implement fallback mock data for development and testing, especially for new features
- **Error Handling**: Always implement proper loading states and user-friendly error messages
- **Pagination**: Use infinite scroll pattern with `van-list` for content lists

### File Organization Principles

- **Module Structure**: Each feature module follows the pattern: `src/views/{Module}/index.vue` and `src/views/{Module}Detail/index.vue`
- **Component Location**: Shared components in `src/components/`, feature-specific components in module folders
- **API Files**: Module-specific API functions in `src/views/{Module}/api.ts`
- **Types**: TypeScript interfaces in `src/types/` with proper module organization

### TCM-Specific Considerations

- **Cultural Sensitivity**: All content must respect traditional Chinese medicine terminology and concepts
- **Chinese Language**: Optimize for Chinese text rendering and input
- **Medical Content**: Ensure all medical information is accurate and appropriately presented
- **User Trust**: Healthcare content requires extra attention to credibility and professional presentation

## Development Workflow Insights

### Performance Critical Areas

- **Image Loading**: TCM content includes many medical images - always implement lazy loading
- **Bundle Size**: Monitor carefully as Vant UI and medical content can increase bundle size
- **Mobile Performance**: Test on actual mobile devices, especially slower Android phones
- **API Response Time**: Some TCM content APIs may be slower - implement appropriate loading states

### User Experience Patterns

- **Navigation**: Hash-based routing works well for deployment flexibility
- **Touch Interactions**: All interactive elements must be touch-friendly (minimum 44px)
- **Loading States**: Users expect immediate feedback for all actions
- **Back Navigation**: Always provide clear back navigation, especially important for medical content browsing

### Testing Approach

- **Cross-Device**: Test on various mobile devices and screen sizes
- **Content Loading**: Test with slow internet connections common in healthcare settings
- **Error Scenarios**: Test API failures and provide graceful degradation
- **Accessibility**: Healthcare applications require strong accessibility compliance

## Component Creation Guidelines

### When Creating New Components

1. Check if existing shared components can be extended or customized
2. Follow the ActivityHistoryCardList pattern for specialized list components
3. Implement proper prop interfaces with TypeScript
4. Include loading, error, and empty states
5. Ensure mobile responsiveness and touch interactions
6. Add proper documentation (README.md and example.vue files)

### API Integration Best Practices

1. Use the centralized request handling from `src/api/request.ts`
2. Implement proper TypeScript interfaces for API responses
3. Add retry logic for healthcare content (users may have poor connections)
4. Provide meaningful error messages in Chinese for user-facing errors
5. Implement caching for frequently accessed TCM content

### Link Handling Best Practices

1. **Smart URL Processing**: Always distinguish between internal routes and external links
2. **Complete URL Support**: Handle project-specific complete URLs by extracting route paths
3. **External Link Handling**: Open external links in new windows for better UX
4. **Error Handling**: Provide user-friendly error messages for link failures
5. **Debug Logging**: Include console logging for troubleshooting link issues

## Known Project Patterns to Follow

### Successful Patterns to Replicate

- **ActivityHistoryCardList**: Good example of specialized component with mock data fallback
- **CommonCardList**: Standard pattern for content display with infinite scroll
- **Module-Detail Pattern**: Consistent detail page structure across all content types
- **Global Header**: Consistent navigation header with proper back button handling
- **Carousel Smart Link Handling**: Universal pattern for handling various URL formats (complete URLs, external links, internal routes)

### Patterns to Avoid

- **Deep Component Nesting**: Keep component hierarchy flat for better performance
- **Large Bundle Imports**: Use selective imports from libraries (especially Vant)
- **Inline Styles**: Use scoped CSS or CSS modules for component styling
- **Hard-coded API URLs**: Always use the centralized URL management system

## Healthcare-Specific Considerations

### Content Accuracy

- All TCM-related content must be medically accurate
- Traditional medicine terms should be properly translated and explained
- Expert profiles and credentials must be clearly presented
- Activity information must be current and accurate

### User Trust Factors

- Professional visual design with appropriate medical imagery
- Clear information hierarchy for complex medical concepts
- Proper crediting of medical experts and institutions
- Transparent presentation of activity details and registration information

### Accessibility for Healthcare

- Support for users with varying technical literacy
- Clear, simple language for complex medical concepts
- Proper contrast ratios for users with vision challenges
- Touch targets appropriate for users with mobility challenges

## Future Development Guidance

### Immediate Priorities

1. **Performance Optimization**: Focus on mobile loading speeds
2. **Search Implementation**: Add content search functionality
3. **Analytics**: Implement user behavior tracking
4. **Accessibility**: Enhance compliance with healthcare standards

### Long-term Evolution

- Consider Progressive Web App (PWA) features for offline access
- Explore integration with hospital appointment systems
- Plan for multi-language support (simplified/traditional Chinese)
- Consider voice accessibility features for healthcare contexts

This project intelligence captures the unique characteristics and requirements of the TCM H5 application, ensuring consistent development practices that respect both technical excellence and healthcare content standards.
