import html2canvas from 'html2canvas';
/**
 * 等待图片加载完成
 */
async function waitForImages(element) {
    const images = element.querySelectorAll('img');
    const promises = Array.from(images).map((img) => {
        if (img.complete && img.naturalWidth > 0)
            return Promise.resolve();
        return new Promise((resolve) => {
            const timeoutId = setTimeout(() => {
                console.warn('图片加载超时:', img.src);
                resolve();
            }, 5000);
            img.onload = () => {
                clearTimeout(timeoutId);
                resolve();
            };
            img.onerror = () => {
                console.warn('图片加载失败:', img.src);
                clearTimeout(timeoutId);
                resolve();
            };
        });
    });
    await Promise.all(promises);
    // 额外等待确保DOM渲染完成
    await new Promise(resolve => setTimeout(resolve, 500));
}
/**
 * 确保元素完全可见并渲染完成
 */
async function ensureElementReady(element) {
    // 临时显示元素（如果隐藏的话）
    const originalDisplay = element.style.display;
    const originalVisibility = element.style.visibility;
    const originalPosition = element.style.position;
    element.style.display = 'block';
    element.style.visibility = 'visible';
    element.style.position = 'relative';
    // 添加图片保存优化样式
    const hasImageSavingClass = element.classList.contains('image-saving');
    if (!hasImageSavingClass) {
        element.classList.add('image-saving');
    }
    // 强制重新计算布局
    element.offsetHeight;
    // 等待布局稳定和样式应用
    await new Promise(resolve => requestAnimationFrame(resolve));
    await new Promise(resolve => setTimeout(resolve, 200));
    // 恢复原始样式（如果需要的话）
    if (originalDisplay)
        element.style.display = originalDisplay;
    if (originalVisibility)
        element.style.visibility = originalVisibility;
    if (originalPosition)
        element.style.position = originalPosition;
    return () => {
        // 返回清理函数
        if (!hasImageSavingClass) {
            element.classList.remove('image-saving');
        }
    };
}
/**
 * 等待DOM数据渲染完成 - 专门处理异步数据渲染问题
 */
async function waitForDataRendering(element) {
    console.log('开始等待数据渲染...');
    // 检查关键数据元素是否存在且有内容
    const checkDataElements = () => {
        // 检查报名编号
        const regnoElement = element.querySelector('.registration-info-card .info-item .value');
        const regnoValue = regnoElement?.textContent?.trim();
        // 检查二维码
        const qrcodeImg = element.querySelector('.qrcode-image');
        const qrcodeLoaded = qrcodeImg && qrcodeImg.src && qrcodeImg.complete && qrcodeImg.naturalWidth > 0;
        // 检查报名时间
        const timeElements = element.querySelectorAll('.registration-info-card .info-item .value');
        const hasCreateTime = Array.from(timeElements).some(el => el.textContent?.includes('-') || el.textContent?.includes(':'));
        console.log('数据检查结果:', {
            regnoValue,
            qrcodeLoaded,
            hasCreateTime,
            qrcodeSrc: qrcodeImg?.src?.substring(0, 50) + '...'
        });
        return regnoValue &&
            regnoValue !== '' &&
            regnoValue !== '加载中...' &&
            qrcodeLoaded &&
            hasCreateTime;
    };
    // 最多等待10秒，每100ms检查一次
    let attempts = 0;
    const maxAttempts = 100;
    while (attempts < maxAttempts) {
        if (checkDataElements()) {
            console.log('数据渲染完成，等待额外渲染时间...');
            // 数据已渲染，额外等待确保DOM更新完成
            await new Promise(resolve => setTimeout(resolve, 800));
            return;
        }
        await new Promise(resolve => setTimeout(resolve, 100));
        attempts++;
        if (attempts % 10 === 0) {
            console.log(`数据渲染等待中... ${attempts}/${maxAttempts}`);
        }
    }
    console.warn('数据渲染等待超时，但继续执行截图');
}
/**
 * 优化的图片保存函数 - 解决信息不完整问题
 */
export async function saveElementAsImage(element, filename = '报名信息', options = {}) {
    let cleanup;
    let scrollCleanup;
    try {
        console.log('开始优化保存图片流程...');
        // 0. 详细调试元素状态（新增）
        await debugElementForCapture(element);
        // 1. 首先等待数据渲染完成（最重要！）
        await waitForDataRendering(element);
        // 2. 处理长页面滚动问题（关键修复！）
        scrollCleanup = await handleScrollableElement(element);
        // 3. 确保元素准备就绪
        cleanup = await ensureElementReady(element);
        // 4. 等待所有图片加载完成
        await waitForImages(element);
        // 获取元素的实际尺寸 - 重要：确保获取完整内容区域
        const rect = element.getBoundingClientRect();
        const computedStyle = window.getComputedStyle(element);
        // 计算包含padding和border的完整尺寸
        const paddingLeft = parseFloat(computedStyle.paddingLeft) || 0;
        const paddingRight = parseFloat(computedStyle.paddingRight) || 0;
        const paddingTop = parseFloat(computedStyle.paddingTop) || 0;
        const paddingBottom = parseFloat(computedStyle.paddingBottom) || 0;
        const borderLeft = parseFloat(computedStyle.borderLeftWidth) || 0;
        const borderRight = parseFloat(computedStyle.borderRightWidth) || 0;
        const borderTop = parseFloat(computedStyle.borderTopWidth) || 0;
        const borderBottom = parseFloat(computedStyle.borderBottomWidth) || 0;
        // 使用scrollHeight确保包含所有内容
        const fullWidth = Math.max(element.scrollWidth, element.offsetWidth, rect.width, element.clientWidth + paddingLeft + paddingRight + borderLeft + borderRight);
        const fullHeight = Math.max(element.scrollHeight, element.offsetHeight, rect.height, element.clientHeight + paddingTop + paddingBottom + borderTop + borderBottom);
        console.log('元素尺寸信息:', {
            rect: { width: rect.width, height: rect.height },
            scroll: { width: element.scrollWidth, height: element.scrollHeight },
            offset: { width: element.offsetWidth, height: element.offsetHeight },
            client: { width: element.clientWidth, height: element.clientHeight },
            computed: { width: fullWidth, height: fullHeight },
            padding: { left: paddingLeft, right: paddingRight, top: paddingTop, bottom: paddingBottom },
            border: { left: borderLeft, right: borderRight, top: borderTop, bottom: borderBottom }
        });
        // 获取当前滚动位置
        const currentScrollTop = window.scrollY || document.documentElement.scrollTop;
        const currentScrollLeft = window.scrollX || document.documentElement.scrollLeft;
        // 获取元素相对于页面的实际位置
        const elementRect = element.getBoundingClientRect();
        const elementPageTop = elementRect.top + currentScrollTop;
        const elementPageLeft = elementRect.left + currentScrollLeft;
        // 优化的html2canvas配置 - 专门处理长页面截图
        const defaultOptions = {
            backgroundColor: '#ffffff',
            scale: Math.min(Math.max(window.devicePixelRatio || 2, 2), 3), // 限制最大倍数避免内存问题
            useCORS: true,
            allowTaint: true,
            logging: true, // 开启调试日志
            // 关键修复：明确指定截图区域，避免滚动问题
            scrollX: 0,
            scrollY: 0,
            x: 0, // 从元素左边开始
            y: 0, // 从元素顶部开始
            width: fullWidth,
            height: fullHeight,
            // 重要：设置窗口大小来确保长页面正确渲染
            windowWidth: Math.max(fullWidth, window.innerWidth),
            windowHeight: Math.max(fullHeight, window.innerHeight, elementRect.height + 100),
            foreignObjectRendering: false, // 禁用外部对象渲染以提高兼容性
            imageTimeout: 15000, // 增加图片超时时间
            removeContainer: true, // 渲染后移除临时容器
            ignoreElements: (el) => {
                // 忽略可能干扰的元素
                const shouldIgnore = el.classList?.contains('van-toast') ||
                    el.classList?.contains('van-overlay') ||
                    el.classList?.contains('van-popup') ||
                    el.tagName === 'SCRIPT' ||
                    el.tagName === 'STYLE';
                if (shouldIgnore) {
                    console.log('忽略元素:', el.tagName, el.className);
                }
                return shouldIgnore;
            },
            onclone: (clonedDoc, clonedElement) => {
                console.log('HTML2Canvas克隆完成:', {
                    clonedElement: clonedElement.tagName,
                    children: clonedElement.children.length,
                    innerHTML: clonedElement.innerHTML.substring(0, 200) + '...'
                });
                // 确保克隆元素的样式正确
                const clonedStyle = clonedDoc.defaultView?.getComputedStyle(clonedElement);
                console.log('克隆元素样式:', {
                    display: clonedStyle?.display,
                    visibility: clonedStyle?.visibility,
                    opacity: clonedStyle?.opacity,
                    backgroundColor: clonedStyle?.backgroundColor
                });
            },
            ...options
        };
        console.log('html2canvas配置:', defaultOptions);
        // 临时调整元素样式以确保完整渲染
        const originalOverflow = element.style.overflow;
        const originalMaxHeight = element.style.maxHeight;
        const originalHeight = element.style.height;
        element.style.overflow = 'visible';
        element.style.maxHeight = 'none';
        element.style.height = 'auto';
        // 强制重新布局
        element.offsetHeight;
        await new Promise(resolve => setTimeout(resolve, 300));
        try {
            // 创建canvas
            console.log('开始调用html2canvas...');
            const canvas = await html2canvas(element, defaultOptions);
            console.log('Canvas生成成功，详细信息:', {
                width: canvas.width,
                height: canvas.height,
                toDataURL: typeof canvas.toDataURL
            });
            if (canvas.width === 0 || canvas.height === 0) {
                throw new Error('生成的图片尺寸为0，页面内容可能未正确渲染');
            }
            // 详细检查canvas内容
            const context = canvas.getContext('2d');
            if (!context) {
                throw new Error('无法获取Canvas 2D上下文');
            }
            // 检查多个区域的像素数据
            const checkRegions = [
                { x: 0, y: 0, width: Math.min(canvas.width, 50), height: Math.min(canvas.height, 50), name: '左上角' },
                { x: Math.floor(canvas.width / 2) - 25, y: Math.floor(canvas.height / 2) - 25, width: 50, height: 50, name: '中心区域' },
                { x: Math.max(0, canvas.width - 50), y: Math.max(0, canvas.height - 50), width: 50, height: 50, name: '右下角' }
            ];
            let hasContent = false;
            for (const region of checkRegions) {
                try {
                    const imageData = context.getImageData(region.x, region.y, region.width, region.height);
                    const isRegionEmpty = imageData.data.every((pixel, index) => {
                        if (index % 4 === 3)
                            return true; // 忽略alpha通道
                        return pixel === 255; // 检查是否全为白色
                    });
                    console.log(`${region.name}区域检查:`, {
                        x: region.x,
                        y: region.y,
                        width: region.width,
                        height: region.height,
                        isEmpty: isRegionEmpty,
                        samplePixels: Array.from(imageData.data.slice(0, 20))
                    });
                    if (!isRegionEmpty) {
                        hasContent = true;
                    }
                }
                catch (error) {
                    console.warn(`检查${region.name}区域时出错:`, error);
                }
            }
            if (!hasContent) {
                // 尝试创建一个测试图片来验证Canvas功能
                const testCanvas = document.createElement('canvas');
                testCanvas.width = 100;
                testCanvas.height = 100;
                const testCtx = testCanvas.getContext('2d');
                if (testCtx) {
                    testCtx.fillStyle = '#ff0000';
                    testCtx.fillRect(0, 0, 100, 100);
                    const testDataURL = testCanvas.toDataURL();
                    console.log('Canvas功能测试成功，测试图片大小:', testDataURL.length);
                }
                throw new Error('生成的图片内容为空，所有检查区域都是白色。可能的原因：CSS样式问题、元素被隐藏、或html2canvas兼容性问题');
            }
            // 转换为高质量图片
            const dataURL = canvas.toDataURL('image/png', 1.0);
            if (!dataURL || dataURL === 'data:,') {
                throw new Error('图片数据生成失败');
            }
            console.log('图片数据生成成功，大小约:', Math.round(dataURL.length / 1024), 'KB');
            // 恢复元素样式
            element.style.overflow = originalOverflow;
            element.style.maxHeight = originalMaxHeight;
            element.style.height = originalHeight;
            // 智能保存
            const result = await saveImageToDevice(dataURL, filename);
            // 清理样式
            if (cleanup)
                cleanup();
            if (scrollCleanup)
                scrollCleanup();
            return result;
        }
        finally {
            // 确保恢复元素样式
            element.style.overflow = originalOverflow;
            element.style.maxHeight = originalMaxHeight;
            element.style.height = originalHeight;
        }
    }
    catch (error) {
        console.error('保存图片失败:', error);
        throw error;
    }
    finally {
        // 确保在任何情况下都清理样式和滚动位置
        if (cleanup)
            cleanup();
        if (scrollCleanup)
            scrollCleanup();
    }
}
/**
 * 智能设备保存 - 根据环境选择最佳保存方式
 */
async function saveImageToDevice(dataURL, filename) {
    const isWechat = /MicroMessenger/i.test(navigator.userAgent);
    const isIOS = /iPhone|iPad|iPod/i.test(navigator.userAgent);
    const isAndroid = /Android/i.test(navigator.userAgent);
    const isMobile = isWechat || isIOS || isAndroid || window.innerWidth <= 768;
    console.log('设备环境检测:', { isWechat, isIOS, isAndroid, isMobile });
    if (!isMobile) {
        // 桌面端直接下载
        return downloadImageDirectly(dataURL, filename);
    }
    // 移动端处理
    if (isWechat) {
        return await handleWechatSave(dataURL, filename);
    }
    else {
        return await handleMobileSave(dataURL, filename);
    }
}
/**
 * 桌面端直接下载
 */
function downloadImageDirectly(dataURL, filename) {
    try {
        const link = document.createElement('a');
        link.download = `${filename}_${new Date().toISOString().slice(0, 10)}.png`;
        link.href = dataURL;
        link.style.display = 'none';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        console.log('桌面端图片下载成功');
        return true;
    }
    catch (error) {
        console.error('桌面端下载失败:', error);
        return false;
    }
}
/**
 * 微信环境优化保存 - 使用更直观的方式
 */
async function handleWechatSave(dataURL, filename) {
    return new Promise((resolve) => {
        try {
            console.log('微信环境：创建直观的保存界面');
            // 创建全屏遮罩
            const overlay = document.createElement('div');
            overlay.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0,0,0,0.9);
        z-index: 99999;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 20px;
        box-sizing: border-box;
      `;
            // 创建头部区域（包含标题和关闭按钮）
            const header = document.createElement('div');
            header.style.cssText = `
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;
        margin-bottom: 20px;
      `;
            // 创建提示标题
            const title = document.createElement('div');
            title.style.cssText = `
        color: white;
        font-size: 18px;
        font-weight: bold;
        text-align: center;
        line-height: 1.4;
        flex: 1;
      `;
            title.innerHTML = '📱 保存成功！<br>请长按下方图片选择"保存图片"';
            // 创建关闭按钮
            const closeButton = document.createElement('div');
            closeButton.style.cssText = `
        width: 32px;
        height: 32px;
        border-radius: 50%;
        background: rgba(255,255,255,0.2);
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: background 0.2s ease;
        flex-shrink: 0;
      `;
            closeButton.innerHTML = '✕';
            closeButton.addEventListener('mouseover', () => {
                closeButton.style.background = 'rgba(255,255,255,0.3)';
            });
            closeButton.addEventListener('mouseout', () => {
                closeButton.style.background = 'rgba(255,255,255,0.2)';
            });
            header.appendChild(title);
            header.appendChild(closeButton);
            // 创建图片容器
            const imgContainer = document.createElement('div');
            imgContainer.style.cssText = `
        background: white;
        border-radius: 12px;
        padding: 15px;
        max-width: 90%;
        max-height: 70%;
        overflow: auto;
        box-shadow: 0 8px 32px rgba(0,0,0,0.3);
        position: relative;
      `;
            // 创建图片元素 - 关键修复：确保图片可以被长按保存
            const img = document.createElement('img');
            img.src = dataURL;
            img.style.cssText = `
        width: 100%;
        height: auto;
        display: block;
        border-radius: 8px;
        /* 关键：确保图片可以被长按保存 */
        -webkit-touch-callout: default;
        -webkit-user-select: auto;
        -khtml-user-select: auto;
        -moz-user-select: auto;
        -ms-user-select: auto;
        user-select: auto;
        pointer-events: auto;
      `;
            // 设置图片属性以支持保存
            img.setAttribute('crossorigin', 'anonymous');
            img.setAttribute('alt', '报名凭证');
            // 组装界面
            imgContainer.appendChild(img);
            overlay.appendChild(header);
            overlay.appendChild(imgContainer);
            // 关闭事件处理函数
            const closeModal = () => {
                document.body.removeChild(overlay);
                resolve(true);
            };
            // 点击关闭按钮关闭
            closeButton.addEventListener('click', (e) => {
                e.stopPropagation();
                closeModal();
            });
            // 防止点击图片容器时关闭
            imgContainer.addEventListener('click', (e) => {
                e.stopPropagation();
            });
            // ESC键关闭
            const handleKeyDown = (e) => {
                if (e.key === 'Escape') {
                    document.removeEventListener('keydown', handleKeyDown);
                    closeModal();
                }
            };
            document.addEventListener('keydown', handleKeyDown);
            // 添加到页面
            document.body.appendChild(overlay);
            console.log('微信环境：保存界面已显示');
        }
        catch (error) {
            console.error('微信保存界面创建失败:', error);
            resolve(false);
        }
    });
}
/**
 * 移动端保存处理
 */
async function handleMobileSave(dataURL, filename) {
    try {
        // 转换为Blob
        const response = await fetch(dataURL);
        const blob = await response.blob();
        // 尝试使用Web Share API
        if (navigator.share && navigator.canShare) {
            try {
                const file = new File([blob], `${filename}.png`, { type: 'image/png' });
                if (navigator.canShare({ files: [file] })) {
                    console.log('使用Web Share API分享');
                    await navigator.share({
                        title: '报名凭证',
                        text: '活动报名信息',
                        files: [file]
                    });
                    return true;
                }
            }
            catch (shareError) {
                console.log('Web Share API失败，使用备用方案:', shareError);
            }
        }
        // 备用方案：创建下载链接
        const blobUrl = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.download = `${filename}_${new Date().toISOString().slice(0, 10)}.png`;
        link.href = blobUrl;
        link.style.display = 'none';
        document.body.appendChild(link);
        link.click();
        setTimeout(() => {
            document.body.removeChild(link);
            URL.revokeObjectURL(blobUrl);
        }, 1000);
        console.log('移动端保存完成');
        return true;
    }
    catch (error) {
        console.error('移动端保存失败:', error);
        return false;
    }
}
/**
 * 主要的智能保存函数 - 简化的API
 */
export async function saveImageSmart(element, filename = '报名信息') {
    try {
        console.log('开始智能保存图片...');
        // 检查元素是否存在且可见
        if (!element) {
            throw new Error('保存目标元素不存在');
        }
        if (element.offsetWidth === 0 || element.offsetHeight === 0) {
            throw new Error('保存目标元素不可见，请检查页面显示');
        }
        // 使用优化的保存函数
        const success = await saveElementAsImage(element, filename, {
            // 移动端优化配置
            scale: window.devicePixelRatio >= 2 ? 2 : window.devicePixelRatio,
            useCORS: true,
            allowTaint: true,
            imageTimeout: 10000
        });
        return success;
    }
    catch (error) {
        console.error('智能保存失败:', error);
        throw error;
    }
}
/**
 * 移动端优化的保存图片函数（保持向后兼容）
 */
export async function saveMobileImage(element, filename = '报名信息') {
    return await saveImageSmart(element, filename);
}
/**
 * 详细的元素和样式调试 - 找出截图空白的原因
 */
async function debugElementForCapture(element) {
    console.log('=== 开始详细调试元素状态 ===');
    // 1. 基础元素信息
    console.log('1. 元素基础信息:', {
        tagName: element.tagName,
        className: element.className,
        id: element.id,
        innerHTML: element.innerHTML.substring(0, 200) + '...'
    });
    // 2. 元素尺寸和位置
    const rect = element.getBoundingClientRect();
    const computedStyle = window.getComputedStyle(element);
    console.log('2. 元素尺寸和位置:', {
        rect: {
            x: rect.x,
            y: rect.y,
            width: rect.width,
            height: rect.height,
            top: rect.top,
            left: rect.left,
            bottom: rect.bottom,
            right: rect.right
        },
        offset: {
            width: element.offsetWidth,
            height: element.offsetHeight,
            top: element.offsetTop,
            left: element.offsetLeft
        },
        scroll: {
            width: element.scrollWidth,
            height: element.scrollHeight,
            top: element.scrollTop,
            left: element.scrollLeft
        },
        client: {
            width: element.clientWidth,
            height: element.clientHeight
        }
    });
    // 3. 关键样式属性
    console.log('3. 关键样式属性:', {
        display: computedStyle.display,
        visibility: computedStyle.visibility,
        opacity: computedStyle.opacity,
        position: computedStyle.position,
        zIndex: computedStyle.zIndex,
        overflow: computedStyle.overflow,
        transform: computedStyle.transform,
        backgroundColor: computedStyle.backgroundColor,
        color: computedStyle.color
    });
    // 4. 检查子元素
    const children = Array.from(element.children);
    console.log('4. 子元素数量:', children.length);
    children.forEach((child, index) => {
        if (index < 5) { // 只检查前5个子元素
            const childRect = child.getBoundingClientRect();
            const childStyle = window.getComputedStyle(child);
            console.log(`子元素[${index}]:`, {
                tagName: child.tagName,
                className: child.className,
                rect: { width: childRect.width, height: childRect.height },
                display: childStyle.display,
                visibility: childStyle.visibility,
                textContent: child.textContent?.substring(0, 50) + '...'
            });
        }
    });
    // 5. 检查是否在视口内
    const isInViewport = rect.top >= 0 &&
        rect.left >= 0 &&
        rect.bottom <= window.innerHeight &&
        rect.right <= window.innerWidth;
    console.log('5. 视口检查:', {
        isInViewport,
        windowSize: { width: window.innerWidth, height: window.innerHeight },
        elementPosition: { top: rect.top, left: rect.left, bottom: rect.bottom, right: rect.right }
    });
    // 6. 检查二维码图片
    const qrcodeImg = element.querySelector('.qrcode-image');
    if (qrcodeImg) {
        console.log('6. 二维码图片检查:', {
            src: qrcodeImg.src,
            complete: qrcodeImg.complete,
            naturalWidth: qrcodeImg.naturalWidth,
            naturalHeight: qrcodeImg.naturalHeight,
            width: qrcodeImg.width,
            height: qrcodeImg.height,
            crossOrigin: qrcodeImg.crossOrigin
        });
    }
    // 7. 检查文本内容
    const textElements = element.querySelectorAll('.info-item .value');
    console.log('7. 文本内容检查:', Array.from(textElements).map((el, index) => ({
        index,
        textContent: el.textContent,
        innerHTML: el.innerHTML
    })));
    console.log('=== 调试信息结束 ===');
}
/**
 * 简化版本的截图功能 - 用于测试和调试
 */
export async function saveElementAsImageSimple(element, filename = '测试截图') {
    try {
        console.log('=== 开始简化版截图测试 ===');
        // 检查元素基本状态
        const rect = element.getBoundingClientRect();
        console.log('元素基本信息:', {
            tagName: element.tagName,
            className: element.className,
            rect: { width: rect.width, height: rect.height },
            innerHTML: element.innerHTML.substring(0, 100) + '...'
        });
        // 最基础的配置
        const simpleOptions = {
            backgroundColor: '#ffffff',
            scale: 1, // 使用最低倍数
            useCORS: false, // 禁用CORS
            allowTaint: false, // 禁用taint
            logging: true,
            width: Math.min(rect.width, 500),
            height: Math.min(rect.height, 800)
        };
        console.log('使用简化配置:', simpleOptions);
        // 直接截图，不做任何预处理
        const canvas = await html2canvas(element, simpleOptions);
        console.log('简化版Canvas结果:', {
            width: canvas.width,
            height: canvas.height
        });
        // 转换为图片
        const dataURL = canvas.toDataURL('image/png', 0.8);
        console.log('简化版图片数据大小:', dataURL.length);
        if (dataURL.length < 1000) {
            console.error('简化版图片数据太小，可能截图失败');
            return false;
        }
        // 在微信环境中直接显示
        const isWechat = /MicroMessenger/i.test(navigator.userAgent);
        if (isWechat) {
            const newWindow = window.open('', '_blank');
            if (newWindow) {
                newWindow.document.write(`
          <html>
            <head><title>测试截图</title></head>
            <body style="margin:0;padding:20px;background:#f0f0f0;">
              <h3>简化版截图结果</h3>
              <img src="${dataURL}" style="max-width:100%;border:1px solid #ccc;" />
              <br><br>
              <button onclick="window.close()">关闭</button>
            </body>
          </html>
        `);
            }
        }
        console.log('=== 简化版截图测试完成 ===');
        return true;
    }
    catch (error) {
        console.error('简化版截图失败:', error);
        return false;
    }
}
/**
 * 处理长页面滚动截图问题 - 关键修复
 */
async function handleScrollableElement(element) {
    console.log('开始处理长页面滚动截图...');
    // 获取原始滚动位置
    const originalScrollTop = window.scrollY || document.documentElement.scrollTop;
    const originalScrollLeft = window.scrollX || document.documentElement.scrollLeft;
    // 获取元素相对于页面的位置
    const elementRect = element.getBoundingClientRect();
    const elementTop = elementRect.top + originalScrollTop;
    const elementLeft = elementRect.left + originalScrollLeft;
    console.log('滚动位置信息:', {
        原始滚动位置: { top: originalScrollTop, left: originalScrollLeft },
        元素位置: { top: elementTop, left: elementLeft },
        元素尺寸: { width: elementRect.width, height: elementRect.height },
        视口尺寸: { width: window.innerWidth, height: window.innerHeight }
    });
    // 计算需要滚动到的位置，确保元素顶部在视口内
    const targetScrollTop = Math.max(0, elementTop - 20); // 留20px边距
    const targetScrollLeft = Math.max(0, elementLeft - 20);
    // 滚动到元素位置
    console.log(`滚动到目标位置: top=${targetScrollTop}, left=${targetScrollLeft}`);
    window.scrollTo(targetScrollLeft, targetScrollTop);
    // 等待滚动完成
    await new Promise(resolve => setTimeout(resolve, 300));
    // 检查元素是否现在完全可见
    const newRect = element.getBoundingClientRect();
    const isFullyVisible = newRect.top >= 0 &&
        newRect.left >= 0 &&
        newRect.bottom <= window.innerHeight &&
        newRect.right <= window.innerWidth;
    console.log('滚动后元素可见性:', {
        新位置: { top: newRect.top, left: newRect.left, bottom: newRect.bottom, right: newRect.right },
        完全可见: isFullyVisible
    });
    // 如果元素太长，无法完全显示在一个视口内，需要特殊处理
    if (!isFullyVisible && newRect.height > window.innerHeight) {
        console.log('检测到长页面，元素高度超过视口，需要特殊截图处理');
        // 临时调整视口大小的模拟（通过CSS）
        const originalBodyStyle = document.body.style.cssText;
        const originalHtmlStyle = document.documentElement.style.cssText;
        // 临时扩展视口来容纳整个元素
        document.body.style.cssText += `
      min-height: ${newRect.height + 100}px !important;
      overflow: visible !important;
    `;
        document.documentElement.style.cssText += `
      min-height: ${newRect.height + 100}px !important;
      overflow: visible !important;
    `;
        await new Promise(resolve => setTimeout(resolve, 200));
        return () => {
            // 恢复原始滚动位置和样式
            window.scrollTo(originalScrollLeft, originalScrollTop);
            document.body.style.cssText = originalBodyStyle;
            document.documentElement.style.cssText = originalHtmlStyle;
            console.log('已恢复原始滚动位置和样式');
        };
    }
    return () => {
        // 恢复原始滚动位置
        window.scrollTo(originalScrollLeft, originalScrollTop);
        console.log('已恢复原始滚动位置');
    };
}
/**
 * 专门用于报名成功页面的调试保存函数
 * 包含详细的滚动处理和调试信息
 */
export async function saveRegistrationPageDebug(element, filename = '报名凭证调试') {
    console.log('=== 开始报名成功页面调试保存 ===');
    try {
        // 1. 页面和元素基础信息
        console.log('1. 页面基础信息:', {
            页面高度: document.documentElement.scrollHeight,
            视口高度: window.innerHeight,
            当前滚动位置: window.scrollY,
            需要滚动: document.documentElement.scrollHeight > window.innerHeight
        });
        const rect = element.getBoundingClientRect();
        console.log('2. 元素基础信息:', {
            元素高度: rect.height,
            元素宽度: rect.width,
            元素顶部位置: rect.top,
            元素底部位置: rect.bottom,
            是否在视口内: rect.top >= 0 && rect.bottom <= window.innerHeight,
            超出视口多少: rect.bottom - window.innerHeight
        });
        // 2. 检查报名信息完整性
        const registrationInfo = {
            报名编号: element.querySelector('.registration-info-card .info-item .value')?.textContent,
            二维码: element.querySelector('.qrcode-image'),
            提示信息: element.querySelector('.tips-section')?.textContent?.substring(0, 50)
        };
        console.log('3. 报名信息检查:', {
            报名编号: registrationInfo.报名编号,
            二维码存在: !!registrationInfo.二维码,
            二维码已加载: registrationInfo.二维码?.complete && registrationInfo.二维码?.naturalWidth > 0,
            二维码尺寸: registrationInfo.二维码 ? {
                natural: { width: registrationInfo.二维码.naturalWidth, height: registrationInfo.二维码.naturalHeight },
                display: { width: registrationInfo.二维码.width, height: registrationInfo.二维码.height }
            } : null,
            提示信息: registrationInfo.提示信息
        });
        // 3. 详细的滚动处理
        if (rect.height > window.innerHeight || rect.bottom > window.innerHeight) {
            console.log('4. 检测到长页面，需要滚动处理');
            // 记录原始滚动位置
            const originalScrollY = window.scrollY;
            // 滚动到元素顶部
            const targetScrollY = Math.max(0, (rect.top + window.scrollY) - 50);
            console.log(`滚动到目标位置: ${targetScrollY} (原位置: ${originalScrollY})`);
            window.scrollTo(0, targetScrollY);
            await new Promise(resolve => setTimeout(resolve, 500));
            // 检查滚动后的元素位置
            const newRect = element.getBoundingClientRect();
            console.log('滚动后元素位置:', {
                新顶部位置: newRect.top,
                新底部位置: newRect.bottom,
                现在是否完全可见: newRect.top >= 0 && newRect.bottom <= window.innerHeight
            });
        }
        // 4. 使用优化的保存函数
        const success = await saveElementAsImage(element, filename, {
            // 专门针对长页面的配置
            scale: 2,
            useCORS: true,
            allowTaint: true,
            logging: true,
            backgroundColor: '#ffffff',
            // 确保捕获完整高度
            height: Math.max(rect.height, element.scrollHeight, element.offsetHeight),
            width: Math.max(rect.width, element.scrollWidth, element.offsetWidth)
        });
        console.log('5. 保存结果:', success ? '成功' : '失败');
        return success;
    }
    catch (error) {
        console.error('调试保存失败:', error);
        return false;
    }
    finally {
        console.log('=== 报名成功页面调试保存结束 ===');
    }
}
/**
 * 快速测试函数 - 检查元素是否可以正常截图
 */
export async function quickScreenshotTest(element) {
    console.log('=== 快速截图测试 ===');
    try {
        // 最简配置测试
        const canvas = await html2canvas(element, {
            backgroundColor: '#ffffff',
            scale: 1,
            logging: true,
            width: Math.min(element.offsetWidth, 400),
            height: Math.min(element.offsetHeight, 600)
        });
        console.log('测试结果:', {
            Canvas尺寸: { width: canvas.width, height: canvas.height },
            数据大小: canvas.toDataURL().length,
            状态: canvas.width > 0 && canvas.height > 0 ? '成功' : '失败'
        });
        // 在控制台显示缩略图（如果支持）
        if (typeof console.image !== 'undefined') {
            console.image(canvas.toDataURL(), canvas.width * 0.1, canvas.height * 0.1);
        }
    }
    catch (error) {
        console.error('快速测试失败:', error);
    }
}
// 在window对象上暴露调试函数，方便在浏览器控制台调用
if (typeof window !== 'undefined') {
    window.saveImageDebug = {
        saveRegistrationPageDebug,
        quickScreenshotTest,
        saveElementAsImage,
        saveElementAsImageSimple
    };
    console.log('调试函数已暴露到 window.saveImageDebug');
}
