import { ref, onMounted, computed } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import GlobalHeader from '../../components/GlobalHeader.vue'; // 导入通用页头组件
import GlobalFooter from '../../components/GlobalFooter.vue'; // 导入通用页脚组件
import { getHospitalNewsDetailWithRetry } from './api';
import '../../style/common.css'; // 导入公共样式
import './style.css';
const router = useRouter();
const route = useRoute();
const loading = ref(false);
const error = ref('');
const id = ref(null);
// 医院新闻数据
const news = ref({
    id: 0,
    name: '',
    desc: '',
    thumbnail: '',
    content: '',
    create_time: '',
    viewtimes_display: '',
    cat_display: '',
    tags: [],
});
// 计算属性：是否为热门新闻（浏览次数超过100）
const isHotNews = computed(() => {
    const viewCount = parseInt(news.value.viewtimes_display) || 0;
    return viewCount > 100;
});
// 计算属性：是否为最新新闻（7天内发布）
const isRecentNews = computed(() => {
    if (!news.value.create_time)
        return false;
    const publishDate = new Date(news.value.create_time);
    const now = new Date();
    const diffTime = now.getTime() - publishDate.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays <= 7;
});
// 计算属性：显示的标签数组
const displayTags = computed(() => {
    const tags = [];
    // 分类标签
    if (news.value.cat_display) {
        tags.push(news.value.cat_display);
    }
    else {
        tags.push('医院新闻');
    }
    // API返回的标签
    if (news.value.tags && news.value.tags.length > 0) {
        tags.push(...news.value.tags);
    }
    // 动态标签
    if (isHotNews.value) {
        tags.push('热门');
    }
    if (isRecentNews.value) {
        tags.push('最新');
    }
    return tags;
});
// 格式化日期
const formatDate = (dateString) => {
    if (!dateString)
        return '';
    const date = new Date(dateString);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    return `${year}-${month}-${day} ${hours}:${minutes}`;
};
// 获取标签类型（用于不同颜色显示）
const getTagType = (index) => {
    const types = ['default', 'success', 'warning', 'danger'];
    return types[index % types.length];
};
// 获取医院新闻详情
const fetchHospitalDetail = async (newsNewsId) => {
    try {
        loading.value = true;
        const res = await getHospitalNewsDetailWithRetry(newsNewsId);
        console.log('获取到医院新闻详情数据:', res);
        const data = res.hospital_news;
        // 将API返回的数据转换为组件需要的格式
        news.value = {
            id: data.id,
            name: data.name,
            desc: data.desc,
            thumbnail: data.thumbnail,
            content: data.content,
            create_time: data.create_time,
            viewtimes_display: data.viewtimes_display,
            cat_display: data.cat_display || '',
            tags: data.tags || []
        };
    }
    catch (err) {
        console.error('获取医院新闻详情失败:', err);
        error.value = '获取医院新闻详情失败，请稍后再试';
    }
    finally {
        loading.value = false;
    }
};
// 初始化
onMounted(() => {
    id.value = Number(route.params.id);
    console.log('医院新闻详情ID:', id.value);
    fetchHospitalDetail(id.value);
});
debugger; /* PartiallyEnd: #3632/scriptSetup.vue */
const __VLS_ctx = {};
let __VLS_components;
let __VLS_directives;
// CSS variable injection 
// CSS variable injection end 
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ class: "news-detail-page" },
});
/** @type {[typeof GlobalHeader, ]} */ ;
// @ts-ignore
const __VLS_0 = __VLS_asFunctionalComponent(GlobalHeader, new GlobalHeader({
    title: "医院新闻",
}));
const __VLS_1 = __VLS_0({
    title: "医院新闻",
}, ...__VLS_functionalComponentArgsRest(__VLS_0));
if (__VLS_ctx.loading) {
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
        ...{ class: "loading-container" },
    });
    const __VLS_3 = {}.VanLoading;
    /** @type {[typeof __VLS_components.VanLoading, typeof __VLS_components.vanLoading, ]} */ ;
    // @ts-ignore
    const __VLS_4 = __VLS_asFunctionalComponent(__VLS_3, new __VLS_3({
        type: "spinner",
        color: "#1989fa",
    }));
    const __VLS_5 = __VLS_4({
        type: "spinner",
        color: "#1989fa",
    }, ...__VLS_functionalComponentArgsRest(__VLS_4));
    __VLS_asFunctionalElement(__VLS_intrinsicElements.p, __VLS_intrinsicElements.p)({});
}
else if (__VLS_ctx.error) {
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
        ...{ class: "error-container" },
    });
    const __VLS_7 = {}.VanEmpty;
    /** @type {[typeof __VLS_components.VanEmpty, typeof __VLS_components.vanEmpty, typeof __VLS_components.VanEmpty, typeof __VLS_components.vanEmpty, ]} */ ;
    // @ts-ignore
    const __VLS_8 = __VLS_asFunctionalComponent(__VLS_7, new __VLS_7({
        description: "加载失败",
        image: "error",
    }));
    const __VLS_9 = __VLS_8({
        description: "加载失败",
        image: "error",
    }, ...__VLS_functionalComponentArgsRest(__VLS_8));
    __VLS_10.slots.default;
    {
        const { description: __VLS_thisSlot } = __VLS_10.slots;
        __VLS_asFunctionalElement(__VLS_intrinsicElements.p, __VLS_intrinsicElements.p)({});
        (__VLS_ctx.error);
    }
    const __VLS_11 = {}.VanButton;
    /** @type {[typeof __VLS_components.VanButton, typeof __VLS_components.vanButton, typeof __VLS_components.VanButton, typeof __VLS_components.vanButton, ]} */ ;
    // @ts-ignore
    const __VLS_12 = __VLS_asFunctionalComponent(__VLS_11, new __VLS_11({
        ...{ 'onClick': {} },
        round: true,
        type: "primary",
    }));
    const __VLS_13 = __VLS_12({
        ...{ 'onClick': {} },
        round: true,
        type: "primary",
    }, ...__VLS_functionalComponentArgsRest(__VLS_12));
    let __VLS_15;
    let __VLS_16;
    let __VLS_17;
    const __VLS_18 = {
        onClick: (...[$event]) => {
            if (!!(__VLS_ctx.loading))
                return;
            if (!(__VLS_ctx.error))
                return;
            __VLS_ctx.fetchHospitalDetail(__VLS_ctx.news.id);
        }
    };
    __VLS_14.slots.default;
    var __VLS_14;
    var __VLS_10;
}
else {
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
        ...{ class: "content" },
    });
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
        ...{ class: "section-container" },
    });
    __VLS_asFunctionalElement(__VLS_intrinsicElements.h1, __VLS_intrinsicElements.h1)({
        ...{ class: "news-title" },
    });
    (__VLS_ctx.news.name);
    if (__VLS_ctx.displayTags.length > 0) {
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "news-tags" },
        });
        if (__VLS_ctx.news.cat_display) {
            const __VLS_19 = {}.VanTag;
            /** @type {[typeof __VLS_components.VanTag, typeof __VLS_components.vanTag, typeof __VLS_components.VanTag, typeof __VLS_components.vanTag, ]} */ ;
            // @ts-ignore
            const __VLS_20 = __VLS_asFunctionalComponent(__VLS_19, new __VLS_19({
                type: "primary",
                size: "medium",
            }));
            const __VLS_21 = __VLS_20({
                type: "primary",
                size: "medium",
            }, ...__VLS_functionalComponentArgsRest(__VLS_20));
            __VLS_22.slots.default;
            (__VLS_ctx.news.cat_display);
            var __VLS_22;
        }
        else {
            const __VLS_23 = {}.VanTag;
            /** @type {[typeof __VLS_components.VanTag, typeof __VLS_components.vanTag, typeof __VLS_components.VanTag, typeof __VLS_components.vanTag, ]} */ ;
            // @ts-ignore
            const __VLS_24 = __VLS_asFunctionalComponent(__VLS_23, new __VLS_23({
                type: "primary",
                size: "medium",
            }));
            const __VLS_25 = __VLS_24({
                type: "primary",
                size: "medium",
            }, ...__VLS_functionalComponentArgsRest(__VLS_24));
            __VLS_26.slots.default;
            var __VLS_26;
        }
        for (const [tag, index] of __VLS_getVForSourceType((__VLS_ctx.news.tags))) {
            const __VLS_27 = {}.VanTag;
            /** @type {[typeof __VLS_components.VanTag, typeof __VLS_components.vanTag, typeof __VLS_components.VanTag, typeof __VLS_components.vanTag, ]} */ ;
            // @ts-ignore
            const __VLS_28 = __VLS_asFunctionalComponent(__VLS_27, new __VLS_27({
                key: (tag),
                type: (__VLS_ctx.getTagType(index)),
                size: "medium",
            }));
            const __VLS_29 = __VLS_28({
                key: (tag),
                type: (__VLS_ctx.getTagType(index)),
                size: "medium",
            }, ...__VLS_functionalComponentArgsRest(__VLS_28));
            __VLS_30.slots.default;
            (tag);
            var __VLS_30;
        }
        if (__VLS_ctx.isHotNews) {
            const __VLS_31 = {}.VanTag;
            /** @type {[typeof __VLS_components.VanTag, typeof __VLS_components.vanTag, typeof __VLS_components.VanTag, typeof __VLS_components.vanTag, ]} */ ;
            // @ts-ignore
            const __VLS_32 = __VLS_asFunctionalComponent(__VLS_31, new __VLS_31({
                type: "danger",
                size: "medium",
            }));
            const __VLS_33 = __VLS_32({
                type: "danger",
                size: "medium",
            }, ...__VLS_functionalComponentArgsRest(__VLS_32));
            __VLS_34.slots.default;
            var __VLS_34;
        }
        if (__VLS_ctx.isRecentNews) {
            const __VLS_35 = {}.VanTag;
            /** @type {[typeof __VLS_components.VanTag, typeof __VLS_components.vanTag, typeof __VLS_components.VanTag, typeof __VLS_components.vanTag, ]} */ ;
            // @ts-ignore
            const __VLS_36 = __VLS_asFunctionalComponent(__VLS_35, new __VLS_35({
                type: "warning",
                size: "medium",
            }));
            const __VLS_37 = __VLS_36({
                type: "warning",
                size: "medium",
            }, ...__VLS_functionalComponentArgsRest(__VLS_36));
            __VLS_38.slots.default;
            var __VLS_38;
        }
    }
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
        ...{ class: "news-meta" },
    });
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
        ...{ class: "meta-item" },
    });
    const __VLS_39 = {}.VanIcon;
    /** @type {[typeof __VLS_components.VanIcon, typeof __VLS_components.vanIcon, ]} */ ;
    // @ts-ignore
    const __VLS_40 = __VLS_asFunctionalComponent(__VLS_39, new __VLS_39({
        name: "clock-o",
    }));
    const __VLS_41 = __VLS_40({
        name: "clock-o",
    }, ...__VLS_functionalComponentArgsRest(__VLS_40));
    __VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({
        ...{ class: "meta-label" },
    });
    __VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({
        ...{ class: "meta-value" },
    });
    (__VLS_ctx.formatDate(__VLS_ctx.news.create_time));
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
        ...{ class: "meta-item" },
    });
    const __VLS_43 = {}.VanIcon;
    /** @type {[typeof __VLS_components.VanIcon, typeof __VLS_components.vanIcon, ]} */ ;
    // @ts-ignore
    const __VLS_44 = __VLS_asFunctionalComponent(__VLS_43, new __VLS_43({
        name: "eye-o",
    }));
    const __VLS_45 = __VLS_44({
        name: "eye-o",
    }, ...__VLS_functionalComponentArgsRest(__VLS_44));
    __VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({
        ...{ class: "meta-label" },
    });
    __VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({
        ...{ class: "meta-value" },
    });
    (__VLS_ctx.news.viewtimes_display);
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
        ...{ class: "news-image-container" },
    });
    __VLS_asFunctionalElement(__VLS_intrinsicElements.img)({
        src: (__VLS_ctx.news.thumbnail),
        alt: (__VLS_ctx.news.name),
        ...{ class: "news-image card-image-base image-hover-scale" },
    });
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
        ...{ class: "news-summary" },
    });
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
        ...{ class: "summary-header" },
    });
    const __VLS_47 = {}.VanIcon;
    /** @type {[typeof __VLS_components.VanIcon, typeof __VLS_components.vanIcon, ]} */ ;
    // @ts-ignore
    const __VLS_48 = __VLS_asFunctionalComponent(__VLS_47, new __VLS_47({
        name: "info-o",
        color: "#1989fa",
    }));
    const __VLS_49 = __VLS_48({
        name: "info-o",
        color: "#1989fa",
    }, ...__VLS_functionalComponentArgsRest(__VLS_48));
    __VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({});
    __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
        ...{ class: "summary-content" },
    });
    (__VLS_ctx.news.desc);
    if (__VLS_ctx.news.content) {
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "section-container" },
        });
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "section-header" },
        });
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "section-title" },
        });
        const __VLS_51 = {}.VanIcon;
        /** @type {[typeof __VLS_components.VanIcon, typeof __VLS_components.vanIcon, ]} */ ;
        // @ts-ignore
        const __VLS_52 = __VLS_asFunctionalComponent(__VLS_51, new __VLS_51({
            name: "description-o",
            color: "#4b8bf4",
        }));
        const __VLS_53 = __VLS_52({
            name: "description-o",
            color: "#4b8bf4",
        }, ...__VLS_functionalComponentArgsRest(__VLS_52));
        __VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({});
        __VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
            ...{ class: "news-content" },
        });
        __VLS_asFunctionalDirective(__VLS_directives.vHtml)(null, { ...__VLS_directiveBindingRestFields, value: (__VLS_ctx.news.content) }, null, null);
    }
}
const __VLS_55 = {}.VanBackTop;
/** @type {[typeof __VLS_components.VanBackTop, typeof __VLS_components.vanBackTop, ]} */ ;
// @ts-ignore
const __VLS_56 = __VLS_asFunctionalComponent(__VLS_55, new __VLS_55({
    right: "16",
    bottom: "80",
}));
const __VLS_57 = __VLS_56({
    right: "16",
    bottom: "80",
}, ...__VLS_functionalComponentArgsRest(__VLS_56));
/** @type {[typeof GlobalFooter, ]} */ ;
// @ts-ignore
const __VLS_59 = __VLS_asFunctionalComponent(GlobalFooter, new GlobalFooter({}));
const __VLS_60 = __VLS_59({}, ...__VLS_functionalComponentArgsRest(__VLS_59));
/** @type {__VLS_StyleScopedClasses['news-detail-page']} */ ;
/** @type {__VLS_StyleScopedClasses['loading-container']} */ ;
/** @type {__VLS_StyleScopedClasses['error-container']} */ ;
/** @type {__VLS_StyleScopedClasses['content']} */ ;
/** @type {__VLS_StyleScopedClasses['section-container']} */ ;
/** @type {__VLS_StyleScopedClasses['news-title']} */ ;
/** @type {__VLS_StyleScopedClasses['news-tags']} */ ;
/** @type {__VLS_StyleScopedClasses['news-meta']} */ ;
/** @type {__VLS_StyleScopedClasses['meta-item']} */ ;
/** @type {__VLS_StyleScopedClasses['meta-label']} */ ;
/** @type {__VLS_StyleScopedClasses['meta-value']} */ ;
/** @type {__VLS_StyleScopedClasses['meta-item']} */ ;
/** @type {__VLS_StyleScopedClasses['meta-label']} */ ;
/** @type {__VLS_StyleScopedClasses['meta-value']} */ ;
/** @type {__VLS_StyleScopedClasses['news-image-container']} */ ;
/** @type {__VLS_StyleScopedClasses['news-image']} */ ;
/** @type {__VLS_StyleScopedClasses['card-image-base']} */ ;
/** @type {__VLS_StyleScopedClasses['image-hover-scale']} */ ;
/** @type {__VLS_StyleScopedClasses['news-summary']} */ ;
/** @type {__VLS_StyleScopedClasses['summary-header']} */ ;
/** @type {__VLS_StyleScopedClasses['summary-content']} */ ;
/** @type {__VLS_StyleScopedClasses['section-container']} */ ;
/** @type {__VLS_StyleScopedClasses['section-header']} */ ;
/** @type {__VLS_StyleScopedClasses['section-title']} */ ;
/** @type {__VLS_StyleScopedClasses['news-content']} */ ;
var __VLS_dollars;
const __VLS_self = (await import('vue')).defineComponent({
    setup() {
        return {
            GlobalHeader: GlobalHeader,
            GlobalFooter: GlobalFooter,
            loading: loading,
            error: error,
            news: news,
            isHotNews: isHotNews,
            isRecentNews: isRecentNews,
            displayTags: displayTags,
            formatDate: formatDate,
            getTagType: getTagType,
            fetchHospitalDetail: fetchHospitalDetail,
        };
    },
});
export default (await import('vue')).defineComponent({
    setup() {
        return {};
    },
});
; /* PartiallyEnd: #4569/main.vue */
