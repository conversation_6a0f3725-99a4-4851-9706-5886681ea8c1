/* 活动新闻详情页样式 */
.activity-news-detail-page {
  padding: 0 0 20px 0;
  background-color: #f7f8fa;
  min-height: 100vh;
}

/* 自定义导航栏 */
.custom-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  height: 44px;
}

.back-icon, .share-icon {
  font-size: 20px;
  color: #333;
  cursor: pointer;
  transition: color 0.3s ease;
}

.back-icon:hover, .share-icon:hover {
  color: #4b8bf4;
}

.header-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

/* 加载和错误状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 50px 0;
}

.loading-container p {
  margin-top: 10px;
  color: #666;
  font-size: 14px;
}

.error-container {
  padding: 30px 15px;
}

.content {
  padding: 5px;
}

/* 文章标题样式 */
.news-title {
  font-size: 22px;
  font-weight: bold;
  margin: 0 0 16px 0;
  color: #333;
  line-height: 1.4;
  text-align: left;
  word-break: break-word;
}

/* 文章标签样式 */
.news-tags {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
  flex-wrap: wrap;
}

.news-tags .van-tag {
  font-size: 12px;
  border-radius: 16px;
  padding: 6px 12px;
  font-weight: 600;
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.news-tags .van-tag:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.news-tags .van-tag::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s ease;
}

.news-tags .van-tag:hover::before {
  left: 100%;
}

/* 分类标签 - 明艳蓝色 */
.news-tags .van-tag--primary {
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
  border-color: transparent;
  color: #fff;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

/* API标签1 - 明艳紫色 */
.news-tags .van-tag--default {
  background: linear-gradient(135deg, #8e44ad 0%, #9b59b6 100%);
  border-color: transparent;
  color: #fff;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

/* API标签2 - 明艳绿色 */
.news-tags .van-tag--success {
  background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
  border-color: transparent;
  color: #fff;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

/* API标签4/最新标签 - 明艳橙色 */
.news-tags .van-tag--warning {
  background: linear-gradient(135deg, #ff6b35 0%, #ff8c69 100%);
  border-color: transparent;
  color: #fff;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

/* 热门标签 - 明艳红色 */
.news-tags .van-tag--danger {
  background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
  border-color: transparent;
  color: #fff;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  animation: pulse 2s infinite;
}

/* 热门标签脉动动画 */
@keyframes pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 2px 8px rgba(231, 76, 60, 0.3);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 4px 16px rgba(231, 76, 60, 0.5);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 2px 8px rgba(231, 76, 60, 0.3);
  }
}

/* 文章元信息样式 */
.news-meta {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 16px;
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #1989fa;
}

.meta-item {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #666;
}

.meta-item .van-icon {
  margin-right: 6px;
  color: #1989fa;
  font-size: 16px;
}

.meta-label {
  margin-right: 4px;
  color: #666;
}

.meta-value {
  color: #333;
  font-weight: 500;
}

/* 文章封面图容器 */
.news-image-container {
  margin-bottom: 16px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.news-image {
  width: 100%;
  height: 220px;
  object-fit: cover;
  border-radius: 8px;
  transition: transform 0.3s ease;
}

/* 文章摘要样式 */
.news-summary {
  background: linear-gradient(135deg, #f8fbff 0%, #f0f7ff 100%);
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 0;
  border: 1px solid #e1f0ff;
}

.summary-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.summary-content {
  color: #666;
  line-height: 1.6;
  font-size: 15px;
  margin: 0;
  text-align: justify;
}

/* 正文内容样式 */
.news-content {
  color: #333;
  line-height: 1.8;
  font-size: 15px;
}

.news-content img {
  max-width: 100%;
  border-radius: 4px;
  margin: 15px 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.news-content p {
  margin-bottom: 16px;
  text-align: justify;
  text-indent: 2em; /* 段落首行缩进 */
}

.news-content h1,
.news-content h2,
.news-content h3,
.news-content h4,
.news-content h5,
.news-content h6 {
  margin: 24px 0 12px 0;
  color: #333;
  font-weight: 600;
  line-height: 1.3;
}

.news-content h1 {
  font-size: 20px;
  border-bottom: 2px solid #1989fa;
  padding-bottom: 8px;
}

.news-content h2 {
  font-size: 18px;
  color: #1989fa;
}

.news-content h3 {
  font-size: 16px;
}

.news-content ul,
.news-content ol {
  margin: 12px 0;
  padding-left: 24px;
}

.news-content li {
  margin-bottom: 8px;
  line-height: 1.6;
}

.news-content blockquote {
  margin: 16px 0;
  padding: 16px;
  background: linear-gradient(135deg, #f8f9fa 0%, #f1f3f4 100%);
  border-left: 4px solid #1989fa;
  border-radius: 0 8px 8px 0;
  color: #666;
  font-style: italic;
  position: relative;
}

.news-content blockquote::before {
  content: '"';
  position: absolute;
  top: -8px;
  left: 12px;
  font-size: 36px;
  color: #1989fa;
  opacity: 0.3;
}

.news-content table {
  width: 100%;
  border-collapse: collapse;
  margin: 16px 0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.news-content th,
.news-content td {
  border: 1px solid #e0e0e0;
  padding: 12px;
  text-align: left;
}

.news-content th {
  background: linear-gradient(135deg, #f5f7fa 0%, #e8f4f8 100%);
  font-weight: 600;
  color: #333;
}

.news-content td {
  background-color: #fff;
}

.news-content tr:nth-child(even) td {
  background-color: #fafbfc;
}

/* 响应式优化 */
@media (max-width: 480px) {
  .news-title {
    font-size: 20px;
  }
  
  .news-image {
    height: 180px;
  }
  
  .news-content {
    font-size: 14px;
  }
  
  .news-content p {
    text-indent: 1.5em;
  }
  
  .news-meta {
    padding: 10px;
  }
  
  .meta-item {
    font-size: 13px;
  }
  
  .summary-content {
    font-size: 14px;
  }
}

@media (max-width: 320px) {
  .news-title {
    font-size: 18px;
  }
  
  .news-tags .van-tag {
    font-size: 11px;
    padding: 3px 6px;
  }
  
  .news-image {
    height: 160px;
  }
}

/* 底部操作栏 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  padding: 12px 16px;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  display: flex;
  gap: 12px;
  z-index: 1000;
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
}

.action-btn {
  flex: 1;
  height: 44px;
  border-radius: 22px;
  font-weight: 500;
  font-size: 14px;
  transition: all 0.3s ease;
}

.action-btn.secondary {
  background: #f5f5f5;
  color: #666;
  border: none;
}

.action-btn.secondary:hover {
  background: #e8e8e8;
  color: #555;
}

.action-btn.primary {
  background: linear-gradient(135deg, #4b8bf4 0%, #667eea 100%);
  border: none;
  box-shadow: 0 4px 15px rgba(75, 139, 244, 0.3);
  color: white;
}

.action-btn.primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 6px 20px rgba(75, 139, 244, 0.4);
}

/* 底部间距 */
.footer-spacer {
  height: 20px;
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.news-body {
  animation: fadeInUp 0.6s ease-out;
}

.content-section {
  animation: fadeInUp 0.6s ease-out;
  animation-fill-mode: both;
}

.content-section:nth-child(1) { animation-delay: 0.1s; }
.content-section:nth-child(2) { animation-delay: 0.2s; }
.content-section:nth-child(3) { animation-delay: 0.3s; }

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 4px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 2px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
