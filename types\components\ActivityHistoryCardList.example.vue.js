import { ref, onMounted } from 'vue';
import ActivityHistoryCardList from './ActivityHistoryCardList.vue';
const loading = ref(false);
const finished = ref(true);
const infiniteLoading = ref(false);
const infiniteFinished = ref(false);
// 示例数据
const sampleItems = ref([
    {
        id: 1,
        name: '中医养生健康讲座',
        desc: '本次讲座将介绍中医养生的基本理念和实用方法，帮助大家了解如何通过中医理论来保持身体健康。',
        thumbnail: 'https://picsum.photos/300/200?random=1',
        startdate: '2024-01-15',
        enddate: '2024-01-15',
        location: '中医院大礼堂',
        participants: 120,
        cat_display: '健康讲座',
        status: '已结束'
    },
    {
        id: 2,
        name: '针灸技术培训班',
        desc: '面向医务人员的针灸技术专业培训，由资深中医师授课，理论与实践相结合。',
        thumbnail: 'https://picsum.photos/300/200?random=2',
        startdate: '2024-01-20',
        enddate: '2024-01-22',
        location: '培训中心',
        participants: 45,
        cat_display: '专业培训',
        status: '已结束'
    },
    {
        id: 3,
        name: '中药材识别活动',
        desc: '带领市民了解常见中药材的识别方法和功效，增进对中医药文化的了解。',
        thumbnail: 'https://picsum.photos/300/200?random=3',
        startdate: '2024-02-01',
        enddate: '2024-02-01',
        location: '中药园',
        participants: 80,
        cat_display: '科普活动',
        status: '已结束'
    }
]);
const infiniteItems = ref([...sampleItems.value]);
// 处理卡片点击
const handleCardClick = (item) => {
    console.log('点击了活动:', item);
    // 这里可以跳转到活动详情页
    // router.push(`/activity-detail/${item.id}`);
};
// 加载更多数据
const loadMoreItems = () => {
    if (infiniteLoading.value || infiniteFinished.value)
        return;
    infiniteLoading.value = true;
    // 模拟API请求
    setTimeout(() => {
        const newItems = [
            {
                id: infiniteItems.value.length + 1,
                name: `活动 ${infiniteItems.value.length + 1}`,
                desc: '这是一个模拟的活动描述，用于展示无限滚动功能。',
                thumbnail: `https://picsum.photos/300/200?random=${infiniteItems.value.length + 10}`,
                startdate: '2024-02-10',
                enddate: '2024-02-10',
                location: '会议室',
                participants: 60,
                cat_display: '模拟活动',
                status: '已结束'
            },
            {
                id: infiniteItems.value.length + 2,
                name: `活动 ${infiniteItems.value.length + 2}`,
                desc: '这是另一个模拟的活动描述，展示列表的多样性。',
                thumbnail: `https://picsum.photos/300/200?random=${infiniteItems.value.length + 11}`,
                startdate: '2024-02-15',
                enddate: '2024-02-15',
                location: '大厅',
                participants: 90,
                cat_display: '模拟活动',
                status: '已结束'
            }
        ];
        infiniteItems.value.push(...newItems);
        infiniteLoading.value = false;
        // 模拟到达底部
        if (infiniteItems.value.length >= 10) {
            infiniteFinished.value = true;
        }
    }, 1500);
};
onMounted(() => {
    console.log('ActivityHistoryCardList 示例页面已加载');
});
debugger; /* PartiallyEnd: #3632/scriptSetup.vue */
const __VLS_ctx = {};
let __VLS_components;
let __VLS_directives;
/** @type {__VLS_StyleScopedClasses['example-header']} */ ;
/** @type {__VLS_StyleScopedClasses['example-header']} */ ;
/** @type {__VLS_StyleScopedClasses['example-section']} */ ;
/** @type {__VLS_StyleScopedClasses['activity-history-example']} */ ;
/** @type {__VLS_StyleScopedClasses['example-header']} */ ;
/** @type {__VLS_StyleScopedClasses['example-header']} */ ;
/** @type {__VLS_StyleScopedClasses['example-header']} */ ;
/** @type {__VLS_StyleScopedClasses['example-section']} */ ;
/** @type {__VLS_StyleScopedClasses['example-section']} */ ;
// CSS variable injection 
// CSS variable injection end 
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ class: "activity-history-example" },
});
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ class: "example-header" },
});
__VLS_asFunctionalElement(__VLS_intrinsicElements.h2, __VLS_intrinsicElements.h2)({});
__VLS_asFunctionalElement(__VLS_intrinsicElements.p, __VLS_intrinsicElements.p)({});
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ class: "example-container" },
});
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ class: "example-section" },
});
__VLS_asFunctionalElement(__VLS_intrinsicElements.h3, __VLS_intrinsicElements.h3)({});
/** @type {[typeof ActivityHistoryCardList, ]} */ ;
// @ts-ignore
const __VLS_0 = __VLS_asFunctionalComponent(ActivityHistoryCardList, new ActivityHistoryCardList({
    ...{ 'onCardClick': {} },
    items: (__VLS_ctx.sampleItems),
    loading: (__VLS_ctx.loading),
    finished: (__VLS_ctx.finished),
    useInfiniteScroll: (false),
}));
const __VLS_1 = __VLS_0({
    ...{ 'onCardClick': {} },
    items: (__VLS_ctx.sampleItems),
    loading: (__VLS_ctx.loading),
    finished: (__VLS_ctx.finished),
    useInfiniteScroll: (false),
}, ...__VLS_functionalComponentArgsRest(__VLS_0));
let __VLS_3;
let __VLS_4;
let __VLS_5;
const __VLS_6 = {
    onCardClick: (__VLS_ctx.handleCardClick)
};
var __VLS_2;
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ class: "example-section" },
});
__VLS_asFunctionalElement(__VLS_intrinsicElements.h3, __VLS_intrinsicElements.h3)({});
/** @type {[typeof ActivityHistoryCardList, ]} */ ;
// @ts-ignore
const __VLS_7 = __VLS_asFunctionalComponent(ActivityHistoryCardList, new ActivityHistoryCardList({
    ...{ 'onCardClick': {} },
    ...{ 'onLoadMore': {} },
    items: (__VLS_ctx.infiniteItems),
    loading: (__VLS_ctx.infiniteLoading),
    finished: (__VLS_ctx.infiniteFinished),
    useInfiniteScroll: (true),
}));
const __VLS_8 = __VLS_7({
    ...{ 'onCardClick': {} },
    ...{ 'onLoadMore': {} },
    items: (__VLS_ctx.infiniteItems),
    loading: (__VLS_ctx.infiniteLoading),
    finished: (__VLS_ctx.infiniteFinished),
    useInfiniteScroll: (true),
}, ...__VLS_functionalComponentArgsRest(__VLS_7));
let __VLS_10;
let __VLS_11;
let __VLS_12;
const __VLS_13 = {
    onCardClick: (__VLS_ctx.handleCardClick)
};
const __VLS_14 = {
    onLoadMore: (__VLS_ctx.loadMoreItems)
};
var __VLS_9;
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ class: "example-section" },
});
__VLS_asFunctionalElement(__VLS_intrinsicElements.h3, __VLS_intrinsicElements.h3)({});
/** @type {[typeof ActivityHistoryCardList, ]} */ ;
// @ts-ignore
const __VLS_15 = __VLS_asFunctionalComponent(ActivityHistoryCardList, new ActivityHistoryCardList({
    ...{ 'onCardClick': {} },
    items: ([]),
    loading: (false),
    finished: (true),
    emptyText: "暂无活动历史记录",
}));
const __VLS_16 = __VLS_15({
    ...{ 'onCardClick': {} },
    items: ([]),
    loading: (false),
    finished: (true),
    emptyText: "暂无活动历史记录",
}, ...__VLS_functionalComponentArgsRest(__VLS_15));
let __VLS_18;
let __VLS_19;
let __VLS_20;
const __VLS_21 = {
    onCardClick: (__VLS_ctx.handleCardClick)
};
var __VLS_17;
__VLS_asFunctionalElement(__VLS_intrinsicElements.div, __VLS_intrinsicElements.div)({
    ...{ class: "example-section" },
});
__VLS_asFunctionalElement(__VLS_intrinsicElements.h3, __VLS_intrinsicElements.h3)({});
/** @type {[typeof ActivityHistoryCardList, ]} */ ;
// @ts-ignore
const __VLS_22 = __VLS_asFunctionalComponent(ActivityHistoryCardList, new ActivityHistoryCardList({
    ...{ 'onCardClick': {} },
    items: ([]),
    loading: (true),
    finished: (false),
}));
const __VLS_23 = __VLS_22({
    ...{ 'onCardClick': {} },
    items: ([]),
    loading: (true),
    finished: (false),
}, ...__VLS_functionalComponentArgsRest(__VLS_22));
let __VLS_25;
let __VLS_26;
let __VLS_27;
const __VLS_28 = {
    onCardClick: (__VLS_ctx.handleCardClick)
};
var __VLS_24;
/** @type {__VLS_StyleScopedClasses['activity-history-example']} */ ;
/** @type {__VLS_StyleScopedClasses['example-header']} */ ;
/** @type {__VLS_StyleScopedClasses['example-container']} */ ;
/** @type {__VLS_StyleScopedClasses['example-section']} */ ;
/** @type {__VLS_StyleScopedClasses['example-section']} */ ;
/** @type {__VLS_StyleScopedClasses['example-section']} */ ;
/** @type {__VLS_StyleScopedClasses['example-section']} */ ;
var __VLS_dollars;
const __VLS_self = (await import('vue')).defineComponent({
    setup() {
        return {
            ActivityHistoryCardList: ActivityHistoryCardList,
            loading: loading,
            finished: finished,
            infiniteLoading: infiniteLoading,
            infiniteFinished: infiniteFinished,
            sampleItems: sampleItems,
            infiniteItems: infiniteItems,
            handleCardClick: handleCardClick,
            loadMoreItems: loadMoreItems,
        };
    },
});
export default (await import('vue')).defineComponent({
    setup() {
        return {};
    },
});
; /* PartiallyEnd: #4569/main.vue */
