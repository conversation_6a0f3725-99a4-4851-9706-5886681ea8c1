import{d as c,a as i,u as p,r as m,l as _,c as u,o as f,b as a,g,_ as h}from"./index-BE8XLQ59.js";import{G as k,a as v}from"./GlobalHeader-lL88u8sR.js";import{L as x}from"./LeaderDetailCard-VVs8AxZp.js";import{s as t}from"./function-call-BUl5915X.js";import"./index-CAfqjps3.js";import"./index-B27ddgxZ.js";const y={class:"knowledge-detail-page"},b={class:"detail-content"},w=c({__name:"index",setup(L){const o=i(),r=p(),s=m("");_(()=>{const e=Array.isArray(o.params.id)?o.params.id[0]:o.params.id;e?s.value=e:(t("内容ID无效"),r.back())});const n=e=>{console.log("中医知识详情加载成功:",e)},d=e=>{console.error("中医知识详情加载失败:",e),t(e)},l=()=>{console.log("重试加载中医知识详情")};return(e,R)=>(f(),u("div",y,[a(k,{title:"中医知识详情"}),g("div",b,[a(x,{"leader-id":s.value,"api-type":"knowledge",onLoaded:n,onError:d,onRetry:l},null,8,["leader-id"])]),a(v)]))}}),D=h(w,[["__scopeId","data-v-5e798185"]]);export{D as default};
