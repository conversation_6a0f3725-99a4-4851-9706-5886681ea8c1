# Technical Context: TCM H5 Application

## Technology Stack Overview

### Frontend Framework

- **Vue 3.5.16**: Latest stable version with Composition API
- **TypeScript 5.8.3**: Full type safety throughout the application
- **Vant 4.9.19**: Mobile-optimized UI component library specifically designed for Vue
- **Vue Router 4.5.1**: Hash-based routing for deployment flexibility

### Build & Development Tools

- **Vite 6.3.5**: Modern build tool for fast development and optimized production builds
- **@vitejs/plugin-vue 5.2.3**: Official Vue plugin for Vite
- **vue-tsc 2.2.8**: TypeScript compiler for Vue single file components

### Auto-Import & Component Resolution

- **unplugin-auto-import 19.3.0**: Automatic import of Vue APIs and utilities
- **unplugin-vue-components 28.7.0**: Automatic component registration
- **@vant/auto-import-resolver 1.3.0**: Automatic Vant component imports

### HTTP & API Layer

- **Axios 1.9.0**: HTTP client with custom interceptors and retry logic
- **Custom API layer**: Centralized request handling with environment-specific configurations
- **Environment Variables**: Complete configuration externalization with multi-environment support

### UI & Styling

- **Animate.css 4.1.1**: CSS animation library for enhanced user experience
- **Responsive CSS**: Mobile-first design with custom breakpoints
- **Vant CSS**: Component styling from Vant UI library

### Additional Features

- **html2canvas 1.4.1**: Screenshot and image generation capabilities
- **qrcode 1.5.4**: QR code generation for sharing and connectivity
- **vue-qr 4.0.9**: Vue wrapper for QR code functionality

## Environment Configuration System

### Environment Variables

The application now uses a comprehensive environment variable system for configuration:

```bash
# Development Environment (.env.development)
VITE_API_BASE_URL=                    # Empty for proxy usage
VITE_PROXY_TARGET=https://gujiao.sxaliyun.cn  # Development proxy target
VITE_API_TIMEOUT=10000               # Request timeout in milliseconds

# Production Environment (.env.production)
VITE_API_BASE_URL=https://gujiao.sxaliyun.cn  # Production API endpoint
VITE_API_TIMEOUT=10000               # Request timeout in milliseconds

# Test Environment (.env.test)
VITE_API_BASE_URL=https://test-api.zyy.sxaliyun.cn  # Test API endpoint
VITE_API_TIMEOUT=10000               # Request timeout in milliseconds
```

### TypeScript Environment Support

```typescript
// src/vite-env.d.ts
interface ImportMetaEnv {
  readonly VITE_API_BASE_URL: string;
  readonly VITE_PROXY_TARGET: string;
  readonly VITE_API_TIMEOUT: string;
}

interface ImportMeta {
  readonly env: ImportMetaEnv;
}
```

### Dynamic Configuration System

The application features a complete dynamic configuration system for runtime settings:

```typescript
// System Configuration APIs
/api/system/hospital_info/    // Hospital information
/api/system/contact_info/     // Contact details
/api/system/seo_config/       // SEO and metadata

// Configuration Management
- 24-hour localStorage caching
- Silent fallback to defaults
- Vue 3 Composition API integration
- TypeScript type safety
```

## Development Environment Setup

### Prerequisites

- Node.js 18+ (recommended LTS version)
- npm or yarn package manager
- Modern browser with ES6+ support

### Local Development

```bash
# Install dependencies
npm install

# Start development server (uses .env.development)
npm run dev

# Build for production (uses .env.production)
npm run build

# Preview production build
npm run preview
```

### Development Server Configuration

- **Port**: Default Vite port (usually 5173)
- **Proxy**: API requests to `/api/*` proxied to environment-configured target
- **Environment Loading**: Automatic environment variable loading based on NODE_ENV
- **Hot Reload**: Automatic page refresh on file changes
- **TypeScript**: Real-time type checking during development

## Architecture Patterns

### Component Architecture

```
src/
├── components/           # Shared/reusable components
├── views/               # Page-level components
│   ├── Home/
│   ├── Activity/
│   ├── Knowledge/
│   └── [feature]/
├── api/                 # API layer and HTTP utilities
├── composables/         # Vue 3 composables (including system config)
├── router/              # Vue Router configuration
├── types/               # TypeScript type definitions
├── utils/               # Utility functions
└── assets/              # Static assets
```

### API Layer Design

- **Environment-Based Configuration**: Automatic API endpoint selection
- **Multi-Environment Support**: Development, production, and test configurations
- **Request Interceptors**: Automatic token injection and request preprocessing
- **Response Interceptors**: Unified error handling and response processing
- **Retry Logic**: Automatic retry for failed requests with exponential backoff
- **Dynamic Configuration**: Runtime configuration management with caching

### State Management

- **Composition API**: Vue 3 reactive state management
- **Component-level State**: Local state management within components
- **System Configuration**: Centralized configuration state with `useSystemConfig`
- **Prop Drilling**: Parent-child component communication
- **Event Emitters**: Cross-component communication for specific use cases

## API Integration

### Environment-Based API Configuration

```typescript
// src/api/request.ts
const baseURL = import.meta.env.VITE_API_BASE_URL || "";
const timeout = parseInt(import.meta.env.VITE_API_TIMEOUT) || 10000;

const request = axios.create({
  baseURL,
  timeout,
  headers: {
    "Content-Type": "application/json",
  },
});
```

### Vite Proxy Configuration

```typescript
// vite.config.ts
export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd(), "");

  return {
    server: {
      proxy: {
        "/api": {
          target: env.VITE_PROXY_TARGET || "https://houma.sxaliyun.cn",
          changeOrigin: true,
          secure: false,
          rewrite: (path) => path,
        },
      },
    },
  };
});
```

### Dynamic Configuration Integration

```typescript
// System configuration composable
export function useSystemConfig() {
  // Hospital information
  const hospitalInfo = ref<HospitalInfo | null>(null);
  // Contact information
  const contactInfo = ref<ContactInfo | null>(null);
  // SEO configuration
  const seoConfig = ref<SeoConfig | null>(null);

  // 24-hour caching with localStorage
  // Silent fallback to defaults
  // Reactive updates across components
}
```

### API Layer Structure

```typescript
// Centralized request handling
export function get<T>(url: string, params?: any): Promise<T>;
export function post<T>(url: string, data?: any): Promise<T>;

// Module-specific API functions
export function getActivityList(params?: any): Promise<ActivityListResponse>;
export function getActivityDetail(id: string): Promise<ActivityDetailResponse>;

// System configuration APIs
export function getHospitalInfo(): Promise<HospitalInfo>;
export function getContactInfo(): Promise<ContactInfo>;
export function getSeoConfig(): Promise<SeoConfig>;
```

### Error Handling

- **Network Errors**: Automatic retry with exponential backoff
- **API Errors**: Standardized error response handling
- **Configuration Errors**: Silent fallback for system configuration
- **User Feedback**: Toast notifications for user-facing errors
- **Development**: Console logging for debugging

## Deployment Configuration

### Multi-Environment Support

The application supports seamless deployment across different environments:

**Development**:

- Uses Vite proxy for API requests
- Empty baseURL for relative API calls
- Hot reload and debugging tools enabled

**Production**:

- Direct API calls to configured endpoint
- Optimized build with minification
- Production error handling

**Testing**:

- Separate test API endpoint
- Testing-specific configurations
- Isolated test environment settings

### Build Configuration

```typescript
// Environment-specific builds
npm run dev     # Development with .env.development
npm run build   # Production with .env.production
npm run test    # Testing with .env.test
```

### Configuration Template

```bash
# .env.example - Template for deployment
VITE_API_BASE_URL=          # API base URL (empty for development)
VITE_PROXY_TARGET=          # Development proxy target
VITE_API_TIMEOUT=10000      # Request timeout (milliseconds)
```

## Performance Optimization

### Configuration Caching

- **System Configuration**: 24-hour localStorage caching
- **API Responses**: Intelligent caching for frequently accessed data
- **Static Assets**: Browser caching with appropriate headers
- **Component Caching**: Vue keep-alive for expensive components

### Code Splitting

- **Route-based**: Lazy loading for each page component
- **Component-based**: Dynamic imports for large components
- **Vendor Splitting**: Separate chunks for third-party libraries
- **Environment Splitting**: Different builds for different environments

### Asset Optimization

- **Image Optimization**: Responsive images with appropriate formats
- **Bundle Analysis**: Regular bundle size monitoring
- **Tree Shaking**: Elimination of unused code
- **Minification**: Production build optimization

## Mobile Optimization

### Responsive Design

```css
/* Enhanced breakpoint strategy with configuration support */
@media (max-width: 374px) {
  /* Small phones */
}
@media (max-width: 480px) {
  /* Regular phones */
}
@media (max-width: 768px) {
  /* Tablets */
}
@media (min-width: 768px) {
  /* Desktop */
}
```

### Touch Optimization

- **Touch Targets**: Minimum 44px touch targets for accessibility
- **Gesture Support**: Swipe navigation and touch-friendly interactions
- **Performance**: Optimized for various mobile device capabilities
- **Network Awareness**: Adaptive loading based on connection quality

## Security Considerations

### Environment Variable Security

- **Client-side Variables**: Only `VITE_` prefixed variables exposed to client
- **Sensitive Data**: No sensitive information in client-side environment variables
- **Production Security**: Proper server-side configuration for sensitive settings

### API Security

- **HTTPS**: All API communications over HTTPS
- **CORS**: Proper cross-origin resource sharing configuration
- **Request Validation**: Input validation and sanitization
- **Error Handling**: Secure error messages without information leakage

## Infrastructure Status

### Configuration Management ✅

- **Environment Variables**: Complete externalization of configuration
- **Multi-Environment**: Development, production, and test support
- **Type Safety**: Full TypeScript support for configuration
- **Template System**: Deployment template with `.env.example`

### Dynamic Configuration ✅

- **API Integration**: Real-time configuration via REST APIs
- **Caching Strategy**: 24-hour localStorage caching
- **Fallback System**: Silent degradation to hardcoded defaults
- **Component Integration**: Seamless integration with Vue components

### Performance ✅

- **Bundle Optimization**: Tree shaking and code splitting
- **Asset Management**: Optimized static asset handling
- **Caching**: Multi-layer caching strategy
- **Mobile Performance**: Optimized for mobile device constraints

The technical infrastructure is now production-ready with comprehensive configuration management, robust API integration, and flexible deployment capabilities.
